<?php

declare(strict_types=1);

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;
use Common\Traits\DB\QuiteSaveTrait;
use Illuminate\Support\Carbon;

class LeadFormRequest extends Model
{
    use QuiteSaveTrait,
        UuidTrait;

    protected $connection = 'utility';
    protected $table = 'leadFormRequests';
    protected $primaryKey = 'leadFormRequestID';
    protected $fillable = [
        'leadFormRequestID', 'leadFormID', 'apiKey', 'ipAddressType', 'ipAddress',
        'method', 'path', 'requestHeaders', 'requestData', 'responseHeaders',
        'responseData', 'statusCode', 'time', 'createdAt'
    ];
    protected $dates = ['createdAt'];
    protected $dateFormat = 'Y-m-d H:i:s.u';
    public $timestamps = false;


    /**
     * Boot method to generate a UUID (as binary) when creating.
     */
    protected static function boot(): void
    {
        parent::boot();

        static::creating(function (LeadFormRequest $model) {
            if (empty($model->leadFormRequestID)) {
                $model->leadFormRequestID = Uuid::uuid4()->getBytes();
            }

            if (empty($model->createdAt)) {
                $model->createdAt = Carbon::now('UTC');
            }
        });
    }

}