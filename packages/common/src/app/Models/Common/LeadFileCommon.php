<?php

namespace Common\Models\Common;

use Common\Models\Company;
use Common\Models\File;
use Common\Models\Lead;
use Common\Traits\DB\ScopeSearchTrait;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

trait LeadFileCommon
{
    use SoftDeletes;
    use ScopeSearchTrait;
    use UuidTrait;

    protected $castMap = [
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];
    protected $searchColumns = ['name'];

    public function file()
    {
        return $this->belongsTo(File::class, 'fileID', 'fileID');
    }

    public function lead()
    {
        return $this->belongsTo(Lead::class, 'leadID', 'leadID');
    }

    public function scopeWithLead(object $query): object
    {
        return $query->join('leads', 'leads.leadID', '=', "{$this->table}.leadID");
    }

    public function scopeOfCompany(object $query, $company): object
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $this->scopeWithLead($query)->where('leads.companyID', $company);
    }
}
