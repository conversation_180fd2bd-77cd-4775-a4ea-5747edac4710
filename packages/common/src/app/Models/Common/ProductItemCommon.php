<?php

namespace Common\Models\Common;

use Common\Models\Material;
use Common\Models\ProductItemAdditionalCost;
use Common\Models\ProductItemMaterial;
use Common\Models\BidContent;
use Common\Models\Company;
use Common\Models\Interfaces\ProductItemInterface;
use Common\Models\Pivots\BidContentProductItem;
use Common\Models\Pivots\ProductCategoryItem;
use Common\Models\ProductCategory;
use Common\Models\ProductItemMeta;
use Common\Models\ProductItemPrice;
use Common\Models\Unit;
use Common\Traits\DB\ScopeSearchTrait;
use Common\Traits\DB\UuidTrait;

trait ProductItemCommon
{
    use ScopeSearchTrait;
    use UuidTrait;

    protected $castMap = [
        'ownerType' => 'int',
        'ownerID' => 'int',
        'isIntangible' => 'bool',
        'pricingType' => 'int',
        'status' => 'int',
        'archivedByUserID' => 'int',
        'createdByUserID' => 'int',
        'updatedByUserID' => 'int',
        'deletedByUserID' => 'int'
    ];
    protected $searchColumns = ['name', 'description'];

    public function bidContent()
    {
        return $this->belongsToMany(BidContent::class, 'bidContentProductItems', 'productItemID', 'bidContentID')
            ->using(BidContentProductItem::class)
            ->withPivot('bidContentProductItemID', 'createdByUserID', 'updatedByUserID', 'deletedByUserID')
            ->withTimestamps()
            ->whereNull('bidContentProductItems.deletedAt');
    }

    public function categories()
    {
        return $this->belongsToMany(ProductCategory::class, 'productCategoriesItems', 'productItemID', 'productCategoryID')
            ->using(ProductCategoryItem::class)
            ->withPivot('productCategoryItemID', 'createdByUserID', 'updatedByUserID', 'deletedByUserID')
            ->withTimestamps()
            ->whereNull('productCategoriesItems.deletedAt');
    }

    public function materials()
    {
        return $this->hasMany(ProductItemMaterial::class, 'productItemID');
    }

    public function additionalCosts()
    {
        return $this->hasMany(ProductItemAdditionalCost::class, 'productItemID');
    }

    public function meta()
    {
        return $this->hasMany(ProductItemMeta::class, 'productItemID');
    }

    public function nonTieredPrice()
    {
        return $this->hasOne(ProductItemPrice::class, 'productItemID', 'productItemID')
            ->where('minCount', 0)->whereNull('maxCount')->where('status', ProductItemPrice::STATUS_ACTIVE);
    }

    public function owner()
    {
        return $this->morphTo('owner', 'ownerType', 'ownerID');
    }

    public function prices()
    {
        return $this->hasMany(ProductItemPrice::class, 'productItemID');
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class, 'unitID', 'unitID');
    }

    public function scopeActive($query)
    {
        return $query->where("{$this->table}.status", ProductItemInterface::STATUS_ACTIVE);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy("{$this->table}.name", 'asc');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->where(function ($query) use ($company) {
            $query->where("{$this->table}.ownerType", ProductItemInterface::OWNER_COMPANY)
                ->where("{$this->table}.ownerID", $company);
        });
    }
}
