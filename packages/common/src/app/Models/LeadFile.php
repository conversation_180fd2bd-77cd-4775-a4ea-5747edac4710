<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Interfaces\DB\ScopeSearchInterface;
use Common\Models\Common\LeadFileCommon;

class LeadFile extends Model implements ScopeSearchInterface
{
    use LeadFileCommon;

    protected $table = 'leadFiles';
    protected $primaryKey = 'leadFileID';
    protected $fillable = [
        'leadFileID', 'leadID', 'name', 'fileID', 'createdAt', 'createdByUserID', 'updatedAt', 'updatedByUser<PERSON>',
        'deletedAt', 'deletedByUserID'
    ];
}
