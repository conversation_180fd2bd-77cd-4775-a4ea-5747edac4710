<?php

declare(strict_types=1);

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Core\Components\Http\StaticAccessors\URI;

class LeadForm extends Model
{
    use UuidTrait;

    protected $table = 'leadForms';
    protected $primaryKey = 'leadFormID';
    protected $fillable = ['leadFormID', 'token', 'companyID', 'title', 'saveButtonLabel', 'defaultAssignedToUserID',
        'isSendNotifications', 'additionalEmailRecipients', 'googleTagID', 'isActive'];

    protected $dates = ['apiKeyCreatedAt'];
    protected $dateFormat = 'Y-m-d H:i:s.u';

    const REFERENCE_MARKETING_SOURCE = 'marketing_source';
    const REFERENCE_PROJECT_TYPE = 'project_type';

    /**
     * Convert the model instance to a customized array structure dedicated to render
     *
     * @return array
     */
    public function toRenderArray()
    {
        return [
            'form_url' => URI::route('lead-forms-action.ingest')->build(),
            'form_files_url' => URI::route('lead-forms-action.ingest-files')->build(),
            'token' => $this->token,
            'company_id' => $this->companyID,
            'title' => $this->title,
            'save_button_label' => $this->saveButtonLabel,
            'is_active' => $this->isActive,
            'google_tag_id' => $this->googleTagID,
            'is_send_notifications' => $this->isSendNotifications,
            'fields' => $this->fields->map(function ($field) {
                return [
                    'reference' => $field->reference,
                    'field_type' => $field->fieldType,
                    'label' => $field->label,
                    'is_enabled' => $field->isEnabled,
                    'is_required' => $field->isRequired,
                ];
            })->toArray(),
        ];
    }

    public function getMarketingSourceField()
    {
        return $this->fields->where('reference', LeadForm::REFERENCE_MARKETING_SOURCE)->first();
    }

    public function getProjectTypeField()
    {
        return $this->fields->where('reference', LeadForm::REFERENCE_PROJECT_TYPE)->first();
    }

    /**
     * Relationship with Company
     */
    public function company()
    {
        return $this->belongsTo(Company::class, 'companyID');
    }

    public function fields()
    {
        return $this->hasMany(LeadFormField::class, 'leadFormID');
    }

    public function requests()
    {
        return $this->hasMany(LeadFormRequest::class, 'leadFormID', 'leadFormID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->where("{$this->table}.companyID", $company);
    }

}