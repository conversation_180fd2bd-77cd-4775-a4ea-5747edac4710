<?php

declare(strict_types=1);

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;

class LeadFormField extends Model
{
    use UuidTrait;

    protected $table = 'leadFormFields';
    protected $primaryKey = 'leadFormFieldID';
    protected $fillable = ['leadFormID', 'reference', 'fieldType', 'label', 'instruction', 'isEnabled', 'isRequired'];


    public function leadForm()
    {
        return $this->belongsTo(LeadForm::class, 'leadFormID', 'leadFormID');
    }

    public function scopeOfCompany($query, $company)
    {
        if (is_object($company) && $company instanceof Company) {
            $company = $company->getKey();
        }
        return $query->whereHas('leadForm', function ($q) use ($company) {
            $q->where('companyID', $company);
        });
    }
}