<?php

declare(strict_types=1);

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;

class Export extends Model
{
    use UuidTrait;

    public const TYPE_CUSTOMER = 1;
    public const TYPE_PROPERTY = 2;
    public const TYPE_PROJECT = 3;
    public const TYPE_BID_LINE_ITEMS = 4;
    public const TYPE_PRODUCT = 5;
    public const TYPE_TASK = 6;
    public const TYPE_LEAD = 7;
    public const TYPE_MATERIAL = 8;
    public const TYPE_ADDITIONAL_COST = 9;
    public const TYPE_FINANCING_PREQUAL = 10;
    public const TYPE_FINANCING_TRANSACTIONS = 11;
    public const TYPE_FINANCING_OPPORTUNITIES = 12;
    public const TYPE_PROJECT_EVENTS = 13;

    protected $table = 'exports';
    protected $primaryKey = 'exportID';
    protected $fillable = [
        'exportID', 'type', 'scope', 'totalRows', 'time', 'createdAt', 'createdByUserID'
    ];
    protected $casts = [
        'type' => 'int',
        'scope' => 'array',
        'totalRows' => 'int',
        'time' => 'int',
        'createdByUserID' => 'int'
    ];
    protected $dates = ['createdAt'];

    public $timestamps = false;
}
