<?php

namespace Common\Models;

use Common\Classes\DB\HistoryEntityModel;
use Common\Interfaces\DB\ScopeSearchInterface;

class Lead extends HistoryEntityModel implements Interfaces\LeadInterface, ScopeSearchInterface
{
    use Common\LeadCommon;

    protected $table = 'leads';
    protected $primaryKey = 'leadID';
    protected $fillable = [
        'leadUUID', 'companyID', 'status', 'isSentNotifications', 'origin', 'isUnsubscribed', 'priority', 'projectTypeID',
        'marketingTypeID', 'assignedToUserID', 'email', 'businessName', 'firstName', 'lastName', 'address', 'address2',
        'city', 'state', 'zip', 'phoneNumber', 'notes', 'workingNotes', 'deadNotes', 'workingAt', 'workingByUserID',
        'convertedAt', 'convertedByUserID', 'deadAt', 'deadByUserID', 'createdAt', 'createdByUserID', 'updatedAt',
        'updatedByUser<PERSON>', 'deletedAt', 'deletedByUser<PERSON>'
    ];
}
