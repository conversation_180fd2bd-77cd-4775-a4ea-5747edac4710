<?php

namespace Common\Models\Interfaces;

/**
 * Interface EmailTemplateInterface
 *
 * @package Common\Models\Interfaces
 */
interface EmailTemplateInterface
{
    const OWNER_MANUFACTURER = 4;
    const OWNER_COMPANY = 5;

    const STATUS_ACTIVE = 1;
    const STATUS_ARCHIVED = 2;

    const SOURCE_SYSTEM = 1;
    const SOURCE_CUSTOM = 2;

    const TYPE_NEW_CUSTOMER = 1;
    const TYPE_SALES_APPOINTMENT = 2;
    const TYPE_SALES_APPOINTMENT_REMINDER = 3;
    const TYPE_CUSTOMER_BID = 4;
    const TYPE_BID_ACCEPTED = 5;
    const TYPE_BID_REJECTED = 6;
    const TYPE_INSTALLATION_APPOINTMENT = 7;
    const TYPE_INSTALLATION_APPOINTMENT_REMINDER = 8;
    const TYPE_WARRANTIES = 9;
    const TYPE_INVOICE = 10;
    const TYPE_BID_FOLLOW_UP = 11;
    const TYPE_NEW_LEAD = 12;
    const TYPE_INVOICE_PAID = 13;

    public function owner();

    public function scopeActive($query);

    public function scopeOrdered($query);

    public function scopeOfCompany($query, $company);
}
