<?php

namespace Common\Models\Interfaces;

/**
 * Interface FileInterface
 *
 * @package Common\Models\Interfaces
 */
interface FileInterface
{
    const TYPE_DRAWING = 1;
    const TYPE_REPAIR_PLAN = 2;
    const TYPE_USER_IMAGE = 3;
    const TYPE_COMPANY_LOGO = 4;
    const TYPE_FORM_UPLOAD = 5;
    const TYPE_BID = 6;
    const TYPE_SCOPE_OF_WORK = 7;
    const TYPE_BID_CUSTOM_DRAWING = 8;
    const TYPE_MEDIA = 9;
    const TYPE_INVOICE_STATEMENT = 10;
    const TYPE_PROPERTY_IMAGE = 11;
    const TYPE_PROJECT_FILE = 12;
    const TYPE_CUSTOM_REPORT_RESULT = 13;
    const TYPE_LEAD_FILE = 14;

    const STATUS_IN_PROGRESS = 1;
    const STATUS_FINISHED = 2;
    const STATUS_FAILED = 3;

    public function variants();

    public function scopeOfCompany($query, $company);
}
