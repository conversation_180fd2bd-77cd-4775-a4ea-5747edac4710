<?php

namespace Common\Models\Interfaces;

/**
 * Interface ProjectScheduleInterface
 *
 * @package Common\Models\Interfaces
 */
interface ProjectScheduleInterface
{
    const STATUS_ACTIVE = 1;
    const STATUS_CANCELLED = 2;
    const STATUS_REPLACED = 3;
    const STATUS_COMPLETED = 4;

    const SOURCE_CUSTOMER_ADD = 1;
    const SOURCE_PROPERTY_ADD = 2;
    const SOURCE_PROJECT_ADD = 3;
    const SOURCE_PROJECT_RESCHEDULE = 4;
    const SOURCE_CALENDAR_RESCHEDULE = 5;
    const SOURCE_PROJECT_SCHEDULE = 6;

    const TYPE_EVALUATION = 'Evaluation';
    const TYPE_INSTALLATION = 'Installation';

    public function project();

    public function scheduledUser();

    public function scopeWithProperty($query);

    public function scopeOfCompany($query, $company);

    public function scopeOfUser($query, $user);

    public function tasks();
}
