<?php

namespace Common\Models;

use Common\Classes\DB\Model;
use Common\Traits\DB\UuidTrait;
use Illuminate\Database\Eloquent\SoftDeletes;

class Notification extends Model
{
    use SoftDeletes;
    use UuidTrait;

    const TYPE_PROJECT_EVENT_CREATED = 1;
    const TYPE_PROJECT_EVENT_REMINDER = 6;
    const TYPE_CUSTOMER_CREATED = 2;
    const TYPE_EVALUATION_VIEW = 3;
    const TYPE_USER_PASSWORD_RESET = 4;
    const TYPE_BID_ITEM_VIEW = 5;
    const TYPE_EMAIL_MESSAGE_RECIPIENT_BOUNCE = 6;
    const TYPE_USER_ACCESS_LINK = 7;
    const TYPE_BID_FOLLOW_UP_NOTIFICATION_1 = 8;
    const TYPE_BID_FOLLOW_UP_NOTIFICATION_2 = 9;
    const TYPE_BID_FOLLOW_UP_NOTIFICATION_3 = 10;
    const TYPE_BID_FOLLOW_UP_NOTIFICATION_4 = 11;
    const TYPE_BID_FOLLOW_UP_NOTIFICATION_5 = 12;

    const TYPE_TASK_ASSIGNMENT_NOTIFICATION = 13;
//    const TYPE_TASK_DUE_NOTIFICATION = 14;
    const TYPE_LEAD_ASSIGNMENT_NOTIFICATION = 15;
    const TYPE_PROJECT_ASSIGNMENT_NOTIFICATION = 16;
    const TYPE_USER_APPOINTMENT_SCHEDULED_NOTIFICATION = 17;
    const TYPE_USER_BID_VIEWED_NOTIFICATION = 18;
    const TYPE_USER_BID_ACCEPTED_NOTIFICATION = 19;
    const TYPE_USER_BID_REJECTED_NOTIFICATION = 20;

    const TYPE_BID_FOLLOW_UP_NOTIFICATION_6 = 21;
    const TYPE_BID_FOLLOW_UP_NOTIFICATION_7 = 22;
    const TYPE_BID_FOLLOW_UP_NOTIFICATION_8 = 23;
    const TYPE_BID_FOLLOW_UP_NOTIFICATION_9 = 24;
    const TYPE_BID_FOLLOW_UP_NOTIFICATION_10 = 25;
    const TYPE_LEAD_CREATED = 26;

    protected $table = 'notifications';
    protected $primaryKey = 'notificationID';
    protected $fillable = ['notificationID', 'type', 'itemID'];

    public function distributions()
    {
        return $this->hasMany(NotificationDistribution::class, 'notificationID');
    }
}
