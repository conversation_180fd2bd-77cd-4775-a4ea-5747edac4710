<?php

use function Core\Functions\env;

$connections = [
    'app' => [
        'host'     => env('DB_HOST'),
        'database' => env('DB_NAME'),
        'username' => env('DB_USER'),
        'password' => env('DB_PASS')
    ],
    'app-migration' => [
        'host'     => env('DB_HOST'),
        'database' => env('DB_NAME'),
        'username' => env('DB_MIGRATION_USER'),
        'password' => env('DB_MIGRATION_PASS')
    ],
    'import' => [
        'host'     => env('DB_IMPORT_HOST'),
        'database' => env('DB_IMPORT_NAME'),
        'username' => env('DB_IMPORT_USER'),
        'password' => env('DB_IMPORT_PASS')
    ],
    'import-migration' => [
        'host'     => env('DB_IMPORT_HOST'),
        'database' => env('DB_IMPORT_NAME'),
        'username' => env('DB_IMPORT_MIGRATION_USER'),
        'password' => env('DB_IMPORT_MIGRATION_PASS')
    ],
    'utility' => [
        'host'     => env('DB_UTILITY_HOST'),
        'database' => env('DB_UTILITY_NAME'),
        'username' => env('DB_UTILITY_USER'),
        'password' => env('DB_UTILITY_PASS')
    ],
    'utility-migration' => [
        'host'     => env('DB_UTILITY_HOST'),
        'database' => env('DB_UTILITY_NAME'),
        'username' => env('DB_UTILITY_MIGRATION_USER'),
        'password' => env('DB_UTILITY_MIGRATION_PASS')
    ]
];

return [
    'debug' => env('DB_DEBUG', false),
    'default' => 'app',
    'connections' => array_map(function ($connection) {
        return array_merge($connection, [
            'driver'    => 'mysql',
            'charset'   => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix'    => ''
        ]);
    }, $connections),
    'redis' => [
        'client' => 'phpredis',
        'cluster' => false,
        'cache' => [
            'host' => env('REDIS_CACHE_HOST'),
            'port' => env('REDIS_CACHE_PORT', 6379),
            'database' => env('REDIS_CACHE_DATABASE', 0)
        ],
        'queue' => [
            'host' => env('REDIS_QUEUE_HOST'),
            'port' => env('REDIS_QUEUE_PORT', 6379),
            'database' => env('REDIS_QUEUE_DATABASE', 0)
        ]
    ]
];
