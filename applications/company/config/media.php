<?php

return [
    'resource_map' => [
        'company-logos' => [\App\Resources\CompanyResource::class, 'logo'],
        'drawing-images' => [\App\Resources\DrawingResource::class, 'image'],
        'repair-plans' => [\App\Resources\DrawingResource::class, 'repair_plan'],
        'user-images' => [\App\Resources\UserResource::class, 'image'],
        'form-files' => [\App\Resources\Form\Item\Entry\Group\FieldFileResource::class, 'file'],
        'bids' => [\App\Resources\Bid\ItemResource::class, 'file'],
        'scope-of-work' => [\App\Resources\Bid\ItemResource::class, 'scope_of_work'],
        'bid-custom-drawings' => [\App\Resources\Bid\Item\CustomDrawingResource::class, 'file'],
        'company-media' => [\App\Resources\MediaResource::class, 'file'],
        'invoice-statements' => [\App\Resources\Company\InvoiceResource::class, 'statement_file'],
        'property-images' => [\App\Resources\PropertyResource::class, 'image'],
        'property-street-view-images' => [\App\Resources\PropertyResource::class, 'street_view_image'],
        'project-overview' => [\App\Resources\ProjectResource::class, 'project_overview'],
        'project-files' => [\App\Resources\Project\FileResource::class, 'file'],
        'lead-files' => [\App\Resources\Lead\FileResource::class, 'file'],
        'system-form-item-images' => [\App\Resources\System\Form\ItemResource::class, 'image'],
        'custom-report-files' => [\App\Resources\Company\CustomReport\ResultResource::class, 'file']
    ]
];
