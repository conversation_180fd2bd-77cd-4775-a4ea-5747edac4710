'use strict';

import 'parsleyjs';
import event_mixin from '@cac-js/mixins/event';

/**
 * Wrapper for parsleyjs
 */
class Validator {
    /**
     * Constructor
     *
     * @param {jQuery} form
     * @param {object} fields
     * @param {object} config
     */
    constructor(form, fields, config = {}) {
        this.elem = {form, input: {}};
        let names = Object.keys(fields);
        for (let name of names) {
            this.elem.input[name] = form.fxFind(name);
        }
        this.state = {
            field: {},
            field_error_idx: {},
            field_errors: {}
        };

        this.state.instance = form.parsley(
            Object.assign({
                errorsContainer: function (field) {
                    let element = field.$element.data('fx_field') || field.$element;
                    return element.parent();
                },
                errorsWrapper: '<div class="f-f-errors"></div>',
                errorTemplate: '<div class="f-fe-error"></div>',
            },
            // Override extra configs
            config.excluded !== 'undefined' ? { excluded: config.excluded } : {}
            ))
            .on('form:submit', () => {
                this.emit('submit');
                return false;
            })
            .on('form:validate', () => {
                if (config.validate_event !== 'undefined' && config.validate_event) {
                    this.emit('validate');
                }
                return false;
            })
            .on('form:error', () => {
                if (config.error_event !== 'undefined' && config.error_event) {
                    this.emit('error');
                }
                return false;
            });

        for (let name of names) {
            let config = fields[name];
            if (config.required && config.requiredMessage === undefined) {
                config.requiredMessage = 'Required';
            }
            this.state.field[name] = this.elem.input[name].parsley(config);
        }
    };

    /**
     * Get input elem instance
     *
     * @param {string} name
     * @returns {jQuery|undefined}
     */
    getInputElem(name) {
        return this.elem.input[name];
    };

    /**
     * Get parsleyjs field instance
     *
     * @param {string} name
     * @returns {*}
     */
    getField(name) {
        return this.state.field[name];
    };

    /**
     * Set error message on specific field
     *
     * @param {string} name
     * @param {string} message
     * @returns {boolean|number}
     */
    setFieldError(name, message) {
        if (this.state.field[name] === undefined) {
            return false;
        }
        if (this.state.field_error_idx[name] === undefined) {
            this.state.field_error_idx[name] = 0;
        }
        let idx = ++this.state.field_error_idx[name];
        this.state.field[name].addError(`ca-error-${idx}`, {message});
        if (this.state.field_errors[name] === undefined) {
            this.state.field_errors[name] = [];
        }
        this.state.field_errors[name].push(idx);
        return idx;
    };

    /**
     * Clear specific error for field identified by index returned from setFieldError() method
     *
     * @param {string} name
     * @param {number} idx
     * @returns {boolean}
     */
    clearFieldError(name, idx) {
        if (this.state.field_errors[name] === undefined || this.state.field_errors[name].indexOf(idx) === -1) {
            return false;
        }
        this.state.field[name].removeError(`ca-error-${idx}`);
        this.state.field_errors[name] = this.state.field_errors[name].filter(existing_idx => existing_idx !== idx);
        return true;
    };

    /**
     * Clear all errors for field
     *
     * @param {string} name
     */
    clearFieldErrors(name) {
        if (this.state.field_errors[name] === undefined || this.state.field_errors[name].length === 0) {
            return;
        }
        for (let idx of this.state.field_errors[name]) {
            this.state.field[name].removeError(`ca-error-${idx}`);
        }
        this.state.field_errors[name] = [];
        this.state.field_error_idx[name] = 0;
    };

    /**
     * Set errors for field from object
     *
     * Keys should be field names, value should be message.
     *
     * @param {object} errors
     */
    setFieldErrors(errors) {
        for (let name of Object.keys(errors)) {
            if (Array.isArray(errors[name])) {
                for (let message of errors[name]) {
                    this.setFieldError(name, message);
                }
                continue;
            }
            this.setFieldError(name, errors[name]);
        }
    };

    /**
     * Clear all errors for each field
     */
    clearAllFieldErrors() {
        for (let name of Object.keys(this.state.field)) {
            this.clearFieldErrors(name);
        }
    };

    /**
     * Check if the form has any validation errors
     *
     * @returns {boolean} - true if form has errors, false otherwise
     */
    hasErrors() {
        return !this.state.instance.isValid();
    }

    /**
     * Reset form completely
     */
    reset() {
        this.elem.form[0].reset();
        this.state.instance.reset();
        this.clearAllFieldErrors();
    };
}

event_mixin(Validator);

/**
 * Initialize form element using Parsley
 *
 * Sets up proper error wrappers and error container detection
 *
 * @param {jQuery} form - form jQuery element
 * @returns {*}
 */
export function init(form) {
    return form.parsley({
        errorsContainer: function (field) {
            let element = field.$element.data('fx_field') || field.$element;
            return element.parent();
        },
        errorsWrapper: '<div class="f-f-errors"></div>',
        errorTemplate: '<div class="f-fe-error"></div>'
    });
}

/**
 * Factory method to create validator instance
 *
 * @param {jQuery} form
 * @param {object} fields
 * @param {object} config
 * @returns {Validator}
 */
export function create(form, fields, config = {}) {
    return new Validator(form, fields, config);
}

export default {init, create};
