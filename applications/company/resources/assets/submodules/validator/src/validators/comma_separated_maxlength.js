'use strict';

const $ = require('jquery');
const Pa<PERSON><PERSON> = require('parsleyjs');

Parsley.addValidator('commaSeparatedMaxlength', {
    requirementType: 'integer',
    validateString: function (value, maxLength) {
        if (!value || !value.trim()) {
            return true;
        }

        let categories = value.trim().split(',');
        let invalidCategories = [];

        for (let i = 0; i < categories.length; i++) {
            let trimmedCategory = categories[i].trim();

            if (trimmedCategory === '') {
                continue;
            }

            if (trimmedCategory.length > maxLength) {
                let displayName = trimmedCategory.length > 30
                    ? trimmedCategory.substring(0, 10) + '...'
                    : trimmedCategory;
                invalidCategories.push(`"${displayName}" (${trimmedCategory.length} chars)`);
            }
        }

        if (invalidCategories.length > 0) {
            let errorMessage = `The following ${invalidCategories.length === 1 ? 'category exceeds' : 'categories exceed'} the ${maxLength} character limit: ${invalidCategories.join(', ')}`;
            return $.Deferred().reject(errorMessage);
        }

        return true;
    },
    messages: {
        en: 'Each category must not exceed %s characters.'
    }
});
