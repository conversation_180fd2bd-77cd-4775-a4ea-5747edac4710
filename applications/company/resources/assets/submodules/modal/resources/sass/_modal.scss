@use '~@cac-sass/base';
@use '~@cas-flash-message-sass/flash-message';

.reveal-overlay {
    -webkit-backdrop-filter: blur(0.5rem);
    backdrop-filter: blur(0.5rem);
    padding: base.unit-rem-calc(24px);
    @include base.respond-to('<small') {
        padding: 0;
    }
}
.s-modal {
    display: flex;
    align-self: center;
    flex-direction: column;
    max-height: calc(70dvh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
    min-height: unset;
    height: unset;
    padding: 0;
    border-radius: base.unit-rem-calc(12px);
    border: none;
    box-shadow: base.$elevation-level-1;
    outline: none;
    float: left;
    left: 50% !important;
    top:50% !important;
    transform: translate(-50%, -50%);
    @media (max-height: 40rem) {
        max-height: 100% !important;
    }
    @include base.respond-to('<small') {
        min-height: 100%;
        height: 100%;
        left: unset !important;
        top: unset !important;
        transform: unset;
    }
    &.tiny {
        width: base.unit-rem-calc(528px);
        @include base.respond-to('<small') {
            width: 100%;
            max-height: 100% !important;
            border-radius: 0;
            box-shadow: none;
            padding: 0;
        }
    }
    &.large {
        width: base.unit-rem-calc(928px);
        @include base.respond-to('<976px') {
            width: 100%;
        }
        @include base.respond-to('<small') {
            border-radius: 0;
            box-shadow: none;
        }
    }
    @include base.respond-to('<small') {
        border-radius: 0;
        box-shadow: none;
    }
    .i-m-messages {
        .i-fm-message {
            margin: base.unit-rem-calc(8px) base.unit-rem-calc(8px) 0;
        }
    }
    .i-m-header {
        display: flex;
        padding: base.unit-rem-calc(12px);
        border-bottom: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
    }
        .i-mh-text {
            flex: 1;
            align-self: center;
            @include base.typo-header($size: 24px, $line-height: base.unit-rem-calc(32px));
            color: base.$color-grey-dark-4;
            padding-left: base.unit-rem-calc(4px);
            margin-bottom: 0;
            @include base.respond-to('<small') {
                @include base.typo-header($size: 20px, $line-height: base.unit-rem-calc(32px));
            }
        }
        .i-mh-close {
            display: flex;
            justify-content: center;
            align-items: center;
            width: base.unit-rem-calc(32px);
            height: base.unit-rem-calc(32px);
            border-radius: base.unit-rem-calc(32px);
            &:hover {
                border: base.unit-rem-calc(1px) base.$color-grey-light-3 solid;
            }
        }
            .i-mhc-icon {
                @include base.svg-icon('default-24');
                color: base.$color-grey-light-2;
            }
    .i-m-content {
        display: flex;
        flex-direction: column;
        gap: base.unit-rem-calc(16px);
        padding: base.unit-rem-calc(24px);
        height: 100%;
        overflow: scroll;

        p {
            margin: 0;
        }
        @include base.respond-to('<small'){
            height: calc(100% - 114px);
            padding: base.unit-rem-calc(24px) base.unit-rem-calc(16px);
        }
    }

    .i-m-footer {
        display: flex;
        justify-content: flex-end;
        padding: base.unit-rem-calc(12px);
        border-top: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
    }
        .i-mf-action-wrapper {
            display: flex;
            align-items: center;
            gap: base.unit-rem-calc(16px);
            justify-content: flex-end;
            @include base.respond-to('<small') {
                width: 100%;
            }
        }
            .i-mfaw-working {
                display: none;
                width: base.unit-rem-calc(24px);
                height: base.unit-rem-calc(24px);
                padding: base.unit-rem-calc(8px);
                background: url('~@cac-public/images/loading_blue.svg') no-repeat center;
                background-size: base.unit-rem-calc(24px) base.unit-rem-calc(24px);
                &.t-show {
                    display: flex;
                }
            }
            .i-mfaw-actions {
                display: flex;
                justify-content: flex-end;
                gap: base.unit-rem-calc(16px);
                width: 100%;
            }
                .i-mfawa-action {
                    @include base.respond-to('<small') {
                        &.t-primary {
                            padding: 0 base.unit-rem-calc(24px);
                        }
                    }
                }
}

.t-notification .i-m-content {
    overflow: auto;
}

.notification-modal {
    .c-nm-date {
        @include base.typo-paragraph-small;
        font-style: italic;
        color: base.$color-grey-dark-1;
        display: block;
        margin-bottom: 0.75rem;
    }
}