'use strict';

import $ from 'jquery';
import Uppy from '@uppy/core';
import Dashboard from '@uppy/dashboard';
import StatusBar from '@uppy/status-bar';
import XHRUpload from '@uppy/xhr-upload';
import Webcam from '@uppy/webcam';
import debounce from 'lodash/debounce';
import unique from 'lodash/uniq';

import {BatchRequest as Request} from '@ca-package/api';

import '../../utils/fancybox_buttons';

import {Field} from './base';
import {File as FileEntity} from '../../file';
import {Types as LayoutTypes} from '../layout/constants';

import file_tpl from '@cas-form-tpl/fields/file.hbs';
import file_preview_tpl from '@cas-form-tpl/fields/file/preview.hbs';

/**
 * @memberof module:Form/Group/Field
 */
export class File extends Field {
    /**
     * Constructor
     *
     * @param {module:Form.Group} parent - Parent group
     * @param {Object} data
     * @param {Object} entry
     * @param {Array} entry.files - Existing files
     */
    constructor(parent, data, entry) {
        super(parent, data, entry);
        Object.assign(this.state, {
            save_type: 'form-item-entry-group-field-file',
            allowed_validators: ['min_count', 'max_count'],
            queue: {
                is_sending: false,
                pending: [],
                send: debounce(() => this.processQueue(), 750)
            },
            files: new Map,
            upload_errors: [],
            lightbox_items: null
        });
        if (Array.isArray(entry.files)) {
            let value = [];
            for (let file of entry.files) {
                let $file = new FileEntity(this, file);
                this.addFile($file, false);
                value.push($file.file_id);
            }
            if (value.length > 0) {
                this.state.value = value;
            }
        }
    };

    /**
     * Validate value
     *
     * If validation passes, then set value of field and run appropriate updates
     *
     * @param {array|null} value
     * @param {boolean} [update=true]
     * @param {boolean} [rules=true]
     */
    validate(value, update = true, rules = true) {
        // if array, remove any duplicate file ids to prevent errors with entries server side. shouldn't happen but
        // done to prevent errors which impact the customer.
        if (Array.isArray(value)) {
            value = unique(value);
        }
        super.validate(value, update, rules);
    };

    /**
     * Get API request payload
     *
     * @returns {Object}
     */
    getPayload() {
        let payload = super.getPayload();
        payload.file_ids = this.value === null ? [] : this.value;
        return payload;
    };

    /**
     * Get entry data
     *
     * @returns {object}
     */
    getEntry() {
        let entry = super.getEntry();
        entry.files = [];
        if (this.state.value !== null) {
            for (let file_id of this.state.value) {
                entry.files.push({file_id});
            }
        }
        return entry;
    }

    /**
     * Get API request
     *
     * @returns {Request.Single}
     */
    getSaveRequest() {
        return new Request.Single(this.state.save_type, 'sync', this.getPayload());
    };

    /**
     * Add newly uploaded file id to queue to be saved to this field
     *
     * Uppy sends the file to the server to be saved and we return a file id. This file id is in turn saved to the
     * field. A queue based system is used to help with large volumes of uploads.
     *
     * @param {string} file_id - UUID
     */
    enqueueFile(file_id) {
        this.state.queue.pending.push(file_id);
        this.state.queue.send();
    };

    /**
     * Process any pending file ids in queue
     */
    processQueue() {
        if (this.state.queue.is_sending || this.state.queue.pending.length === 0) {
            return;
        }
        this.state.queue.is_sending = true;
        let curr_file_ids = this.value || [],
            file_ids = [...curr_file_ids, ...this.state.queue.pending];
        this.state.queue.pending = [];
        this.validate(file_ids);
    };

    /**
     * Add file
     *
     * @param {module:Form.File} file
     * @param {boolean} [with_preview=true] - Determines if we are loading item initially or adding after boot with preview
     */
    addFile(file, with_preview = true) {
        file.on('destroy', () => {
            this.destroyFile(file.id);
        });
        let $file = {
            file: file
        };
        this.state.files.set(file.id, $file);
        // if not adding preview, then we emit a loaded event instead of added since
        this.form.events.emit(with_preview ? 'file-added' : 'file-loaded', file);
        if (with_preview) {
            this.renderPreviews();
        }
    };

    /**
     * Show fancybox lightbox with all available files
     *
     * Designed to show the selected file represented with the passed file id in the proper order in the lightbox
     *
     * @param {string} file_id - File uuid
     */
    showLightbox(file_id) {
        if (this.state.lightbox_items === null) {
            let items = [];
            let index = 0;
            this.state.files.forEach((info) => {
                info.lightbox_index = index;
                items.push({
                    src: info.file.url,
                    opts: {
                        caption: info.file.name,
                        thumb: info.file.thumbnail_url,
                        fx_delete_handler: (data) => {
                            data.fb_instance.close();
                            info.file.startDelete(true);
                        }
                    }
                });
                index++;
            });
            this.state.lightbox_items = items;
        }
        let item = this.state.files.get(file_id);
        $.fancybox.open(this.state.lightbox_items, {
            buttons: [
                'thumbs',
                'delete',
                'close'
            ],
            thumbs: {
                autoStart: true
            }
        }, item.lightbox_index);
    };

    /**
     * Render preview
     *
     * If a preview element doesn't exist, one is created. A loading image will be shown until the thumbnail is
     * loaded. If overflow is passed, then the extra data is rendered with the preview item.
     *
     * @param {Object} file - File info
     * @param {?number} [overflow=null]
     */
    renderPreview(file, overflow = null) {
        if (file.elem === undefined) {
            file.elem = {
                root: $(file_preview_tpl({
                    id: file.file.id
                }))
            };
            file.elem.loading = file.elem.root.fxChildren('loading');
            let image = new Image;
            image.style.display = 'none';
            image.onload = () => {
                image.style.display = 'block';
                file.elem.loading.hide();
            };
            image.src = file.file.thumbnail_url;
            image.alt = file.file.name;
            file.elem.root.append(image);
            file.elem.overflow = file.elem.root.fxChildren('overflow');
        }
        if (overflow !== null) {
            file.elem.overflow.text(`+${overflow}`).addClass('t-show');
            if (overflow > 9) {
                file.elem.overflow.addClass('t-large');
            }
        } else {
            file.elem.overflow.removeClass('t-show');
        }
        this.elem.files.append(file.elem.root);
    };

    /**
     * Render previews for a files filling the available space
     */
    renderPreviews() {
        // reset lightbox items so the list is pulled again since it probably changed if we are rendering previews
        this.state.lightbox_items = null;
        this.elem.files.empty();
        let width = this.elem.files.width(),
            preview_width = 40,
            available_slots = Math.floor(width / preview_width);
        // if width is less than one slot, then we just force it to 1 since we don't want it to be empty
        if (available_slots === 0) {
            available_slots = 1;
        }
        let overflow = this.state.files.size > available_slots ? this.state.files.size - available_slots : 0,
            count = this.state.files.size < available_slots ? this.state.files.size : available_slots,
            files = this.state.files.values();
        for (let c = 1; c <= count; c++) {
            let file = files.next().value;
            this.renderPreview(file, c === count && overflow > 0 ? overflow : null);
        }
    };

    /**
     * Delete a file by sending a batch request and removing file from dom if successful
     *
     * @param {string} id - File uuid
     * @returns {Promise}
     */
    deleteFile(id) {
        return new Promise((resolve, reject) => {
            let request = new Request.Single(this.state.save_type, 'delete', {
                id: id
            });
            request.promise.then(() => {
                let file = this.state.files.get(id);
                let value = this.value.slice();
                let index = value.indexOf(file.file.file_id);
                if (index !== -1) {
                    value.splice(index, 1);
                    if (value.length === 0) {
                        value = null;
                    }
                    this.validate(value, false, false);
                }
                resolve();
            }, (error) => {
                console.log(error);
                reject();
            });
            this.state.parent.saveField(request);
        });
    };

    /**
     * Remove file from dom, but don't send batch request
     *
     * This is used in cases where the deletion happened server-side and the files just need to be removed from the dom
     *
     * @param id
     */
    destroyFile(id) {
        let file = this.state.files.get(id);
        if (file.elem !== undefined) {
            file.elem.root.remove();
        }
        this.state.files.delete(id);
        if (this.isBooted()) {
            this.renderPreviews();
        }
    };

    /**
     * Add upload error from Uppy
     *
     * @param {string} id - File id
     * @param {string} file_name - File name
     */
    addUploadError(id, file_name) {
        this.state.upload_errors.push(id);
        this.addError(`upload_error_${id}`, `Upload failed for file: ${file_name}`);
    };

    /**
     * Remove upload error by file id
     *
     * @param {string} id - File id
     */
    removeUploadError(id) {
        let index = this.state.upload_errors.indexOf(id);
        if (index !== -1) {
            this.state.upload_errors.splice(index, 1);
        }
        this.removeError(`upload_error_${id}`);
    };

    /**
     * Clear all upload errors
     */
    clearUploadErrors() {
        for (let file_id of this.state.upload_errors) {
            this.removeUploadError(file_id);
        }
    };

    /**
     * Determines if the field has any incomplete uploads
     *
     * @returns {boolean}
     */
    hasIncompleteUploads() {
        if (this.state.queue.is_sending || this.state.queue.pending.length > 0) {
            return true;
        }
        let files = this.state.uppy.getState().files;
        for (let id in files) {
            let file = files[id];
            if (
                file.progress.uploadComplete &&
                !file.progress.preprocess &&
                !file.progress.postprocess &&
                !file.error
            ) {
                continue;
            }
            return true;
        }
        return false;
    };

    /**
     * Initialize Uppy, add plugins, and bind events
     *
     * @returns {{uppy: Uppy.Uppy, dashboard: Uppy.Plugin}}
     */
    initUppy() {
        let auto_proceed = this.config('auto_proceed', true),
            restrictions = {},
            validation = this.config('validation', {});
        if (validation.max_size !== undefined) {
            restrictions.maxFileSize = validation.max_size;
        }
        if (validation.allowed_types !== undefined) {
            restrictions.allowedFileTypes = validation.allowed_types;
        }
        let uppy = Uppy({
                id: `${this.state.parent.entry.id}-${this.state.id}`,
                // autoProceed: auto_proceed,
                restrictions: restrictions,
                onBeforeUpload: (files) => {
                    let valid = true;
                    for (let file_id in files) {
                        let file = files[file_id];
                        if (file.meta && file.meta.name) {
                            if (file.meta.name.length > 150) {
                                uppy.info(`'${file.name}' name exceeds 150 characters.`, 'error', 5000);
                                valid = false;
                            }
                        }
                        if (file.meta && file.meta.description) {
                            if (file.meta.description.length > 250) {
                                uppy.info(`'${file.name}' description exceeds 250 characters.`, 'error', 5000);
                                valid = false;
                            }
                        }
                    }
                    return valid;
                }
            }),
            dashboard;
        // configure plugins
        uppy.use(Dashboard, {
            showLinkToFileUploadResult: false,
            closeModalOnClickOutside: true,
            hideProgressAfterFinish: true,
            proudlyDisplayPoweredByUppy: false,
            metaFields: [
                {id: 'name', name: 'Name', placeholder: 'File name'},
                {id: 'description', name: 'Description', placeholder: 'Image Description'},
            ],
            onRequestCloseModal: () => {
                dashboard.closeModal();
                let files = Object.assign({}, uppy.getState().files);
                if (Object.keys(files).length === 0) {
                    uppy.reset();
                }
            }
        })
            .use(XHRUpload, {
                endpoint: this.form.config('upload_api_endpoint'),
                method: 'PUT',
                fieldName: 'file'
            })
            .use(Webcam, {
                target: Dashboard,
                modes: ['picture'],
                facingMode: 'environment',
                mirror: false
            })
            .use(StatusBar, {
                id: `${this.id}-status-bar`,
                target: this.elem.status_bar[0]
            });
        // configure events
        dashboard = uppy.getPlugin('Dashboard');
        uppy.on('upload-success', (file, resp) => {
            this.enqueueFile(resp.body.file_id);
        })
            // @todo use before upload hook check internal count to see if upload is allowed
            .on('upload-retry', (file_id) => {
                this.removeUploadError(file_id);
            })
            .on('retry-all', () => {
                this.clearUploadErrors();
            })
            .on('cancel-all', () => {
                this.clearUploadErrors();
            })
            .on('file-removed', (file) => {
                this.removeUploadError(file.id);
            })
            .on('upload-error', (file) => {
                this.addUploadError(file.id, file.name);
            })
            .on('complete', () => {
                setTimeout(() => {
                    let close_modal = true;
                    let files = uppy.getState().files;
                    for (let id in files) {
                        let file = files[id];
                        let is_processing = file.progress.preprocess || file.progress.postprocess;
                        if (file.progress.uploadComplete && !is_processing && !file.error) {
                            uppy.removeFile(file.id);
                            continue;
                        }
                        close_modal = false;
                    }
                    if (dashboard.isModalOpen() && close_modal) {
                        dashboard.closeModal();
                    }
                }, 1000);
            });
        return {uppy, dashboard};
    };

    /**
     * Boot file field
     */
    async boot() {
        await super.boot();
        this.elem.button = this.elem.root.fxFind('upload-button');

        if (!this.form.config('preview_mode', false)) {
            this.elem.files = this.elem.root.fxFind('file-list');
            this.elem.status_bar = this.elem.root.fxFind('status-bar');

            // @todo calculate min and max values based on current file counts
            // @todo disable button if max count is satisfied

            let {uppy, dashboard} = this.initUppy();

            this.state.uppy = uppy;
            this.elem.button.fxClick((e) => {
                e.preventDefault();
                // @todo disable button if min and max count validations have been met
                dashboard.openModal();
            });

            this.on('save', (event) => {
                event.request.scope({
                    format: 'form-client-v1'
                });
                event.request.promise
                    .then((result) => {
                        let response = result.response.parsedData(),
                            ids = Object.keys(response.create);
                        if (ids.length > 0) {
                            for (let id of ids) {
                                this.addFile(new FileEntity(this, response.create[id]));
                            }
                        }
                    }, (error) => {
                        console.log(error);
                    })
                    .always(() => {
                        // continue queue if possible since this request is finished
                        this.state.queue.is_sending = false;
                        this.state.queue.send();
                    });
            });

            this.form.on('resize', () => {
                this.renderPreviews();
            });

            this.renderPreviews();

            const that = this;
            this.elem.files.fxClickWatcher('item', function (e) {
                e.preventDefault();
                that.showLightbox($(this).data('id'));
                return false;
            });
        } else {
            this.elem.button.prop('disabled', true);
        }

        this.state.booted = true;
    };

    /**
     * Render file field
     *
     * @param {number} layout_type
     * @returns {string}
     */
    render(layout_type) {
        super.render();
        let classes = [];
        switch (layout_type) {
            case LayoutTypes.INPUT_TABLE_ROW:
                classes.push('t-layout-table-row');
                break;
        }
        return file_tpl({
            path: this.path(),
            classes: classes,
            label: this.state.label
        });
    };

    /**
     * Destroy file field, make sure all related files are destroyed (so anywhere else they exist like the sidebar can
     * remove them as well)
     */
    destroy() {
        this.state.files.forEach((file) => {
            file.file.destroy();
        });
        super.destroy();
    }
}
