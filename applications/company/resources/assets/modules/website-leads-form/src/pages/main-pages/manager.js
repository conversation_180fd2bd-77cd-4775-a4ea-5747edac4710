'use strict';

import LeadFormBuilder from "@cam-company-profile-js/pages/main-pages/settings-pages/leads-pages/utilities/lead-form-builder";

import $ from 'jquery';
import Page from '@ca-package/router/src/page';
const {findChild, jsSelector} = require("@ca-package/dom");
const FormValidator = require("@cas-validator-js");

import manager_tpl from '@cam-website-leads-form-tpl/pages/main-pages/manager.hbs';
import feedback_tpl from '@cam-website-leads-form-tpl/pages/main-pages/feedback.hbs';
import {MAX_LENGTH, SMALL_LENGTH, MEDIUM_LENGTH} from "@cam-company-profile-js/pages/main-pages/settings-pages/leads-pages/utilities/constants";
import {FIELD_NAMES} from "@cam-company-profile-js/pages/main-pages/settings-pages/leads-pages/utilities/constants";
import LeadFormHelper from "@cam-company-profile-js/pages/main-pages/settings-pages/leads-pages/utilities/lead-form-helper";

export class Manager extends Page {

    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);

        Object.assign(this.state, {
            parent: parent,
            website_data: window.website_leads_form_data,
            company_colors: window.website_leads_form_data ? window.website_leads_form_data.company_colors : {},
            validator: null,
        });
        this.helper = null
    };


    /**
     * Load form
     *
     * @returns {Promise<void>}
     */
    async loadForm () {
        const { website_data } = this.state;
        const form_props = Object.assign({}, website_data.lead_form, {
            token: website_data.lead_form.token,
            fields: website_data.lead_form.fields,
            captcha_key: website_data.captcha_key,
        });

       for (let item of website_data.lead_form.fields) {
           if (item.reference === 'email_checkbox') {
               this.state.email_checkbox = item;
               break;
           }
       }

        const lead_form_builder = new LeadFormBuilder({
            form_props: form_props,
            marketing_types: website_data.marketing_types,
            project_types: website_data.project_types,
        });

        const lead = await lead_form_builder.buildForm()
        this.elem.container.html(lead);

        this.setupValidator(website_data.lead_form);
        this.setupListeners();
    }

    /**
     * Setup form validation dynamically based on form fields
     *
     * @param {Object} leadForm - The lead form data from the server
     */
    setupValidator(leadForm) {
        if (!Array.isArray(leadForm.fields)) {
            console.error('leadForm.fields is not an array:', leadForm.fields);
            return;
        }

        const validationRules = leadForm.fields.reduce((rules, field) => {
            if (field.is_enabled) {
                switch (field.reference) {
                    case 'first_name':
                    case 'last_name':
                        rules[field.reference] = {
                            required: true,
                            requiredMessage: 'This field is required',
                            maxlength: MEDIUM_LENGTH,
                        };
                        break;
                    case 'email':
                        rules[field.reference] = {
                            required: !!field.is_required,
                            pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$',
                            patternMessage: 'Please enter a valid email address',
                            requiredMessage: 'This field is required',
                            maxlength: MEDIUM_LENGTH,
                        };
                        break;
                    case 'phone':
                        rules[field.reference] = {
                            required: !!field.is_required,
                            pattern: '^\\d{10,15}$',
                            patternMessage: 'Please enter a valid phone number (10-15 digits)',
                            requiredMessage: 'This field is required',
                        };
                        break;
                    case 'address':
                        rules[field.reference] = {
                            required: !!field.is_required,
                            maxlength: MEDIUM_LENGTH,
                            requiredMessage: 'This field is required',
                        };

                        rules[FIELD_NAMES.CITY] = {
                            required: !!field.is_required,
                            pattern: '^[A-Za-z\\s]+$',
                            patternMessage: 'Please enter a valid city name (letters and spaces only)',
                            requiredMessage: 'This field is required',
                            maxlength: SMALL_LENGTH,
                        }

                        rules[FIELD_NAMES.STATE] = {
                            required: !!field.is_required,
                            requiredMessage: 'This field is required',
                        }

                        rules[FIELD_NAMES.POSTAL_CODE] = {
                            required: !!field.is_required,
                            pattern: '^\\d{5,9}$',
                            patternMessage: 'Please enter a valid postal code (5-9 digits)',
                            requiredMessage: 'This field is required',
                            title: 'Please enter a valid postal code (5-9 digits)',
                        }

                        break;
                    case 'marketing_source':
                    case 'project_type':
                    case 'customer_notes':
                        rules[field.reference] = {
                            required: !!field.is_required,
                            maxlength: MAX_LENGTH,
                            requiredMessage: 'This field is required',
                        };
                        break;
                    case 'upload_file':
                        rules[field.reference] = {
                            required: !!field.is_required,
                        };
                        break;
                    default:
                        console.warn(`No validation rule defined for field reference: ${field.reference}`);
                        break;
                }
            }

            return rules;
        }, {});

        this.elem.form = findChild(this.elem.root, jsSelector('ca-lead-form'));
        this.elem.feedback_container = findChild(this.elem.root, jsSelector('feedback-container'));

        if (!this.elem.form.length) {
            console.error('Form element not found:', this.elem.root);
            return;
        }

        this.state.validator = FormValidator.create(this.elem.form, validationRules, {
            validate_event: true,
            error_event: true,
            excluded: ''
        })
            .on('submit', () => {
                if (!this.state.validator.hasErrors()) {
                    const event = new CustomEvent('trigger_recaptcha_execution');
                    document.dispatchEvent(event)
                }
            })
            .on('error', () => {});

        const instance = this.elem.form.parsley();
    }

    setupListeners() {
        this.helper.initializeFormComponents();
        this.helper.setupSubmitButtonCompanyColors(this.state.company_colors);
        this.helper.setupAppointmentButtonColors(this.state.company_colors)
        this.helper.setupUploaderColors(this.state.company_colors);
    }


    async save() {
        let success = false;
        const getUppy = () => window.__lead_form_uppy;
        const uploader = window.__lead_uploader;
        const uppy = getUppy();

        try {
            this.showLoader();
            const form_data = new FormData();
            this.elem.form
                .serializeArray()
                .forEach(({ name, value }) => form_data.append(name, value));

            // if the email checkbox field doesn't exist or company hasn't enabled it we need to set it to null so that
            // it gets handled properly in the Lead Form Service
            if (this.state.email_checkbox === undefined || !this.state.email_checkbox.is_enabled) {
                form_data.append('email_checkbox', 'null');
            } else {
                // if the email checkbox hasn't been set in the form data it means the user didn't select it and we
                // need to set it to no so that it gets handled properly in the Lead Form Service
                if (form_data.get('email_checkbox') === null) {
                    form_data.append('email_checkbox', 'no');
                }
            }

            const action_url = this.elem.form.attr('action');
            const resp = await fetch(action_url, { method: 'POST', body: form_data });

            if (!resp.ok) {
                alert('There was a problem submitting the form. Please try again.');
                return;
            }
            const { lead_id } = await resp.json();
            if (!lead_id) {
                console.error('Lead id not returned by server'); return;
            }

            await this._uploadFiles(lead_id, form_data.get('token'));
            this._resetValidatorAndForm();
            success = true;
        } catch (e) {
            console.error(e);
        } finally {
            this.elem.feedback_container.html(feedback_tpl({ success}));
            uploader.removeXHRUpload(uppy);
            this.hideLoader();
        }
    }

    async _uploadFiles(leadId, token) {
        const getUppy = () => window.__lead_form_uppy;
        const uploader = window.__lead_uploader;
        const uppy = getUppy();

        if (!uppy) return;
        if (!uppy.getPlugin('XHRUpload')) {
            uploader.addXHRUpload(uppy);
        }
        await uploader.manualUppyUpload(uppy, leadId, token);
        uppy.reset();
    };

    _resetValidatorAndForm() {
        this.state.validator.reset();

        // reset appointment time slots
        $('.appointment-request-section .time-slots').each(function() {
            // within each row:
            const $slots = $(this).find('.time-slot');
            $slots.each(function(i) {
                const $label = $(this);
                const $radio = $label.find('input[type=radio]');

                if (i === 0) {
                    $label.addClass('active');
                    $radio.prop('checked', true);
                } else {
                    $label.removeClass('active');
                    $radio.prop('checked', false);
                }
            });
        });

        // reset the three date inputs
        $('.js-date-input').each(function(i, el) {
            const d = new Date();
            d.setDate(d.getDate() + i + 1);
            const iso = d.toISOString().slice(0, 10);
            $(el).val(iso).trigger('change');
        });
    };

    /**
     * Show loader overlay
     *
     * @param {boolean} [show=true]
     */
    showLoader(show = true) {
        this.elem.loader.toggle(show);
    };

    /**
     * Hide loader overlay
     */
    hideLoader() {
        return this.showLoader(false);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.loader = findChild(root, jsSelector('loader'));
        this.elem.container = findChild(root, jsSelector('form-container'));
        this.elem.root = root
        this.helper = new LeadFormHelper(root, () => {});
    };

    async load() {
        await this.loadForm()
        document.addEventListener('recaptcha_token_set', (event) => this.save());

        $(document).ready(function() {
            let intervalId = setInterval(function() {
                let $recaptchaBadge = $('.grecaptcha-badge');
                if ($recaptchaBadge.length) {
                    $recaptchaBadge.css('visibility', 'hidden');
                    clearInterval(intervalId);
                }
            }, 50);
        });
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return manager_tpl({});
    };
}