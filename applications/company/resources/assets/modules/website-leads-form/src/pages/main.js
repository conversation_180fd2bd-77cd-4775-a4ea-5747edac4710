'use strict';

import Page from '@ca-package/router/src/page';

import main_tpl from '@cam-website-leads-form-tpl/pages/main.hbs';
import {Manager} from "@cam-website-leads-form-js/pages/main-pages/manager";

export class MainPage extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
    };


    /**
     * Get child routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            manager: {
                path: '{token}',
                bindings: { token: 'uuid' },
                page: Manager,
            }
        };
    };

    /**
     * Set layout
     *
     * @param {module:Layout.Controller} instance
     */
    set layout(instance) {
        this.state.layout = instance;
    };

    /**
     * Get layout
     *
     * @readonly
     *
     * @returns {module:Layout.Controller}
     */
    get layout() {
        return this.state.layout;
    };

    /**
     * Get container for child pages to be rendered
     *
     * @returns {jQuery}
     */
    getPageContainer() {
        return this.elem.pages;
    };


    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.pages = this.elem.root.fxFind('pages');
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return main_tpl({});
    };
}