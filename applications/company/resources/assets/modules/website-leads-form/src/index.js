/**
 * @module Website leads form
 */

'use strict';

import Router from '@ca-package/router';

import {MainPage} from './pages/main';

/**
 * Main controller for Website leads form
 *
 * @memberof module:Website Leads Form
 */
export class Controller {
    /**
     * Website Leads form constructor
     *
     * @param {jQuery} root - jQuery element that contains the custom report module
     *
     */
    constructor(root) {
        this.elem = {root};
        this.state = {};
        this.boot();
    };

    /**
     * Boot module
     */
    boot() {
        this.state.router = new Router(MainPage, {
            base_path: '/website-leads-form',

        });
        this.state.router.boot(this.elem.root);
    };
}
