@use '~@cac-sass/base';

:root {
  --lform-hover-bg:      #{base.$color-primary-light-2};
  --lform-hover-border:  #{base.$color-primary-light-2};
  --lform-hover-text:    #{base.$color-primary-dark-1};
}

%preview-container {
  border: 2px dashed base.$color-primary-light-2;
  border-radius: base.unit-rem-calc(8px);
  background-color: #F7FBFF;
  padding: base.unit-rem-calc(12px);
  max-width: 510px;
  margin-bottom: 1.5em;
}

%ca-form-wrapper {
  margin-top: base.unit-rem-calc(16px);
  padding: 0 base.unit-rem-calc(16px);
  width: 100% !important;

  .customer-notes-section { margin-bottom: base.unit-rem-calc(8px); }

  .email-checkbox-section .form-group-email {
    display: flex;
    gap: base.unit-rem-calc(8px);
    cursor: pointer;
    margin-top: base.unit-rem-calc(8px);
  }

  .form-section-hr {
    border-color: base.$color-grey-light-3;
  }

  .upload-files-section { @extend %upload-files-section; }

  .c-ci-image {
    display: flex;
    flex-direction: column;
    background-color: base.$color-background-form;
    border: 1px solid base.$color-grey-light-4;
    border-radius: base.unit-rem-calc(8px);
    padding: 0 base.unit-rem-calc(12px);
    width: 100%;
  }

  .file-item {
    display: flex; justify-content: space-between; gap: base.unit-rem-calc(8px);
    background-color: base.$color-background-form;

    .thumb-icon,
    .thumb-icon svg {
      width: base.unit-rem-calc(16px); height: base.unit-rem-calc(16px);
      color: base.$color-grey-default;
    }

    .content-section {
      display: flex; gap: base.unit-rem-calc(8px);
      width: 100%; background-color: base.$color-background-form; color: base.$color-grey-dark-4;
      padding: base.unit-rem-calc(6px) base.unit-rem-calc(2px);
      align-items: center;
    }

    .delete-section {
      display: flex; align-items: center;
      svg {
        color: base.$color-red-default!important;
      }
    }
  }

  .appointment-request-section { @extend %appointment-request-section; }
  .form-actions { @extend %form-actions; }
  .form-footer  { @extend %form-footer; }

  /* grid helpers */
  .two-columns       { @extend %two-columns; }
  .three-columns     { @extend %three-columns; }

  .f-f-label         { @extend %f-f-label; }
  .form-group.f-field{ @extend %f-f-field; }
}


/* Re-usable sub-blocks                                        */
/* ────────────────────────────────────────────────────────────── */

%upload-files-section {
  display: flex;
  border: 2px dashed var(--lform-accent-bg, #{base.$color-primary-light-3});
  border-radius: base.unit-rem-calc(12px);
  margin: base.unit-rem-calc(16px) 0;
  color: base.$color-grey-light-3;
  width: 100%;

  .form-group { width: 100%; .t-hidden { display: none; } }

  .c-ci-upload-title-section {
    display: flex; justify-content: center;
    label {
      cursor: pointer;
      padding: base.unit-rem-calc(18px);
      width: 100%;
      text-align: center;
    }
  }
}

%appointment-request-section {
  .f-field-label {
    @include base.typo-paragraph;
    color: base.$color-grey-dark-2;
    font-weight: 500;
    margin-bottom: base.unit-rem-calc(4px);
  }

  .f-section-title {
    font-size: 1.25rem;
    font-weight: 500;
    display: flex; align-items: baseline; gap: .25em;

    .optional { font-size: base.unit-rem-calc(11.2px); font-weight: 500; }
  }

  .option-slot {
    display: grid;
    grid-template-columns: 1fr 3fr;
    gap: base.unit-rem-calc(16px);
    margin-bottom: base.unit-rem-calc(16px);

    .f-fidt-input {
      -webkit-text-fill-color: var(--lform-accent-bg, #{base.$color-primary-default});
      background-color: #fff !important;
      border: 1px solid base.$color-grey-light-4 !important;
    }

    .time-slots {
      display: flex;
      gap: base.unit-rem-calc(3px);
      cursor: pointer;
      align-items: center;

      .time-slot {
        @include base.typo-paragraph;
        border-radius: base.unit-rem-calc(24px);
        font-size: base.unit-rem-calc(13px);
        font-weight: 500;
        color: base.$color-grey-dark-1;
        padding: base.unit-rem-calc(6px) base.unit-rem-calc(10px);
        cursor: pointer;

        &.active {
          background-color: var(--lform-accent-bg, #{base.$color-primary-default});
          border-color:     var(--lform-accent-border, #{base.$color-primary-default});
          color:            var(--lform-accent-text, #fff);
        }

        input[type="radio"] {
          appearance: none;
          -webkit-appearance: none;
          -moz-appearance: none;
          background: transparent;
          border: none;
          padding: 0;
          margin: 0;
        }
      }
    }
  }
}

%form-actions {
  margin-top: base.unit-rem-calc(16px);
  display: flex; flex-direction: column;

  .feedback-container {
    width: 100%; text-align: center;
    .form-alert-success { @include base.callout-success; }
    .form-alert-error   { @include base.callout-error;  }
  }

  .wrapper-actions {
    display: flex; flex-direction: row-reverse;
    margin-top: base.unit-rem-calc(8px);
  }
}

%form-footer {
  @include base.typo-paragraph-small;
  color: base.$color-grey-dark-1; text-align: center;
  margin: base.unit-rem-calc(16px) 0;
}

%two-columns   { @include base.respond-to('>small')  { display:grid; grid-template-columns:repeat(2,1fr); gap:1em; } }
%three-columns { @include base.respond-to('>medium') { display:grid; grid-template-columns:repeat(3,1fr); gap:1em; } }

/* label & field helpers */
%f-f-label {
  @include base.typo-paragraph;
  color: base.$color-grey-dark-2; font-weight: 500; overflow: hidden;

  .f-fl-optional {
    @include base.typo-paragraph;
    color: base.$color-grey-dark-2; font-style: italic;
    margin-bottom: base.unit-rem-calc(8px);
  }
}
%f-f-field { display: flex; flex-direction: column; justify-content: flex-end; }



/* Apply the placeholders to real selectors */
/* ──────────────────────────────────────────── */

.leads-form,
.leads-form-details-preview-container,
.leads-form-edit-preview-container {
  @extend %preview-container;

  .ca-form-wrapper { @extend %ca-form-wrapper; }
  .marketing-section {
    @extend %two-columns;
  }

  .c-c-files {
    display: flex;
    justify-content: space-between;
    align-items: start;
    flex-direction: column;
    grid-column: 1 / -1;

    gap: base.unit-rem-calc(5px);
    margin: 0 0 base.unit-rem-calc(16px) 0;

    .t-hidden {
      display: none;
    }

  }

  .ca-form-wrapper .upload-files-section { @extend %upload-files-section; }

  .c-c-files {
    .upload-files-section      { @extend %upload-files-section; }
  }
  .appointment-request-section { @extend %appointment-request-section; }
  .form-actions             { @extend %form-actions; }
  .form-footer              { @extend %form-footer; }
  .two-columns              { @extend %two-columns; }
  .three-columns            { @extend %three-columns; }
  .f-f-label                { @extend %f-f-label; }
  .form-group.f-field       { @extend %f-f-field; }




}