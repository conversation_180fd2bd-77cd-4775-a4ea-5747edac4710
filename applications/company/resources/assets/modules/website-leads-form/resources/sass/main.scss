@use '~@cac-sass/config/global' with (
    $legacy: false
);
@use '~@cac-sass/base';
@use '~@cac-sass/app/form';
@use '~@cac-sass/app/highlight';
@use '~@cac-sass/app/grid';

@use '~@cas-layout-sass/layout';
@use '~@cas-table-sass/table';
@use '~@cas-modal-sass/modal';
@use '~@cas-tooltip-sass/tooltip';

@use '~@cas-form-input-sass/dropdown';
@use '~@cas-form-input-sass/number';
@use '~@cas-form-input-sass/date-time';
@use '~@cas-form-input-sass/uppy';

@use '_base' as leads-form-base;


body {
  background-color: white!important;
}
.a-pic-logo,
.a-p-image              { display: none; }


.m-website-leads-form {
  position: relative;
  @include base.full-width-height;
  padding: 0;

  .ca-form-wrapper {
    max-width: 550px;
    @extend %ca-form-wrapper;

    height: auto;
    margin: 0 auto;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    border-radius: base.unit-rem-calc(8px);
    background-color: #fff;
    padding: base.unit-rem-calc(24px);

    @include base.respond-to('<medium') {
      width: 100%;
      padding-bottom: base.unit-rem-calc(48px);
    }

    .email-checkbox-section .form-group-email .form-control {
      width: inherit;
    }

  }

  .form-section {
    align-items: start;
    @include base.respond-to('>small') {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 1em;
    }
  }

  .contact-section,
  .address2-section {
    align-items: start;
  }

  .btn-primary:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }

  .c-cr-loader {
    display: none;
    position: absolute;
    @include base.full-width-height;
    background: rgba(0, 0, 0, 0.5) url('~@cac-public/images/loading.svg') no-repeat center;
    background-size: base.unit-rem-calc(100px) base.unit-rem-calc(100px);
    z-index: 120;
  }

  .grecaptcha-badge {
    left: 0 !important;
    right: auto !important;
  }

  .submit-button {
    background-color: var(--lform-accent-bg, #{base.$color-primary-default});
    border-color: var(--lform-accent-border, #{base.$color-primary-default});
    color: var(--lform-accent-text, #fff);

    &:hover {
      background-color: var(--lform-hover-bg, #{base.$color-primary-light-1});
      border-color: var(--lform-accent-border, #{base.$color-primary-light-1});
      color: var(--lform-hover-text, #fff);
    }
  }


}
