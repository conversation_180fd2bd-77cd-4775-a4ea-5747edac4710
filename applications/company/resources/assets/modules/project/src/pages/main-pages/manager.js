'use strict';

const accounting = require("accounting");
const moment = require('moment-timezone');

const Page = require('@ca-package/router/src/page');
const Table = require('@ca-submodule/table').Base;

const Api = require('../../api');

const Cookie = require('@cac-js/utils/cookie');

const manager_tpl = require('@cam-project-tpl/pages/main-pages/manager.hbs');


class Manager extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);
        Object.assign(this.state, {
            page_name: 'project',
            saved_scope: null,
            saved_table_order: null,
            saved_table_visibility: null,
            table: null,
            table_loaded: false,
            table_scope: {
                sorts: {
                    created_at: Table.Sort.ASC
                },
                filters: {
                    status: [Table.Operators.EQUAL, Api.Constants.Projects.Status.ACTIVE]
                }
            },
            total_bids_sent: 0,
            total_bids_accepted: 0,
            total_bids_accepted_value: 0
        });
    };

    /**
     * Get status types
     *
     * @readonly
     *
     * @returns {{CANCELLED: number, CLOSED: number, ACTIVE: number}}
     */
    static get Status() {
        return {
            ACTIVE: 1,
            CANCELLED: 2,
            CLOSED: 3
        };
    };

    /**
     * Get delete modal
     *
     * @readonly
     *
     * @returns {module:Project/Modals/Delete}
     */
    get delete_modal() {
        if (this.state.delete_modal === undefined) {
            let modal = require('@cam-project-js/modals/main/manager/list/delete');
            this.state.delete_modal = new modal(this);
        }
        return this.state.delete_modal;
    };

    /**
     * Get table
     *
     * @readonly
     *
     * @returns {module:Table.Base}
     */
    get table() {
        return this.state.table;
    };

    /**
     * Trigger delete modal for row
     *
     * @param {Object} data - row data from table
     */
    handleDelete(data) {
        this.delete_modal.open(data);
    };

    /**
     * Generate property page url filtered by property id
     *
     * @param {number} id - property id
     * @returns {string}
     */
    propertyPageUrl(id) {
        return window.fx_pages.PROPERTIES.replace('{property_id}', id);
    };

    /**
     * Generate property page url filtered by property id
     *
     * @param {number} id - property id
     * @returns {string}
     */
    projectPageUrl(id) {
        return window.fx_pages.PROJECT_MANAGEMENT.replace('{project_id}',id)
    };

    /**
     * Define the default settings for project table
     *
     * @returns {Object}
     */
    tableSettings() {
        return {
            server_paginate: false,
            use_table_settings: true,
            column_order: this.state.saved_table_order,
            column_visibility: this.state.saved_table_visibility
        };
    };

    /**
     * Change table headers after scope has changed
     */
    changeTableHeaders() {
        this.state.table.changeColumnName('Bids Sent', `Bids Sent <span class="table-header-tag">${this.state.total_bids_sent}</span>`);
        this.state.table.changeColumnName('Bids Accepted', `Bids Accepted <span class="table-header-tag">${this.state.total_bids_accepted}</span>`);
        this.state.table.changeColumnName('Total Sold', `Total Sold <span class="table-header-tag">${accounting.formatMoney(this.state.total_bids_accepted_value)}</span>`);

        this.state.total_bids_accepted = 0;
        this.state.total_bids_accepted_value = 0;
        this.state.total_bids_sent = 0;
    };

    /**
     * Set table header data after row has been drawn
     *
     * @param {object} data
     */
    setTableHeaderData(data) {
        this.state.total_bids_accepted = data.total_bids_accepted !== null ? data.total_bids_accepted + this.state.total_bids_accepted : this.state.total_bids_accepted;
        this.state.total_bids_accepted_value = data.total_bids_accepted_value !== null ? parseFloat(data.total_bids_accepted_value) + parseFloat(this.state.total_bids_accepted_value) : this.state.total_bids_accepted_value;
        this.state.total_bids_sent = data.total_bids_sent !== null ? data.total_bids_sent + this.state.total_bids_sent : this.state.total_bids_sent;
    };

    /**
     * Create the Project DataTable and apply settings and defaults
     */
    createTable() {
        let status_map = new Map([
            [Manager.Status.ACTIVE, '<span class="h-text t-yellow">In Progress</span>'],
            [Manager.Status.CANCELLED, '<span class="h-text t-grey">Cancelled</span>'],
            [Manager.Status.CLOSED, '<span class="h-text t-green">Closed</span>']
        ]);

        let priority_map = new Map([
            [Api.Constants.Projects.Priority.LOW, '<span class="h-text t-blue">Low</span>'],
            [Api.Constants.Projects.Priority.MEDIUM, '<span class="h-text t-yellow">Medium</span>'],
            [Api.Constants.Projects.Priority.HIGH, '<span class="h-text t-red">High</span>']
        ]);

        this.state.table = new Table(this.elem.table, this.tableSettings())
            .on('row_click', (data) => {
                window.location.href = this.projectPageUrl(data.id);
            }).on('scope_change', (scope) => {
               this.changeTableHeaders();

                this.state.table_scope = scope;
                let url_scope = Table.buildUrlFromScope(this.state.table_scope);

                // set cookie with scope to expire after an hour
                Cookie.setCookie(`${this.state.page_name}_table_scope`, url_scope.replace('?', ''), Cookie.expirationTypes().Hours, 1);
                window.history.replaceState(null, '', window.location.href.split('?')[0]+url_scope);
            }).on('table_settings_changed', (config) => {
                Cookie.setCookie(`${this.state.page_name}_table_order`, config.order.toString());
                Cookie.setCookie(`${this.state.page_name}_table_visibility`, config.visibility.toString());
            }).on('row_drawn', (row, data) => {
               this.setTableHeaderData(data);
            });

        // set header config
        this.state.table.setHeader({
            custom_search: true,
            search: true,
            search_placeholder: 'Search',
            filter_name: 'Projects'
        });

        // set toolbar config
        this.state.table.setToolbar({
            filter: false,
            settings: false
        });

        this.state.table.setFilterOptions({
            salesperson_user_id: {
                label: 'Salesperson',
                type: Table.FilterValueTypes.SELECT,
                options: project_data.filter_options.salespeople,
                visible: !project_data.sales_only
            },
            type: {
                label: 'Project Type',
                type: Table.FilterValueTypes.SELECT,
                options: project_data.filter_options.project_types
            },
            priority: {
                label: 'Priority',
                type: Table.FilterValueTypes.SELECT,
                options: {
                    1: {
                        label: 'High',
                        value: Api.Constants.Projects.Priority.HIGH
                    },
                    2: {
                        label: 'Medium',
                        value: Api.Constants.Projects.Priority.MEDIUM
                    },
                    3: {
                        label: 'Low',
                        value: Api.Constants.Projects.Priority.LOW
                    }
                }
            },
            status: {
                label: 'Status',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: {
                    1: {
                        label: 'In Progress',
                        value: Api.Constants.Projects.Status.ACTIVE
                    },
                    2: {
                        label: 'Cancelled',
                        value: Api.Constants.Projects.Status.CANCELLED
                    },
                    3: {
                        label: 'Closed',
                        value: Api.Constants.Projects.Status.CLOSED
                    }
                }
            },
            result_type_id: {
                label: 'Result',
                type: Table.FilterValueTypes.SELECT,
                options: project_data.filter_options.result_types
            },
            referral_marketing_type_id: {
                label: 'Primary Marketing Source',
                type: Table.FilterValueTypes.SELECT,
                options: project_data.filter_options.marketing_sources
            },
            secondary_marketing_type_id: {
                label: 'Secondary Marketing Source',
                type: Table.FilterValueTypes.SELECT,
                options: project_data.filter_options.marketing_sources
            },
            is_financing_enabled: {
                label: 'Is Financing Enabled?',
                type: Table.FilterValueTypes.BOOLEAN,
                field_required: true,
                options: {
                    1: {
                        label: 'Yes',
                        value: 1
                    },
                    2: {
                        label: 'No',
                        value: 0
                    }
                }
            },
            has_sales_appointment: {
                label: 'Has Sales Appointment',
                type: Table.FilterValueTypes.BOOLEAN,
                visible: project_data.user.billing_allowed,
                field_required: true,
                options: {
                    1: {
                        label: 'Yes',
                        value: 1
                    },
                    2: {
                        label: 'No',
                        value: 0
                    }
                }
            },
            has_bids_sent: {
                label: 'Has Bids Sent',
                type: Table.FilterValueTypes.BOOLEAN,
                visible: project_data.user.billing_allowed,
                field_required: true,
                options: {
                    1: {
                        label: 'Yes',
                        value: 1
                    },
                    2: {
                        label: 'No',
                        value: 0
                    }
                }
            },
            has_bids_accepted: {
                label: 'Has Bids Accepted',
                visible: project_data.user.billing_allowed,
                type: Table.FilterValueTypes.BOOLEAN,
                field_required: true,
                options: {
                    1: {
                        label: 'Yes',
                        value: 1
                    },
                    2: {
                        label: 'No',
                        value: 0
                    }
                }
            },
            first_sales_appointment: {
                label: 'First Sales Appointment',
                type: Table.FilterValueTypes.DATE,
            },
            first_bid_accepted: {
                label: 'First Bid Accepted',
                type: Table.FilterValueTypes.DATE,
            },
            cancelled_on: {
                label: 'Cancelled Date',
                type: Table.FilterValueTypes.DATE,
            },
            completed_on: {
                label: 'Closed Date',
                type: Table.FilterValueTypes.DATE,
            },
            created_at: {
                label: 'Created Date',
                type: Table.FilterValueTypes.DATE,
            },
            created_by_user_id: {
                label: 'Created By',
                type: Table.FilterValueTypes.SELECT,
                options: project_data.filter_options.all_users
            },
            updated_at: {
                label: 'Updated Date',
                type: Table.FilterValueTypes.DATE,
            },
            updated_by_user_id: {
                label: 'Updated By',
                type: Table.FilterValueTypes.SELECT,
                field_required: true,
                options: project_data.filter_options.all_users
            }
        });

        // set columns config
        this.state.table.setColumns({
            reference_id: {
                label: 'Reference #'
            },
            customer_name: {
                label: 'Customer',
                value: (data) => {
                    return `<a href="${window.fx_pages.CUSTOMER_MANAGEMENT.replace('{customer_id}', data.property.customer.id)}">${data.customer_name}</a>`
                }
            },
            property_address: {
                label: 'Property',
                value: (data) => {
                    return `<a href="${window.fx_pages.CUSTOMER_MANAGEMENT.replace('{customer_id}', data.property.customer.id)}">${data.property_address}</a>`
                }
            },
            salesperson_name: {
                label: 'Salesperson'
            },
            description: {
                label: 'Project Name',
                value: (data) => {
                    return `<a href="${window.fx_pages.PROJECT_MANAGEMENT.replace('{project_id}', data.id)}">${data.description}</a>`
                }
            },
            type_name: {
                label: 'Project Type',
                value: data => data?.type_name || null
            },
            priority: {
                label: 'Priority',
                value: data => priority_map.get(data.priority)
            },
            status: {
                label: 'Status',
                value: data => status_map.get(data.status)
            },
            result_type_name: {
                label: 'Result',
                value: data => data?.result_type_name || null,
            },
            marketing_source: {
                label: 'Primary Marketing Source'
            },
            secondary_marketing_source: {
                label: 'Secondary Marketing Source'
            },
            is_financing_enabled: {
                label: 'Is Financing Enabled?',
                value: (data) => {
                    return data.is_financing_enabled === true ? 'Yes' : null;
                }
            },
            first_sales_appointment: {
                label: 'First Sales Appointment',
                visible: project_data.user.billing_allowed,
                value: data => {
                    if (data.first_sales_appointment === null) {
                        return null;
                    }
                    return moment(data.first_sales_appointment).format('MM/DD/YYYY h:mm a');
                }
            },
            total_bids_sent: {
                label: 'Bids Sent',
                visible: project_data.user.billing_allowed,
                value: data => data?.total_bids_sent || null
            },
            total_bids_accepted: {
                label: 'Bids Accepted',
                visible: project_data.user.billing_allowed,
                value: data => data?.total_bids_accepted || null
            },
            total_bids_accepted_value: {
                label: 'Total Sold',
                visible: project_data.user.billing_allowed,
                value: (data) => {
                    if (data.total_bids_accepted_value === null) {
                        return null;
                    }
                    return accounting.formatMoney(data.total_bids_accepted_value);
                }
            },
            first_bid_accepted: {
                label: 'First Bid Accepted',
                visible: project_data.user.billing_allowed,
                value: data => {
                    if (data.first_bid_accepted === null) {
                        return null;
                    }
                    return moment.utc(data.first_bid_accepted).tz(this.parent.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                }
            },
            cancelled_on: {
                label: 'Cancelled On',
                value: (data) => {
                    let date = data.cancelled_on;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.parent.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                },
            },
            completed_on: {
                label: 'Closed On',
                value: (data) => {
                    let date = data.completed_on;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.parent.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                },
            },
            created_at: {
                label: 'Created',
                value: (data) => {
                    let date = data.created_at;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.parent.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                }
            },
            created_by_user_name: {
                label: 'Created By'
            },
            updated_at: {
                label: 'Updated',
                value: (data) => {
                    let date = data.updated_at;
                    if (date === null) {
                        return null;
                    }
                    return moment(date).tz(this.parent.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                }
            },
            updated_by_user_name: {
                label: 'Updated By'
            }
        });

        // set row action config
        this.state.table.setRowActions({
            view_details: {
                label: 'View Details',
                link: {
                    href: data => this.projectPageUrl(data.id)
                }
            },
            view_properties: {
                label: 'View Property',
                link: {
                    href: data => this.propertyPageUrl(data.property_id)
                }
            },
            delete: {
                label: 'Delete',
                negate: true,
                visible: project_data.can_delete,
                action: (data) => {
                    this.handleDelete(data);
                }
            }
        });

        // set buttons config
        this.state.table.setButtons({
            export_csv: {
                label: 'Export',
                action: (e) => {
                    let button = $(e.target);
                    button.prop('disabled', true);
                    setTimeout(() => button.prop('disabled', false), 4000);
                    let request = this.state.table.buildRequest(new Api.Request, this.state.table_scope);
                    window.location.href = window.fx_url.API + 'export/projects' + request.getQueryString({
                        disabled: {
                            pagination: true
                        }
                    });
                },
                visible: project_data.can_export,
                type_class: 't-tertiary-icon',
                icon: 'system--download-line'
            }
        });

        this.state.table.setAjax(Api.Resources.Projects, (request) => {
            let accept_type = project_data.user.billing_allowed ? 'application/vnd.adg.fx.collection-v1+json' : 'application/vnd.adg.fx.collection-limited-v1+json';
            request.accept(accept_type);
        });
    };

    /**
     * Load page
     *
     * @param {object} request
     * @param {function} next
     */
    async load(request, next) {
        if (this.state.table_loaded) {
            // false means we don't want to reset paging
            this.state.table.draw(false);
        } else {
            // if request query contains scope we use it
            if (Object.keys(request.query).length > 0) {
                this.state.table_scope = Table.buildScopeFromQuery(request.query);
                // otherwise we get it from the cookie
            } else if (this.state.saved_scope !== null) {
                this.state.table_scope = Table.buildScopeFromQuery(this.state.saved_scope);
            }
            // otherwise we pull from the default scope stored in the state
            this.state.table.setState(this.state.table_scope);
            this.state.table.build();
            this.state.table_loaded = true;
        }
        await super.load(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.table = this.elem.root.fxFind('table-container');
        this.state.saved_scope = Cookie.getCookie(`${this.state.page_name}_table_scope`);

        let column_order = Cookie.getCookie(`${this.state.page_name}_table_order`);
        if (column_order !== null) {
            this.state.saved_table_order = column_order.split(',');
        }

        let column_visibility = Cookie.getCookie(`${this.state.page_name}_table_visibility`);
        if (column_visibility !== null) {
            this.state.saved_table_visibility = column_visibility.split(',');
        }

        this.createTable();
    };

    /**
     * Render page
     */
    render() {
        return manager_tpl();
    };
}

module.exports = Manager;
