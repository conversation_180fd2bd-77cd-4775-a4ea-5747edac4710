<div class="c-p--p-page m-additional-costs-import" data-js="step-container">
    <div class="m-page-header">
        <div class="c-ph-actions" data-button-left>
            <button class="c-pha-tertiary t-icon-text" data-navigate="{{cancel_route}}">
                <div class="t-cancel" data-text>Cancel</div>
            </button>
        </div>
        <div class="c-ph-actions" data-button-right>
            <button class="b-text t-primary" data-js="import-button">
                <div data-text>Import</div>
            </button>
        </div>
    </div>
    <div class="c-import-step" data-js="upload-step">
        <div class="c-import-step-content">
            <div class="c-import-header-row">
                <h4>Import Review</h4>
                <span class="h-text t-green"><span
                        data-js="records-queued-count"></span> Records queued for Import</span>
            </div>
            <div class="c-pi-table t-pi-table-columns" data-js="final-review-table"></div>
        </div>
    </div>
</div>
