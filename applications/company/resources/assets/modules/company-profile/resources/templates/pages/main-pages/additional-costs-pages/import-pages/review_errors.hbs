<div class="c-p--p-page m-additional-costs-import m-additional-costs-import-review-errors"
     data-js="step-container">
    <div class="m-page-header">
        {{#if back_route}}
            <div class="c-ph-actions" data-button-left>
                <button class="c-pha-tertiary t-icon-text" data-navigate="{{back_route}}">
                    <div data-text>Back</div>
                    <svg data-icon>
                        <use xlink:href="#remix-icon--arrows--arrow-left-line"></use>
                    </svg>
                </button>
            </div>
        {{/if}}
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary t-disabled-next" disabled title="Please correct the errors below to proceed.">
                <div data-text>Next</div>
                <svg data-icon>
                    <use xlink:href="#remix-icon--arrows--arrow-right-line"></use>
                </svg>
            </button>
        </div>
    </div>
    <div class="c-import-step" data-js="upload-step">
        <div class="c-import-step-content">
            <div class="c-import-header-row">
                <h4>Review Errors</h4>
                <span class="h-text t-green"><span
                        data-js="records-queued-count"></span> Records queued for Import</span>
            </div>
            <p class="t-error"><span class="t-error" data-js="errors-count"></span> errors detected. Please correct the
                errors below to proceed.</p>
            <div class="c-pi-table t-pi-table-columns" data-js="review-errors-table"></div>
        </div>
    </div>
</div>