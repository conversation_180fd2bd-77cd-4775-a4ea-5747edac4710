<form class="m-form" data-js="form">
    <div class="c-f-row t-name-unit">
        <div class="c-fr-field t-full f-field">
            <label class="f-f-label">Name</label>
            <input class="f-f-input" required type="text" value="{{name}}" data-js="name"/>
        </div>
        <div class="c-fr-field t-full f-field">
            <label class="f-f-label">Unit</label>
            <input class="f-f-input" required type="text" value="{{unit}}" data-js="unit"/>
        </div>
    </div>
    <div class="c-f-row t-cost-markup-price">
        <div class="c-fr-field t-full f-field">
            <label class="f-f-label">Cost</label>
            <input class="f-f-input" required type="text" value="{{cost}}" data-fx-form-input="number"
                   data-parsley-trigger="keyup" data-js="cost"/>
        </div>
        <div class="c-fr-field t-full f-field">
            <label class="f-f-label">
                Markup
                <span class="f-fl-optional">(Optional)</span>
            </label>
            <input class="f-f-input" type="text" value="{{markup}}" data-fx-form-input="number" data-js="markup"/>
        </div>
        <div class="c-fr-field t-full f-field t-unit-price">
            <label class="f-f-label">Unit Price</label>
            <input class="f-f-input" disabled type="text" value="{{unit_price}}" data-fx-form-input="number"
                   data-js="unit_price"/>
        </div>
    </div>
</form>
