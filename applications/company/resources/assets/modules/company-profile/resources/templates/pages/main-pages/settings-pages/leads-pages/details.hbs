<div class="c-s--p-page">
    <div class="m-page-header t-section">
        <h4 class="c-ph-title" data-title>{{title}}</h4>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary t-edit" data-js="enable-button">
                <div data-text>Edit</div>
                <svg data-icon><use xlink:href="#remix-icon--design--edit-2-line"></use></svg>
            </button>
            <button class="c-pha-tertiary t-edit t-hidden" data-js="edit-button" data-navigate="{{edit_route}}">
                <div data-text>Edit</div>
                <svg data-icon><use xlink:href="#remix-icon--design--edit-2-line"></use></svg>
            </button>
        </div>
    </div>
    <div class="m-company-settings m-leads-settings">
        <p>Build a form that potential customers can fill out directly on your website or with an API Integration. Once the form is submitted, the customer's information will automatically be added to the Leads section of {{brand_name}}. Simply design your form, copy the provided code, and paste it into any page on your website or setup the api connection.</p>
        <div class="c-cs-wrapper">
            <div class="c-csw-content t-space-between">
                <div class="f-field flex no-margin" data-js="switch-container">
                    <h4 class="c-cswc-title">
                        Lead Form (Website/API Integration)
                        <span data-tooltip data-type="info"> Allow leads to be captured from your website with an embedded form or with an API Integration.</span>
                    </h4>
                </div>
                <div class="c-cswc-content" data-js="feature-status">
                    <span class="h-text t-grey">Not setup</span>
                </div>
            </div>
        </div>
        <div class="c-cs-content leads-form-details-preview-container t-hidden" data-js="form-preview-container"></div>
        <br>
        <div class="c-cs-content t-hidden" data-js="code-container">
            <h4>Website Form</h4>
            <p>Copy this code to use for on your website or <a href="#" target="_blank" data-js="email-code-link">click here</a> to send lead form code to your website management/marketing team.</p>
            <div class="t-bottom-gap">
                <div class="c-s-content">
                    <div class="c-sc-group f-field">
                        <div class="c-scg-button">
                            <div class="c-scg-input f-f-input"><input data-js="html-code-input" id="code-snippet" type="text" readonly></div>
                            <button class="b-text t-primary" data-js="html-code-copy-button" data-clipboard-target="#code-snippet">Copy</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <br>
        <div class="c-cs-content t-hidden leads-api-section" data-js="leads-api-section">
            <h4>API Integration</h4>
            <p>
                Use this key to submit leads securely via an external integration. Include it in the request header as
                <i>X-CA-Lead-API-Key</i> when sending a POST request to the ingest endpoint. Check out
                <a href="https://cxlratr.to/hc-leads-api" target="_blank">the documentation</a> for more details on how to use this API key.
            </p>
            <div class="c-cs-wrapper" data-js="api-key-code-container">
                <div class="c-csw-content t-space-between">
                    <div class="f-field flex no-margin" data-js="switch-container">
                        <h4 class="c-cswc-title">API Key
                            <span data-tooltip data-type="info"> Use this key to submit leads securely via an external integration.</span>
                        </h4>
                    </div>
                </div>
                <div class="api-key-wrapper">
                    <div class="c-cswc-content" data-js="api-key-status">
                        <span class="h-text t-grey">Not setup</span>
                    </div>
                    <div class="c-cswc-content">
                        <span class="h-text t-hidden" data-js="api-key-code">XXXXXXXXXXXX</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>