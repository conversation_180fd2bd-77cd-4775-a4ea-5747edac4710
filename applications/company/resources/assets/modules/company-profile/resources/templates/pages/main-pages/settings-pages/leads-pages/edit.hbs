<div class="c-s--p-page">
    <div class="m-page-header t-section">
        <h4 class="c-ph-title" data-title>{{title}}</h4>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary-negate-grey" data-js="button" data-navigate="{{cancel_route}}">Cancel</button>
            <button class="c-pha-primary" data-js="save">Save</button>
        </div>
    </div>
    <div class="m-company-settings m-leads-settings">
        <p>Build a form that potential customers can fill out directly on your website or with an API Integration. Once the form is submitted, the customer's information will automatically be added to the Leads section of {{brand_name}}. Simply design your form, copy the provided code, and paste it into any page on your website or setup the api connection.</p>
        <div class="c-cs-wrapper">
            <div class="c-csw-content space-between">
                <div class="f-field t-flex t-no-margin" data-js="switch-container">
                    <input class="f-f-input" type="checkbox" id="status_e" data-fx-form-input="switch" data-js="lead-form-status" style="visibility: hidden; position: absolute; left: -9999px;" data-parsley-multiple="status_e"><label for="status_e" class="f-f-switch"></label>
                    <h4 class="c-cswc-title">
                        Lead Form (Website/API Integration)
                        <span data-tooltip data-type="info">Allow leads to be captured from your website with an embedded form or with an API Integration.</span>
                    </h4>
                </div>
            </div>
        </div>
        <div class="c-cs-content t-hidden" data-js="lead-form-settings">
             <div class="c-cs-wrapper" data-js="lead-form-settings-optional-container">
                <div class="c-wswc-lfs">
                    <h4 class="c-cswc-title lfs-title">
                        Form Settings
                    </h4>
                    <p class="lfs-subtitle">Select when and where you want this form to be visible to your team or customers. Click the info icon for additional descriptions for each setting.</p>
                    <div class="c-wswc-lfs-main">
                        <div class="f-field">
                            <label class="cswc-field-title" for="default_assigned_to">Default Assigned To
                            <span class="f-fl-optional">(Optional)</span>
                            <span data-tooltip data-type="info">
                                Assign a default user to automatically receive new leads from the form. This user can reassign, convert, or edit the lead as needed.</span>
                            </label>
                           <select class="f-f-input" data-js="default_assigned_to" data-fx-form-input="static-dropdown" id="default_assigned_to" name="default_assigned_to"></select>
                        </div>
                        <div class="f-field">
                            <label class="cswc-field-title" for="google_tag_id">Google Tag ID
                                <span class="f-fl-optional">(Optional)</span>
                                <span data-tooltip data-type="info">
                                    Add a Google Tag ID to track form submissions. This ID can be found in your Google Tag Manager account.</span>
                            </label>
                            <input class="f-f-input" type="text" data-js="google_tag_id" id="google_tag_id" name="google_tag_id" value="" placeholder="Enter Google Tag ID here...">
                        </div>

                    </div>
                    <div class="c-wswc-lfs-emails">
                        <div class="f-field">
                            <label class="cswc-field-title" for="additional_email_recipients">Additional Lead Email Recipients
                            <span class="f-fl-optional">(Optional)</span>
                            <span data-tooltip data-type="info">
                                Add additional Lead Email Addresses to receive notifications when a lead is submitted. Separate multiple email addresses with a comma.</span>
                            </label>
                            <textarea name="additional_email_recipients" class="f-f-input" data-js="additional_email_recipients" id="additional_email_recipients" rows="3" placeholder="Enter Lead Email Addresses here..."></textarea>
                        </div>
                    </div>
                    <div class="c-wswc-lfs-settings">
                        <div class="f-field" style="display: flex; gap: 8px; align-items: center;">
                            <input class="f-f-input" type="checkbox" id="is_send_notifications"
                                   data-fx-form-input="switch" data-js="is_send_notifications">
                            <label class="f-f-label t-label" for="is_send_notifications">
                                Send Welcome Email
                                 <span data-tooltip data-type="info">
                                    Automatically send the welcome email to new leads that are added from the form
                                </span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="lead-form-settings">
                <form id="lead-form" class="sidebar" data-js="form">
                    <div class="input-fields">
                        <div class="f-field">
                            <label class="cswc-field-title" for="title">Form Title</label>
                            <input class="f-f-input" type="text" data-js="title_label" id="title_label" name="title_label" value="Contact Us">
                        </div>
                        <div class="f-field">
                            <label class="cswc-field-title" for="save_button_label">Submit Button Label</label>
                            <input class="f-f-input" type="text" data-js="save_button_label" id="save_button_label" name="save_button_label" value="Submit">
                        </div>
                    </div>
                    <div class="optional-inputs">
                        <h4>Optional Inputs</h4>
                        <div class="c-csw-content">
                            <div class="c-cswc-group">
                                <div class="f-checkbox">
                                    <div class="f-field t-flex" data-js="switch-container">
                                        <input class="f-f-input" type="checkbox" data-js="email_visibility" id="email_visibility">
                                    </div>
                                </div>
                                <div class="c-cswc-group-field">
                                    <div class="f-field">
                                        <h4 class="cswc-field-title" data-js="email_options_title">Email</h4>
                                        <div class="c-cswcf-item t-hidden" data-js="email_options_container">
                                            <div class="f-field t-flex" data-js="switch-container">
                                                <input class="f-f-input" type="checkbox" data-js="email_requirement" id="email_requirement" data-fx-form-input="switch" style="visibility: hidden; position: absolute; left: -9999px;">
                                                <label for="email_requirement" class="f-f-switch"></label>
                                                <div class="cswc-field-title">Required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="c-cswc-group">
                                <div class="f-checkbox">
                                    <div class="f-field t-flex" data-js="switch-container">
                                        <input class="f-f-input" type="checkbox" data-js="phone_visibility" id="phone_visibility">
                                    </div>
                                </div>
                                <div class="c-cswc-group-field">
                                    <div class="f-field">
                                        <h4 class="cswc-field-title" data-js="phone_options_title">Phone #</h4>
                                        <div class="c-cswcf-item t-hidden" data-js="phone_options_container">
                                            <div class="f-field t-flex" data-js="switch-container">
                                                <input class="f-f-input" type="checkbox" data-js="phone_requirement" id="phone_requirement" data-fx-form-input="switch" style="visibility: hidden; position: absolute; left: -9999px;">
                                                <label for="phone_requirement" class="f-f-switch "></label>
                                                <div class="cswc-field-title">Required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="c-cswc-group">
                                <div class="f-checkbox">
                                    <div class="f-field t-flex" data-js="switch-container">
                                        <input class="f-f-input" type="checkbox" data-js="address_visibility" id="address_visibility">
                                    </div>
                                </div>
                                <div class="c-cswc-group-field">
                                    <div class="f-field">
                                        <h4 class="cswc-field-title" data-js="address_options_title">Address</h4>
                                        <div class="c-cswcf-item t-hidden" data-js="address_options_container">
                                            <div class="f-field t-flex" data-js="switch-container">
                                                <input class="f-f-input" type="checkbox" data-js="address_requirement" id="address_requirement" data-fx-form-input="switch" style="visibility: hidden; position: absolute; left: -9999px;">
                                                <label for="address_requirement" class="f-f-switch "></label>
                                                <div class="cswc-field-title">Required</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="c-cswc-group">
                                <div class="f-checkbox">
                                    <div class="f-field t-flex" data-js="switch-container">
                                        <input class="f-f-input" type="checkbox" data-js="marketing_source_visibility" id="marketing_source_visibility">
                                    </div>
                                </div>
                                <div class="c-cswc-group-field">
                                    <h4 class="cswc-field-title" data-js="marketing_source_options_title">Marketing Source</h4>
                                    <div class="c-cswcf-item t-hidden" data-js="marketing_source_options_container">
                                        <div class="f-field t-flex padding" data-js="switch-container">
                                            <input class="f-f-input" type="checkbox" data-js="marketing_source_requirement" id="marketing_source_requirement" data-fx-form-input="switch" style="visibility: hidden; position: absolute; left: -9999px;">
                                            <label for="marketing_source_requirement" class="f-f-switch "></label>
                                            <div class="cswc-field-title">Required</div>
                                        </div>
                                        <div class="f-field spacing padding">
                                            <label class="cswc-field-title" for="marketing_source_label">Marketing Source Label</label>
                                            <input class="f-f-input" type="text" data-js="marketing_source_label" id="marketing_source_label" name="marketing_source_label" value="Comments">
                                        </div>
                                        <div class="f-field spacing padding">
                                            <div class="f-field t-flex t-no-margin" data-js="radio-container">
                                                <input class="f-f-input" type="radio" data-js="marketing_source_dropdown" id="marketing_source_dropdown" name="marketing_source_options" value="2" checked>
                                                <label class="dropdown-tooltip" for="marketing_source_dropdown">Dropdown
                                                    <span data-tooltip data-type="info">Use your current Marketing Source dropdown list. This list can be edited in the Marketing section of your top navigation.</span>
                                                </label>
                                            </div>
                                            <div class="f-field t-flex t-no-margin" data-js="radio-container">
                                                <input class="f-f-input" type="radio" data-js="marketing_source_freeform" id="marketing_source_freeform" name="marketing_source_options" value="1">
                                                <label class="dropdown-tooltip" for="marketing_source_freeform">Freeform
                                                    <span data-tooltip data-type="info">Your customers answers will be shown in the notes section of the lead.</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="c-cswc-group">
                                <div class="f-checkbox">
                                    <div class="f-field t-flex" data-js="switch-container">
                                        <input class="f-f-input" type="checkbox" data-js="project_type_visibility" id="project_type_visibility">
                                    </div>
                                </div>
                                <div class="c-cswc-group-field">
                                    <h4 class="cswc-field-title" data-js="project_type_options_title">Project Type</h4>
                                    <div class="c-cswcf-item t-hidden" data-js="project_type_options_container">
                                        <div class="f-field t-flex padding" data-js="switch-container">
                                            <input class="f-f-input" type="checkbox" data-js="project_type_requirement" id="project_type_requirement" data-fx-form-input="switch" style="visibility: hidden; position: absolute; left: -9999px;">
                                            <label for="project_type_requirement" class="f-f-switch "></label>
                                            <div class="cswc-field-title">Required</div>
                                        </div>
                                        <div class="f-field spacing padding">
                                            <label class="cswc-field-subtitle" for="marketing_source_label">Project Type Label</label>
                                            <input class="f-f-input" type="text" data-js="project_type_label" id="project_type_label" name="project_type_label" value="Comments">
                                        </div>
                                        <div class="f-field spacing padding">
                                            <div class="f-field t-flex t-no-margin" data-js="radio-container">
                                                <input class="f-f-input" type="radio" data-js="project_type_dropdown" id="project_type_dropdown" name="project_type_options" value="2" checked>
                                                <label class="dropdown-tooltip" for="project_type_dropdown">Dropdown
                                                    <span data-tooltip data-type="info">Use your current Project Type dropdown list. This list can be edited in the Project section of your Company Settings.</span>
                                                </label>
                                            </div>
                                            <div class="f-field t-flex t-no-margin" data-js="radio-container">
                                                <input class="f-f-input" type="radio" data-js="project_type_freeform" id="project_type_freeform" name="project_type_options" value="1">
                                                <label class="dropdown-tooltip" for="project_type_freeform">Freeform
                                                    <span data-tooltip data-type="info">Your customers answers will be shown in the notes section of the lead.</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="c-cswc-group">
                                <div class="f-checkbox">
                                    <div class="f-field t-flex" data-js="switch-container">
                                        <input class="f-f-input" type="checkbox" data-js="customer_notes_visibility" id="customer_notes_visibility">
                                    </div>
                                </div>
                                <div class="c-cswc-group-field">
                                    <h4 class="cswc-field-title" data-js="customer_notes_options_title">Customer Notes</h4>
                                    <div class="c-cswcf-item t-hidden" data-js="customer_notes_options_container">
                                        <div class="f-field t-flex padding" data-js="switch-container">
                                            <input class="f-f-input" type="checkbox" data-js="customer_notes_requirement" id="customer_notes_requirement" data-fx-form-input="switch" style="visibility: hidden; position: absolute; left: -9999px;">
                                            <label  for="customer_notes_requirement" class="f-f-switch "></label>
                                            <div class="cswc-field-title">Required</div>
                                        </div>
                                        <div class="f-field spacing padding">
                                            <label class="cswc-field-subtitle" for="customer_notes_label">Customer Notes Label</label>
                                            <input class="f-f-input" type="text" data-js="customer_notes_label" id="customer_notes_label" name="customer_notes_label" value="Comments">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="c-cswc-group">
                                <div class="f-checkbox">
                                    <div class="f-field t-flex" data-js="switch-container">
                                        <input class="f-f-input" type="checkbox" data-js="email_checkbox_visibility" id="email_checkbox_visibility">
                                    </div>
                                </div>
                                <div class="c-cswc-group-field">
                                    <h4 class="cswc-field-title" data-js="email_checkbox_options_title">Email Checkbox</h4>
                                    <div class="c-cswcf-item t-hidden" data-js="email_checkbox_options_container">
                                        <div class="f-field t-flex padding" data-js="switch-container">
                                            <input class="f-f-input" type="checkbox" data-js="email_checkbox_requirement" id="email_checkbox_requirement" data-fx-form-input="switch" style="visibility: hidden; position: absolute; left: -9999px;">
                                            <label  for="email_checkbox_requirement" class="f-f-switch "></label>
                                            <div class="cswc-field-title">Required</div>
                                        </div>
                                        <div class="f-field spacing padding">
                                            <label class="cswc-field-subtitle" for="email_checkbox_label">Email Checkbox Label</label>
                                            <input class="f-f-input" type="text" data-js="email_checkbox_label" id="email_checkbox_label" name="email_checkbox_label" value="Is it okay if we send you emails?">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="c-cswc-group">
                                <div class="f-checkbox">
                                    <div class="f-field t-flex" data-js="switch-container">
                                        <input class="f-f-input" type="checkbox" data-js="upload_file_visibility" id="upload_file_visibility">
                                    </div>
                                </div>
                                <div class="c-cswc-group-field">
                                    <h4 class="cswc-field-title" data-js="upload_file_options_title">Upload File</h4>
                                    <div class="c-cswcf-item t-hidden" data-js="upload_file_options_container">
                                        <div class="f-field t-flex padding" data-js="switch-container">
                                            <input class="f-f-input" type="checkbox" data-js="upload_file_requirement" id="upload_file_requirement" data-fx-form-input="switch" style="visibility: hidden; position: absolute; left: -9999px;">
                                            <label for="upload_file_requirement" class="f-f-switch"></label>
                                            <div class="cswc-field-title">Required</div>
                                        </div>
                                        <div class="f-field spacing padding">
                                            <label class="cswc-field-subtitle" for="upload_file_label">Upload File Label</label>
                                            <input class="f-f-input" type="text" data-js="upload_file_label" id="upload_file_label" name="upload_file_label" value="Upload File">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="c-cswc-group">
                                <div class="f-checkbox">
                                    <div class="f-field t-flex" data-js="switch-container">
                                        <input class="f-f-input" type="checkbox" data-js="appointment_request_visibility" id="appointment_request_visibility">
                                    </div>
                                </div>
                                <div class="c-cswc-group-field">
                                    <h4 class="cswc-field-title" data-js="appointment_request_options_title">Appointment Request</h4>
                                    <div class="c-cswcf-item t-hidden" data-js="appointment_request_options_container">
                                        <div class="f-field t-flex padding" data-js="switch-container">
                                            <input class="f-f-input" type="checkbox" data-js="appointment_request_requirement" id="appointment_request_requirement" data-fx-form-input="switch" style="visibility: hidden; position: absolute; left: -9999px;">
                                            <label for="appointment_request_requirement" class="f-f-switch"></label>
                                            <div class="cswc-field-title">Required</div>
                                        </div>
                                        <div class="f-field spacing padding">
                                            <label class="cswc-field-subtitle" for="appointment_request_label">Appointment Request Label</label>
                                            <input class="f-f-input" type="text" data-js="appointment_request_label" id="appointment_request_label" name="appointment_request_label" value="What days/times work best for you? (Choose 3)">
                                        </div>
                                        <div class="f-field spacing padding">
                                            <label class="cswc-field-subtitle" for="appointment_request_instruction_label">Instructions</label>
                                            <textarea class="f-f-input" data-js="appointment_request_instruction_label" id="appointment_request_instruction_label" name="appointment_request_instruction_label" value="Pick 3 appointment windows that are convenient for you. We will do our best to schedule one of these times. If you have an urgent request, call (816) 453-8650"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="content">
                    <div class="leads-form-edit-preview-container" data-js="form-preview-container"></div>
                </div>
            </div>
           <hr>
           <div class="leads-api-section">
               <h4>API Integration</h4>
               <p>
                   Use this key to submit leads securely via an external integration. Include it in the request header as
                   <i>X-CA-Lead-API-Key</i> when sending a POST request to the ingest endpoint. Check out
                   <a href="https://cxlratr.to/hc-leads-api" target="_blank">the documentation</a> for more details on how to use this API key.
               </p>
               <div class="c-cs-wrapper">
                   <div class="c-csw-content t-space-between">

                       <div class="f-field t-flex t-no-margin" data-js="switch-container">
                           <input class="f-f-input" type="checkbox" id="status_api_key" data-fx-form-input="switch" data-js="api-key-status" style="visibility: hidden; position: absolute; left: -9999px;" data-parsley-multiple="status_api_key"><label for="status_api_key" class="f-f-switch"></label>
                           <h4 class="c-cswc-title">API Key
                               <span data-tooltip data-type="info"> Use this key to submit leads securely via an external integration.</span>
                           </h4>
                       </div>
                   </div>
                   <div class="api-key-wrapper">
                       <div class="c-cswc-content" data-js="api-key-feature-status">
                           <span class="h-text t-grey">Not setup</span>
                       </div>
                       <div class="c-cswc-content">
                           <span class="h-text t-hidden" data-js="api-key-code">XXXXXXXXXXXX</span>
                       </div>
                   </div>
               </div>
           </div>
        </div>
    </div>
</div>