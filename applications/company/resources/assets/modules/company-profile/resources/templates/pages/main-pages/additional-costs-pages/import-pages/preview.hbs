<div class="c-p--p-page m-additional-costs-import" data-js="step-container">
    <div class="m-page-header">
        <div class="c-ph-actions" data-button-left>
            <button class="c-pha-tertiary t-icon-text" data-navigate="{{back_route}}">
                <div data-text>Back</div>
                <svg data-icon>
                    <use xlink:href="#remix-icon--arrows--arrow-left-line"></use>
                </svg>
            </button>
        </div>
        <div class="c-ph-actions" data-button-right>
            <button class="c-pha-tertiary t-text-icon" data-navigate="{{next_route}}">
                <div data-text>Next</div>
                <svg data-icon>
                    <use xlink:href="#remix-icon--arrows--arrow-right-line"></use>
                </svg>
            </button>
        </div>
    </div>
    <div class="c-import-step upload-step" data-js="upload-step">
        <div class="c-import-step-content">
            <div class="c-pi-mapping">
                <div class="c-pi-components" data-js="components">
                    <div class="c-pfc-header">
                        <h4>CSV Columns</h4>
                    </div>
                    <div class="c-pfc-input-container">
                        <div class="c-fr-field t-full f-field">
                            <span class="f-f-label">ID</span>
                            <select class="f-f-input" data-js="dropdown-id_user_defined"></select>
                        </div>
                        <div class="c-fr-field t-full f-field">
                            <span class="f-f-label">Name</span>
                            <select class="f-f-input" data-js="dropdown-name"></select>
                        </div>
                        <div class="c-fr-field t-full f-field">
                            <span class="f-f-label">Cost</span>
                            <select class="f-f-input" data-js="dropdown-cost"></select>
                        </div>
                        <div class="c-fr-field t-full f-field">
                            <span class="f-f-label">Markup</span>
                            <select class="f-f-input" data-js="dropdown-markup"></select>
                        </div>
                        <div class="c-fr-field t-full f-field">
                            <span class="f-f-label">Unit</span>
                            <select class="f-f-input" data-js="dropdown-unit"></select>
                        </div>
                    </div>
                </div>
            </div>
            <h4> Preview</h4>
            <div class="c-pi-table t-pi-table-columns t-pi-preview-table" data-js="preview-table-container"></div>
        </div>
    </div>
</div>