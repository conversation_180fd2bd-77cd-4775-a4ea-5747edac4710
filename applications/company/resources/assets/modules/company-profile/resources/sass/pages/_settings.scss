@use '~@cac-sass/base';
@use '~@cas-form-input-sass/uppy';
@use '~@cam-website-leads-form-sass/base' as leads-form-base;

//.t-hidden {
//    display: none;
//}

.m-company-settings-section {
    .c-s--pages {
        height: calc(100% - 56px);
        position: relative;
    }
    .c-s--p-page {
        position: relative;
        height: 100%;
        &.t-hidden {
            display: none;
        }
    }
}

.generate-api-key-container {
    #api-key-input {
        margin-bottom: 1em;
    }

    #copy-button {
        float: right;
    }

}

.m-page-wrapper {
    overflow: auto;
    height: calc(100% - 58px);
    padding: base.unit-rem-calc(24px);
    @include base.respond-to('<small') {
        padding: base.unit-rem-calc(24px) base.unit-rem-calc(16px);
    }
}
/* Company Settings */
.m-company-settings-form {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    gap: base.unit-rem-calc(32px);
    margin-bottom: base.unit-rem-calc(24px);

    .c-csf-error {
        width: 100%;
        display: none;
        @include base.callout-error;
        &.t-show {
            display: inline-flex;
        }
    }
    .c-csf-row {
        display: grid;
        row-gap: base.unit-rem-calc(8px);
        column-gap: base.unit-rem-calc(16px);
        >p {
            margin-bottom: 0;
        }
        .f-field {
            flex: 1;
        }
        &.t-switch {
            display: flex;
            justify-content: flex-start;
            padding: base.unit-rem-calc(12px);
            border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            border-radius: base.unit-rem-calc(8px);
            background: base.$color-background-form;
            overflow: auto;
            flex-wrap: wrap;
            gap: base.unit-rem-calc(24px);

            &.t-switch-multiline {
                grid-template-columns: repeat(auto-fit, minmax(14rem, 1fr));
                display: grid;
            }

            .f-field {
                display: flex;
                align-items: center;
                gap: base.unit-rem-calc(8px);
                .f-f-label {
                    padding: 0 0 base.unit-rem-calc(1px);
                    text-wrap: nowrap;

                    &.f-f-label-multiline {
                        display: flex;
                        flex-direction: row;
                        justify-content: center;
                        align-items: center;
                    }
                }
                .m-tooltip-info {
                    margin-left: base.unit-rem-calc(4px);
                }
            }
        }
        @include base.respond-to('<small') {
            flex-wrap: wrap;
        }
        &.t-email {
            grid-template-columns: repeat(2, 1fr);
            .f-field {
                grid-column: span 1;
                &:last-child {
                    grid-column: span 2;
                }
                @include base.respond-to('<small') {
                    grid-column: span 2;
                }
            }
        }
        &.t-projects {
            grid-template-columns: 1fr 1fr;
            .f-field {
                grid-column: span 1;
                @include base.respond-to('<small') {
                    grid-column: span 2;
                }
            }
        }
        &.t-project-summary {
            width: 75%;
            grid-template-columns: 1fr;
            @include base.respond-to('<medium') {
                width: 100%;
            }
            .f-field {
                grid-column: span 1;
                @include base.respond-to('<small') {
                    grid-column: span 2;
                }
            }
        }
        &.t-payments {
            grid-template-columns: 1fr 1fr;
            .f-field {
                grid-column: span 1;
                @include base.respond-to('<small') {
                    grid-column: span 2;
                }
            }
        }
        &.t-calendar {
            grid-template-columns: repeat(12, 1fr);
            .f-field {
                grid-column: span 4;
                @include base.respond-to('<small') {
                    grid-column: span 6;
                    &:first-child {
                        grid-column: span 12;
                    }
                    @include base.respond-to('<xsmall') {
                        grid-column: span 12;
                    }
                }
            }
        }
    }
    .c-csf-wrapper {

    }
    .c-csf-bid-defaults {
    }
    .c-csf-title {
        @include base.typo-header($size: 14px, $line-height: base.unit-rem-calc(32px));
        &.t-info {
            display: flex;
            align-items: center;
            gap: base.unit-rem-calc(5px);
        }
    }
}

/* Company Settings Projects tab */
.m-company-settings {
    width: 100%;
    height: calc(100% - 58px);
    padding: base.unit-rem-calc(24px) base.unit-rem-calc(24px) base.unit-rem-calc(24px);
    overflow: auto;
    @include base.respond-to('<small') {
        padding: base.unit-rem-calc(32px) base.unit-rem-calc(16px) base.unit-rem-calc(40px);
    }

    .c-cs-wrapper {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(base.unit-rem-calc(224px), 1fr));
        row-gap: base.unit-rem-calc(32px);
        column-gap: base.unit-rem-calc(16px);
        background: base.$color-background-form;
        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        border-radius: base.unit-rem-calc(8px);
        padding: base.unit-rem-calc(12px);
        margin-bottom: base.unit-rem-calc(32px);
        overflow: auto;
    }

        .c-csw-content {
            display: flex;
            flex-direction: column;

            &.t-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 1rem;
            }

            &.t-space-between {
                flex-direction: row;
                justify-content: space-between;
            }
        }

            .cswc-field-title {
                @include base.typo-paragraph;
                color: base.$color-grey-dark-2;
                font-weight: 500;
                &.t-inactive {
                    margin: 0;
                    color: base.$color-grey-dark-1;
                }
            }
                .c-cswcf-item {
                    &.t-hidden {
                        display: none;
                    }
                }
            .c-cswc-title {
                @include base.typo-paragraph-medium;
                width: max-content;
                display: flex;
                align-items: center;
                gap: base.unit-rem-calc(4px);
                margin-bottom: 0;
            }
            .c-cswc-subtitle {
                @include base.typo-paragraph;
                color: base.$color-grey-dark-2;
                font-style: italic;
                margin-bottom: base.unit-rem-calc(8px);
            }
            .c-cswc-content {
                @include base.typo-paragraph;
                color: base.$color-grey-dark-1;
                .h-text {
                    padding: base.unit-rem-calc(2px) base.unit-rem-calc(16px);
                    margin-top: base.unit-rem-calc(4px);
                }
                &.t-link {
                    margin-top: base.unit-rem-calc(16px);
                    @include base.typo-paragraph-small;
                }
                &.t-project-summary {
                    white-space: pre-line;
                }
                &.t-hidden {
                    display: none;
                }
            }

    .c-cs-content {
        &.t-hidden {
            display: none;
        }
    }

}

/* Leads website form settings*/

.m-leads-settings {
    padding-bottom: base.unit-rem-calc(32px);

    .leads-api-section {
        margin-top: 2em;

        .api-key-wrapper {
            display: flex;
            flex-direction: row-reverse;
            gap: 1em;
        }
    }

    .c-wswc-lfs {
        padding: 0 base.unit-rem-calc(4px);

        .lfs-title {
            display: flex;
            gap: base.unit-rem-calc(8px);
            margin-bottom: base.unit-rem-calc(8px);
            font-size: base.unit-rem-calc(20px);
        }

        .lfs-subtitle {
            @include base.typo-paragraph-small;
            color: base.$color-grey-dark-1;
        }

        .c-wswc-lfs-main, .c-wswc-lfs-emails {
            .cswc-field-title {
                margin-bottom: base.unit-rem-calc(6px);
            }
        }

        .c-wswc-lfs-main {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: base.unit-rem-calc(16px);

            //#google_tag_id_err, #additional_email_recipients_err { margin-top: base.unit-rem-calc(12px) };
        }

        .c-wswc-lfs-emails {
            margin-top: base.unit-rem-calc(16px);
        }

        .c-wswc-lfs-settings {
            margin-top: base.unit-rem-calc(12px);
        }
    }


    .lead-form-settings {
        @include base.respond-to('>medium') {
            display: flex;
        }

        .sidebar {
            min-width: 300px;

            .input-fields {
                margin-bottom: base.unit-rem-calc(8px);
                display: flex;
                flex-direction: column;
                gap: base.unit-rem-calc(8px);


                .cswc-field-title {
                    margin-bottom: base.unit-rem-calc(6px);
                }
            }

            .optional-inputs {
                margin: base.unit-rem-calc(16px) 0;
                background: base.$color-background-form;
                border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                border-radius: base.unit-rem-calc(8px);
                padding: base.unit-rem-calc(12px);
                margin-bottom: base.unit-rem-calc(32px);
                overflow: auto;

                .cswc-field-subtitle {
                    font-size: base.unit-rem-calc(12px);
                    margin-bottom: base.unit-rem-calc(6px);
                }

                .c-cswc-group {
                    display: flex;
                    gap: base.unit-rem-calc(8px);
                }

                .c-cswc-group-field {
                    background: base.$color-white-default;
                    border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                    border-radius: base.unit-rem-calc(8px);
                    padding: base.unit-rem-calc(8px) base.unit-rem-calc(12px);
                    margin-bottom: base.unit-rem-calc(12px);
                    overflow: auto;
                    width: 100%;

                    .dropdown-tooltip {
                        display: flex;
                        align-content: center;
                        justify-content: center;
                        align-items: center;
                        gap: 0.5em;
                    }

                    .padding {
                        padding-bottom: base.unit-rem-calc(8px);
                    }
                    .padding:last-of-type {
                        padding-bottom: 6px;
                    }
                }


            }

        }
    }

    .c-scg-button {
        display: flex;
        justify-content: space-between;

        .c-scg-input {
            width: 90%;
        }
    }

    .f-field {
        &.t-flex {
            display: flex;
            align-items: center;
            gap: base.unit-rem-calc(8px);
        }
        &.t-no-margin {
            margin: 0;
        }
    }

    .t-section-flex {
        display:flex;
        justify-content: space-between;
    }

    .c-cs-preview-btn {
        @include base.button-text-icon-tertiary;
    }
}

/* Project types and result types Table */
.m-project-types, .m-result-types {
    width: 100%;
    margin-bottom: base.unit-rem-calc(32px);
    .c-lt-title {
        @include base.typo-header($size: 14px, $line-height: base.unit-rem-calc(32px));
        margin-bottom: 0;
        &.t-info {
            display: flex;
            align-items: center;
            gap: base.unit-rem-calc(5px);
        }
    }
    .c-lt-content {
        @include base.typo-paragraph;
        margin-bottom: base.unit-rem-calc(16px);
        @include base.respond-to('<medium') {
            padding: 0 base.unit-rem-calc(8px);
        }
    }
    .c-lt-table {
        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        border-radius: base.unit-rem-calc(8px);
    }
    .c-ltt-header {
        display: flex;
        align-items: center;
        border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        height: base.unit-rem-calc(48px);
        gap: base.unit-rem-calc(16px);
        .f-field {
            &.t-name {
                width: 90%;
                padding-left: base.unit-rem-calc(16px);
            }
            &.t-status {
                width: 10%;}
        }
        @include base.respond-to('<small') {
            display: none;
        }
    }
    .c-ltt-body {
        display: flex;
        flex-direction: column;
        overflow: auto;
    }
    .c-lttb-row {
        display: flex;
        align-items: center;
        gap: base.unit-rem-calc(16px);
        height: base.unit-rem-calc(48px);
        border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        &:last-child {
            border: none;
        }
        @include base.respond-to('<xsmall') {
            flex-direction: column;
            align-items: flex-start;
            padding: base.unit-rem-calc(8px) base.unit-rem-calc(12px);
            gap: base.unit-rem-calc(8px);
            height: auto;
            min-width: base.unit-rem-calc(256px);
        }
        .f-field {
            .f-f-label {
                display: none;
                @include base.respond-to('<xsmall') {
                    display: inline-flex;
                    align-items: center;
                    gap: base.unit-rem-calc(4px);
                }
            }
        }
        .c-cst-footer {
            display: flex;
            justify-content: flex-end;
            padding: base.unit-rem-calc(16px) 0;
        }
        .c-cstf-info {
            @include base.callout-info;
            width: 85%;
            display: none;
        }
        .c-cstf-add {
            @include base.button-text-icon-tertiary;
            padding-right: base.unit-rem-calc(7px) !important;
        }
    }
    .c-lttbr-item {
        @include base.respond-to('<xsmall') {
            align-items: center;
            height: auto;
            min-height: base.unit-rem-calc(24px);
            gap: base.unit-rem-calc(8px);
            .f-field {
                width: auto;
            }
            .f-f-label {
                white-space: nowrap;
            }
            &> p {
                text-align: right;
            }
        }
        &.t-name {
            flex: 1;
            padding-left: base.unit-rem-calc(16px);
            @include base.respond-to('<xsmall') {
                display: flex;
                width: 100%;
                padding: 0;
            }
        }
        &.t-status {
            margin-right: base.unit-rem-calc(8px);
            @include base.respond-to('<xsmall') {
                display: flex;
                justify-content: space-between;
                width: 100%;
                padding: 0;
            }
        }
        p {
            @include base.typo-paragraph;
            margin-bottom: 0;
            display: flex;
            align-items: center;
            gap: base.unit-rem-calc(4px);
        }
        .f-field {
            .f-f-label {
                display: none;
                @include base.respond-to('<xsmall') {
                    display: inline-flex;
                    align-items: center;
                    gap: base.unit-rem-calc(4px);
                }
            }
        }
    }
    .c-lttbr-line {
        @include base.respond-to('<xsmall') {
            flex: 1;
            min-width: base.unit-rem-calc(16px);
            border-bottom: base.unit-rem-calc(1px) dashed base.$color-grey-light-4;
        }
    }
    .c-ltt-footer {
        display: flex;
        justify-content: flex-end;
        padding: 0 base.unit-rem-calc(8px) base.unit-rem-calc(16px);
        @include base.respond-to('<xsmall') {
            padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px);
            border-top: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
        }
    }
    .c-lttf-add {
        @include base.button-text-icon-tertiary;
        padding-right: base.unit-rem-calc(7px) !important;
    }
    &.t-edit {
        padding: 0;
        border-radius: base.unit-rem-calc(8px);
        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        background: base.$color-background-edit;
        margin-bottom: base.unit-rem-calc(8px);
        height: auto;
        overflow: initial;
        .c-ltt-title {
            @include base.typo-header($size: 20px, $line-height: base.unit-rem-calc(32px));
        }
        .c-ltt-header {
            height: base.unit-rem-calc(32px);
            padding-left: base.unit-rem-calc(16px);
            padding-right: base.unit-rem-calc(8px);
            margin-top: base.unit-rem-calc(8px);
            .f-field {
                &.t-name {
                    flex: 1;
                    padding-left: 0;
                }
                &.t-status {
                    width: base.unit-rem-calc(36px);
                }
                &.t-remove {
                    width: base.unit-rem-calc(32px);
                    margin-left: base.unit-rem-calc(-8px);
                }
            }
        }
        .c-ltt-body {
            gap: base.unit-rem-calc(16px);
            padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px) base.unit-rem-calc(16px) base.unit-rem-calc(16px);
            @include base.respond-to('<xsmall') {
                padding: 0;
                gap: 0;
            }
        }
        .c-lttb-row {
            height: base.unit-rem-calc(32px);
            border-bottom: none;
            .f-field {
                &.t-name {
                    flex: 1;
                }
                &.t-status {
                    width: base.unit-rem-calc(36px);
                    > span {
                        display: none;
                    }
                }
                &.t-remove {
                    width: base.unit-rem-calc(32px);
                    display: flex;
                    align-items: center;
                    flex-wrap: nowrap;
                    gap: base.unit-rem-calc(8px);
                    margin-left: base.unit-rem-calc(-8px);
                    @include base.respond-to('<xsmall') {
                        width: 100%;
                    }
                    .t-button-remove {
                        display: flex;
                        justify-content: center;
                        @include base.respond-to('<xsmall') {
                            width: 100%;
                            justify-content: flex-end;
                        }
                        .f-f-button-remove {
                            display: inline-flex;
                            > [data-icon] {
                                margin-right: base.unit-rem-calc(7px) !important;
                                @include base.respond-to('<xsmall') {
                                    margin-right: 0 !important;
                                }
                            }

                            > [data-text] {
                                display: none;
                                @include base.respond-to('<xsmall') {
                                    display: inline;
                                }
                            }
                        }
                    }
                }
            }
            @include base.respond-to('<xsmall') {
                display: grid;
                grid-template-columns: base.unit-rem-calc(36px) 1fr;
                height: auto;
                row-gap: base.unit-rem-calc(8px);
                column-gap: base.unit-rem-calc(16px);
                padding: base.unit-rem-calc(8px);
                border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                &:last-child {
                    border-bottom: none;
                }
                .f-field {
                    &.t-name {
                        grid-column: 2/3 ;
                    }
                    &.t-status {
                        grid-column: 1/2;
                        width: auto;
                        flex: 1;
                        > span {
                            display: block;
                            margin: base.unit-rem-calc(7px) base.unit-rem-calc(7px) base.unit-rem-calc(13px);
                        }
                    }
                    &.t-remove {
                        grid-column: 1/3;
                        margin-left: 0;
                        .t-button-remove {
                            justify-content: center;
                            .f-f-button-remove {
                                > svg {
                                    display: none;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/* Company Settings Reminder tab */
.m-reminder-settings {
    width: 100%;
    padding: base.unit-rem-calc(24px) base.unit-rem-calc(24px) base.unit-rem-calc(24px);
    height: calc(100% - 58px);
    overflow: auto;
    @include base.respond-to("<small") {
        padding: base.unit-rem-calc(32px) base.unit-rem-calc(16px) base.unit-rem-calc(40px);
    }
    &.t-edit {
        padding: 0;
        height: auto;
        overflow: visible;
        @include base.respond-to('<medium') {
            padding: 0;
        }
        .c-rs-table {
            border-radius: base.unit-rem-calc(8px);
            border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            background: base.$color-background-edit;
            margin-bottom: base.unit-rem-calc(8px);
        }
            // edit state
            .c-rst-body {
                column-gap: base.unit-rem-calc(16px);
                row-gap: base.unit-rem-calc(16px);
                padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px);
                @include base.respond-to('<small') {
                    padding: 0;
                    gap: 0;
                    border-top: none;
                }
                .c-rstb-row {
                    height: base.unit-rem-calc(32px);
                    border-bottom: none;
                    .f-field {
                        &.t-enabled {
                            > span {
                                display: none;
                            }
                        }
                    }
                    @include base.respond-to('<small') {
                        display: grid;
                        grid-template-columns: base.unit-rem-calc(36px) 1fr;
                        row-gap: base.unit-rem-calc(8px);
                        height: auto;
                        padding: base.unit-rem-calc(8px);
                        border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                        &:last-child {
                            border-bottom: none;
                        }
                        .f-field {
                            &.t-enabled {
                                width: base.unit-rem-calc(36px);
                                flex-direction: column;
                                grid-column: 1/2;
                                gap: base.unit-rem-calc(6px);
                                > span {
                                    display: block;
                                    margin: base.unit-rem-calc(7px);
                                }
                            }
                            &.t-email {
                                grid-column: 2/3;
                                margin-left: 0;
                                .f-f-label {
                                    width: 100%;
                                }
                            }
                            &.t-days {
                                grid-column: 1/3;
                                .f-f-label {
                                    width: 100%;
                                }
                            }
                            &.t-remove {
                                grid-column: 1/3;
                                margin-left: 0;
                                .t-button-remove {
                                    justify-content: center;
                                    .f-f-button-remove {
                                        > svg {
                                            display: none;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
    }
    .c-rs-table {
        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        border-radius: base.unit-rem-calc(8px);
        margin-bottom: base.unit-rem-calc(32px);
    }
        .c-rst-header {
            display: flex;
            align-items: center;
            gap: base.unit-rem-calc(16px);
            border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            height: base.unit-rem-calc(48px);
            .f-field {
                &.t-name {
                    width: 40%;
                    padding-left: base.unit-rem-calc(16px);
                }
                &.t-email {
                    width: 70%;
                    margin-left: base.unit-rem-calc(-8px);
                }
                &.t-configured-emails {
                    width: 35%;
                }
                &.t-days {
                    width: 20%;
                    min-width: base.unit-rem-calc(176px);
                }
                &.t-status {
                    flex: 1;
                    min-width: base.unit-rem-calc(96px);
                }
                &.t-enabled {
                    display: flex;
                    justify-content: center;
                    min-width: base.unit-rem-calc(48px);
                }
                &.t-edit {
                    min-width: base.unit-rem-calc(48px);
                }
                &.t-remove {
                    min-width: base.unit-rem-calc(32px);
                }
            }
            @include base.respond-to('<small') {
                display: none;
            }
        }
        .c-rst-body {
            display: flex;
            flex-direction: column;
            overflow: auto;
            @include base.respond-to('<small') {
                padding: 0;
                gap: 0;
            }
        }
            .c-rstb-row {
                display: flex;
                align-items: center;
                column-gap: base.unit-rem-calc(16px);
                row-gap: base.unit-rem-calc(16px);
                height: base.unit-rem-calc(48px);
                border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                &:last-child {
                    border: none;
                }
                @include base.respond-to('<small') {
                    flex-direction: column;
                    align-items: flex-start;
                    padding: base.unit-rem-calc(8px) base.unit-rem-calc(12px);
                    row-gap: base.unit-rem-calc(8px);
                    height: auto;
                    min-width: base.unit-rem-calc(256px);
                }
                .f-field {
                    width: 25%;
                    @include base.respond-to('<small') {
                        width: 100%;
                    }
                    &.t-email {
                        width: 70%;
                        margin-left: base.unit-rem-calc(-8px);
                        @include base.respond-to('<small') {
                            width: 100%;
                            padding: 0;
                            margin-left: base.unit-rem-calc(-4px);
                        }
                    }
                    &.t-days {
                        width: 20%;
                        min-width: base.unit-rem-calc(176px);
                        @include base.respond-to('<small') {
                            width: 100%;
                        }
                    }
                    &.t-enabled {
                        width: base.unit-rem-calc(36px);
                        margin-left: base.unit-rem-calc(4px);
                        @include base.respond-to('<small') {
                            width: 100%;
                            display: flex;
                            align-items: center;
                        }
                    }
                    &.t-remove {
                        width: base.unit-rem-calc(32px);
                        display: flex;
                        align-items: center;
                        flex-wrap: nowrap;
                        gap: base.unit-rem-calc(8px);
                        margin-left: base.unit-rem-calc(-8px);
                        @include base.respond-to('<small') {
                            width: 100%;
                        }
                        .t-button-remove {
                            display: flex;
                            justify-content: center;
                            @include base.respond-to('<small') {
                                width: 100%;
                                justify-content: flex-end;
                            }
                            .f-f-button-remove {
                                display: inline-flex;
                                > [data-icon] {
                                    margin-right: base.unit-rem-calc(7px) !important;
                                    @include base.respond-to('<small') {
                                        margin-right: 0 !important;
                                    }
                                }

                                > [data-text] {
                                    display: none;
                                    @include base.respond-to('<small') {
                                        display: inline;
                                    }
                                }
                            }
                        }
                    }
                    .f-f-label {
                        display: none;
                        @include base.respond-to('<small') {
                            display: inline-flex;
                            align-items: center;
                        }
                    }
                }
            }
                .c-rstbr-item {
                    @include base.respond-to('<small') {
                        align-items: center;
                        height: base.unit-rem-calc(24px);
                        gap: base.unit-rem-calc(16px);
                        .f-field {
                            width: auto;
                        }
                        &> p {
                            white-space: nowrap;
                        }
                        .h-text {
                            padding: base.unit-rem-calc(2px) base.unit-rem-calc(16px);
                        }
                    }
                    &.t-name {
                        width: 40%;
                        padding-left: base.unit-rem-calc(16px);
                        @include base.respond-to('<small') {
                            display: flex;
                            width: 100%;
                            padding: 0;
                        }
                    }
                    &.t-configured-emails {
                        width: 35%;
                        @include base.respond-to('<small') {
                            display: flex;
                            width: 100%;
                            padding: 0;
                        }
                    }
                    &.t-status {
                        flex: 1;
                        min-width: base.unit-rem-calc(96px);
                        @include base.respond-to('<small') {
                            display: flex;
                            justify-content: space-between;
                            width: 100%;
                            padding: 0;
                        }
                    }
                    &.t-edit {
                        display: flex;
                        justify-content: end;
                        min-width: base.unit-rem-calc(48px);
                        @include base.respond-to('<small') {
                            display: flex;
                            height: base.unit-rem-calc(48px);
                            width: 100%;
                            padding: 0;
                        }
                    }
                    p {
                        @include base.typo-paragraph;
                        margin-bottom: 0;
                        display: flex;
                        align-items: center;
                        gap: base.unit-rem-calc(4px);
                        @include base.respond-to('<small') {
                            flex-direction: row-reverse;
                        }
                    }
                }
                    .c-rstbri-button {
                        @include base.button-icon-tertiary;
                        width: base.unit-rem-calc(48px);
                        @include base.respond-to('<small') {
                            width: auto;
                        }
                        .t-desktop {
                            display: inline-flex;
                            margin-left: 0;
                            @include base.respond-to('<small') {
                                display: none;
                            }
                        }
                        .t-mobile {
                            display: none;
                            @include base.respond-to('<small') {
                                display: inline-flex;
                                margin-right: base.unit-rem-calc(7px);
                            }
                        }
                    }
                .c-rstbr-line {
                    @include base.respond-to('<small') {
                        flex: 1;
                        min-width: base.unit-rem-calc(16px);
                        border-bottom: base.unit-rem-calc(1px) dashed base.$color-grey-light-4;
                    }
                }
        .c-rst-footer {
            display: flex;
            justify-content: flex-end;
            padding: 0 base.unit-rem-calc(8px) base.unit-rem-calc(16px);
            @include base.respond-to('<small') {
                padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px);
                border-top: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            }
            &.t-info {
                justify-content: space-between;
                .c-rstf-info {
                    display: block;
                }
                @include base.respond-to('<medium') {
                    justify-content: flex-end;
                    flex-wrap: wrap;
                    .c-rstf-info {
                        width: 100%;
                    }
                }
            }
        }
            .c-rstf-info {
                @include base.callout-info;
                width: 85%;
                display: none;
                margin: 0 base.unit-rem-calc(8px);
            }
            .c-rstf-add {
                @include base.button-text-icon-tertiary;
                padding-right: base.unit-rem-calc(7px) !important;
            }
}

/* This class and all of it's children will be relocated in the future */
.m-bid-defaults {
    width: 100%;
    height: calc(100% - 58px);
    padding: base.unit-rem-calc(24px) base.unit-rem-calc(24px) base.unit-rem-calc(24px);
    overflow: auto;
    @include base.respond-to('<small') {
        padding: base.unit-rem-calc(32px) base.unit-rem-calc(16px) base.unit-rem-calc(40px);
    }
    &.t-edit {
        padding: 0;
        border-radius: base.unit-rem-calc(8px);
        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        background: base.$color-background-edit;
        height: auto;
        overflow: initial;
        .c-bdt-title {
            @include base.typo-header($size: 20px, $line-height: base.unit-rem-calc(32px));
        }
        .c-bdt-header {
            height: base.unit-rem-calc(32px);
            padding-left: base.unit-rem-calc(16px);
            padding-right: base.unit-rem-calc(8px);
            margin-top: base.unit-rem-calc(8px);
            .f-field {
                &.t-name {
                    flex: 1;
                }
                &.t-payment {
                    flex: 1;
                }
                &.t-type {
                    width: base.unit-rem-calc(52px);
                }
                &.t-amount {
                    width: base.unit-rem-calc(112px);
                }
                &.t-remove {
                    width: base.unit-rem-calc(32px);
                    margin-left: base.unit-rem-calc(-8px);
                }
            }
        }
        .c-bdt-body {
            gap: base.unit-rem-calc(16px);
            padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px) base.unit-rem-calc(16px) base.unit-rem-calc(16px);
            @include base.respond-to('<small') {
                padding: 0;
                gap: 0;
            }
        }
        .c-bdtb-row {
            height: base.unit-rem-calc(32px);
            border-bottom: none;
            .f-field {
                &.t-name {
                    flex: 1;
                }
                &.t-payment {
                    flex: 1;
                }
                &.t-type {
                    &.f-field {
                        .f-f-button-group {
                            margin-left: base.unit-rem-calc(-8px);
                            @include base.respond-to('<small') {
                                margin-left: 0;
                            }
                            .f-fbg-button {
                                width: base.unit-rem-calc(26px);
                                @include base.respond-to('<small') {
                                    width: base.unit-rem-calc(26px);
                                    height: base.unit-rem-calc(26px);
                                }
                            }
                        }
                    }
                }
                &.t-amount {
                    width: base.unit-rem-calc(112px);
                }
                &.t-remove {
                    width: base.unit-rem-calc(32px);
                    display: flex;
                    align-items: center;
                    flex-wrap: nowrap;
                    gap: base.unit-rem-calc(8px);
                    margin-left: base.unit-rem-calc(-8px);
                    @include base.respond-to('<small') {
                        width: 100%;
                    }
                    .t-button-remove {
                        display: flex;
                        justify-content: center;
                        @include base.respond-to('<small') {
                            width: 100%;
                            justify-content: flex-end;
                        }
                        .f-f-button-remove {
                            display: inline-flex;
                            > [data-icon] {
                                margin-right: base.unit-rem-calc(7px) !important;
                                @include base.respond-to('<small') {
                                    margin-right: 0 !important;
                                }
                            }

                            > [data-text] {
                                display: none;
                                @include base.respond-to('<small') {
                                    display: inline;
                                }
                            }
                        }
                    }
                }
            }
            @include base.respond-to('<small') {
                display: grid;
                grid-template-columns: base.unit-rem-calc(58px) 1fr;
                height: auto;
                column-gap: base.unit-rem-calc(16px);
                row-gap: base.unit-rem-calc(8px);
                padding: base.unit-rem-calc(8px);
                border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                &:last-child {
                    border-bottom: none;
                }
                .f-field {
                    &.t-name {
                        grid-column: 1/3;
                    }
                    &.t-payment {
                        grid-column: 1/3;
                    }
                    &.t-type {
                        grid-column: 1/2;
                    }
                    &.t-amount {
                        grid-column: 2/3;
                        width: auto;
                        flex: 1;
                    }
                    &.t-remove {
                        grid-column: 1/3;
                        margin-left: 0;
                        .t-button-remove {
                            justify-content: center;
                            .f-f-button-remove {
                                > svg {
                                    display: none;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    .c-bd-wrapper {
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        gap: base.unit-rem-calc(4px);
        background: base.$color-background-form;
        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        border-radius: base.unit-rem-calc(8px);
        padding: base.unit-rem-calc(12px);
        overflow: auto;
        margin-bottom: base.unit-rem-calc(32px);
    }
        .c-bdw-title {
            @include base.typo-paragraph-medium;
            text-wrap: nowrap;
        }
        .c-bdw-content {
            @include base.typo-paragraph;
            &.t-bid-defaults {
                display: flex;
                align-items: center;
                background: base.$color-primary-light-1;
                border-radius: base.unit-rem-calc(32px);
                padding: 0 base.unit-rem-calc(16px);
                height: base.unit-rem-calc(24px);
                color: base.$color-white-default;
                width: max-content;
            }
        }
    .c-bd-title {
        @include base.typo-header($size: 14px, $line-height: base.unit-rem-calc(32px));
    }
    .c-bd-table {
        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        border-radius: base.unit-rem-calc(8px);
        margin-bottom: base.unit-rem-calc(32px);
    }
        .c-bdt-header {
            display: flex;
            align-items: center;
            border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            height: base.unit-rem-calc(48px);
            gap: base.unit-rem-calc(16px);
            .f-field {
                &.t-name {
                    width: 40%;
                    padding-left: base.unit-rem-calc(16px);
                }

                &.t-configured-emails {
                    width: 35%;
                }

                &.t-status {
                    flex: 1;
                    min-width: base.unit-rem-calc(96px);
                }
            }
            @include base.respond-to('<small') {
                display: none;
            }
        }
        .c-bdt-body {
            display: flex;
            flex-direction: column;
            overflow: auto;
        }
            &.t-edit {
                .c-bdtb-row {
                    min-width: base.unit-rem-calc(160px);
                }
            }
            .c-bdtb-row {
                display: flex;
                align-items: center;
                gap: base.unit-rem-calc(16px);
                height: base.unit-rem-calc(48px);
                border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                &:last-child {
                    border: none;
                }
                @include base.respond-to('<small') {
                    flex-direction: column;
                    align-items: flex-start;
                    padding: base.unit-rem-calc(8px) base.unit-rem-calc(12px);
                    gap: base.unit-rem-calc(8px);
                    height: auto;
                    min-width: base.unit-rem-calc(256px);
                }
                .f-field {
                    .f-f-label {
                        display: none;
                        @include base.respond-to('<small') {
                            display: inline-flex;
                            align-items: center;
                            gap: base.unit-rem-calc(4px);
                        }
                    }
                }
            }
                .c-bdtbr-item {
                    @include base.respond-to('<small') {
                        align-items: center;
                        height: auto;
                        min-height: base.unit-rem-calc(24px);
                        gap: base.unit-rem-calc(8px);
                        .f-field {
                            width: auto;
                        }
                            .f-f-label {
                                white-space: nowrap;
                            }
                        &> p {
                            text-align: right;
                        }
                    }
                    &.t-name {
                        width: 40%;
                        padding-left: base.unit-rem-calc(16px);
                        @include base.respond-to('<small') {
                            display: flex;
                            width: 100%;
                            padding: 0;
                        }
                    }
                    &.t-configured-emails {
                        width: 35%;
                        @include base.respond-to('<small') {
                            display: flex;
                            width: 100%;
                            padding: 0;
                        }
                    }
                    &.t-status {
                        flex: 1;
                        min-width: base.unit-rem-calc(96px);
                        @include base.respond-to('<small') {
                            display: flex;
                            justify-content: space-between;
                            width: 100%;
                            padding: 0;
                        }
                    }
                    p {
                        @include base.typo-paragraph;
                        margin-bottom: 0;
                        display: flex;
                        align-items: center;
                        gap: base.unit-rem-calc(4px);
                    }
                    .f-field {
                        .f-f-label {
                            display: none;
                            @include base.respond-to('<small') {
                                display: inline-flex;
                                align-items: center;
                                gap: base.unit-rem-calc(4px);
                            }
                        }
                    }
                }
                .c-bdtbr-line {
                    min-width: base.unit-rem-calc(16px);
                    @include base.respond-to('<small') {
                        flex: 1;
                        min-width: base.unit-rem-calc(16px);
                        border-bottom: base.unit-rem-calc(1px) dashed base.$color-grey-light-4;

                    }
                }
        .c-bdt-footer {
            display: flex;
            justify-content: flex-end;
            padding: 0 base.unit-rem-calc(8px) base.unit-rem-calc(16px);
            @include base.respond-to('<small') {
                padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px);
                border-top: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
            }
        }
            .c-bdtf-add {
                @include base.button-text-icon-tertiary;
                padding-right: base.unit-rem-calc(7px) !important;
            }
}

/* Business Hours Table */
.m-business-hours {
    width: 100%;
    //height: calc(100% - 58px);
    //padding: base.unit-rem-calc(24px) base.unit-rem-calc(24px) base.unit-rem-calc(24px);
    overflow: auto;
    @include base.respond-to('<medium') {
        padding: base.unit-rem-calc(32px) base.unit-rem-calc(16px) base.unit-rem-calc(40px);
    }
    &.t-edit {
        padding: 0;
        height: auto;
        overflow: initial;
        .c-bh-table {
            border-radius: base.unit-rem-calc(8px);
            border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            background: base.$color-background-edit;

            //margin-bottom: 0;
        }
        .c-bht-header {
            //height: base.unit-rem-calc(32px);
            //padding-left: base.unit-rem-calc(16px);
            //padding-right: base.unit-rem-calc(8px);
            //margin-top: base.unit-rem-calc(8px);
            .f-field {
                &.t-status {
                    width: base.unit-rem-calc(96px);
                    padding-left: base.unit-rem-calc(12px);
                }
            }
        }
        .c-bht-body {
            gap: base.unit-rem-calc(16px);
            padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px) base.unit-rem-calc(16px) base.unit-rem-calc(16px);
            @include base.respond-to('<medium') {
                padding: 0;
                gap: 0;
            }
        }
        .c-bhtb-row {
            height: base.unit-rem-calc(32px);
            border-bottom: none;
            padding: 0;
            .f-field {
                &.t-day {
                    flex: 1;
                }
                &.t-start {
                    width: 30%;
                }
                &.t-end {
                    width: 30%;
                }
                &.t-status {
                    width: base.unit-rem-calc(96px);
                    padding-left: base.unit-rem-calc(12px);
                }
            }
            @include base.respond-to('<medium') {
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                height: auto;
                padding: base.unit-rem-calc(16px) base.unit-rem-calc(16px) base.unit-rem-calc(24px) base.unit-rem-calc(16px);
                border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
                &:last-child {
                    border-bottom: none;
                }
                .f-field {
                    &.t-day {
                        width: 100%;
                        flex: unset;
                    }
                    &.t-start {
                        width: 49%;
                    }
                    &.t-end {
                        width: 49%;
                    }
                    &.t-status {
                        width: 100%;
                    }

                }
            }
        }
    }
    .c-bh-title {
        @include base.typo-header($size: 14px, $line-height: base.unit-rem-calc(32px));
        display: flex;
        align-items: center;
        gap: base.unit-rem-calc(4px);
    }
    .c-bh-table {
        border: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        border-radius: base.unit-rem-calc(8px);
        margin-bottom: base.unit-rem-calc(32px);
    }
        .c-bht-header {
            display: flex;
            align-items: center;
            border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
            height: base.unit-rem-calc(48px);
            gap: base.unit-rem-calc(16px);
            padding: 0 base.unit-rem-calc(8px) 0 base.unit-rem-calc(16px);
            .f-field {
                &.t-day {
                    flex: 1;
                    //padding-left: base.unit-rem-calc(16px);
                }
                &.t-start {
                    width: 30%;
                }
                &.t-end {
                    width: 30%;
                }
                &.t-status {
                    width: base.unit-rem-calc(96px);
                }
            }
            @include base.respond-to('<medium') {
                display: none;
            }
        }
    .c-bht-body {
        display: flex;
        flex-direction: column;
        overflow: auto;
    }
    //&.t-edit {
    //    .c-bdtb-row {
    //        min-width: base.unit-rem-calc(160px);
    //    }
    //}
    .c-bhtb-row {
        display: flex;
        align-items: center;
        gap: base.unit-rem-calc(16px);
        height: base.unit-rem-calc(48px);
        border-bottom: base.unit-rem-calc(1px) solid base.$color-grey-light-4;
        padding: 0 base.unit-rem-calc(8px) 0 base.unit-rem-calc(16px);
        &:last-child {
            border: none;
        }
        @include base.respond-to('<medium') {
            flex-direction: column;
            align-items: flex-start;
            padding: base.unit-rem-calc(8px) base.unit-rem-calc(12px);
            gap: base.unit-rem-calc(8px);
            height: auto;
            min-width: base.unit-rem-calc(256px);
        }
        .f-field {
            .f-f-label {
                display: none;
                @include base.respond-to('<medium') {
                    display: inline-flex;
                    align-items: center;
                    gap: base.unit-rem-calc(4px);
                }
            }
        }
    }
    .c-bhtbr-item {
        @include base.respond-to('<medium') {
            align-items: center;
            height: auto;
            min-height: base.unit-rem-calc(24px);
            gap: base.unit-rem-calc(8px);
            .f-field {
                width: auto;
            }
            .f-f-label {
                white-space: nowrap;
            }
            &> p {
                text-align: right;
            }
        }
        &.t-day {
            flex: 1;
            padding-left: base.unit-rem-calc(16px);
            @include base.respond-to('<medium') {
                display: flex;
                width: 100%;
                padding: 0;
            }
        }
        &.t-start {
            width: 25%;
            @include base.respond-to('<medium') {
                display: flex;
                width: 100%;
                padding: 0;
            }
        }
        &.t-end {
            width: 25%;
            @include base.respond-to('<medium') {
                display: flex;
                width: 100%;
                padding: 0;
            }
        }
        &.t-status {
            width: base.unit-rem-calc(96px);
            @include base.respond-to('<medium') {
                display: flex;
                justify-content: space-between;
                width: 100%;
                padding: 0;
            }
        }
        p {
            @include base.typo-paragraph;
            margin-bottom: 0;
            display: flex;
            align-items: center;
            gap: base.unit-rem-calc(4px);
        }
        .f-field {
            .f-f-label {
                display: none;
                @include base.respond-to('<medium') {
                    display: inline-flex;
                    align-items: center;
                    gap: base.unit-rem-calc(4px);
                }
            }
        }
    }
    .c-bhtbr-line {
        min-width: base.unit-rem-calc(16px);
        @include base.respond-to('<medium') {
            flex: 1;
            min-width: base.unit-rem-calc(16px);
            border-bottom: base.unit-rem-calc(1px) dashed base.$color-grey-light-4;

        }
    }
    .c-bht-footer {
        display: flex;
        justify-content: flex-end;
        padding: 0 base.unit-rem-calc(8px) base.unit-rem-calc(16px);
        @include base.respond-to('<medium') {
            padding: base.unit-rem-calc(16px) base.unit-rem-calc(8px);
            border-top: base.unit-rem-calc(1px) base.$color-grey-light-4 solid;
        }
    }
    .c-bhtf-add {
        @include base.button-text-icon-tertiary;
        padding-right: base.unit-rem-calc(7px) !important;
    }
}
