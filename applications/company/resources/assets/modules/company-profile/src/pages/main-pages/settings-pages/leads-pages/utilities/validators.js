'use strict';

const { findChild, jsSelector } = require('@ca-package/dom');
const { FIELD_NAMES } = require('./constants');

function isValidForm (root) {
    const check = [FIELD_NAMES.MARKETING_SOURCE, FIELD_NAMES.PROJECT_TYPE, FIELD_NAMES.CUSTOMER_NOTES];
    let valid = true;

    check.forEach(ref => {
        const visible  = findChild(root, jsSelector(`${ref}_visibility`)).is(':checked');
        const required = findChild(root, jsSelector(`${ref}_requirement`)).is(':checked');
        const $label   = findChild(root, jsSelector(`${ref}_label`));

        if (visible && required && !$label.val()) {
            $label.addClass('error');
            valid = false;
        } else {
            $label.removeClass('error');
        }
    });

    return valid;
}

function buildEntityPayload (root, id, validator, statusElem, settingsElem) {
    const { FIELD_NAMES, TYPES } = require('./constants'); // lazy to avoid cycle
    if (!statusElem.is(':checked')) return { is_active: false, fields: {} };

    const entity = {
        id,
        title: validator.getInputElem('title_label').val(),
        save_button_label: validator.getInputElem('save_button_label').val(),
        default_assigned_to_user_id: validator.getInputElem('default_assigned_to').val() !== '' ? parseInt(validator.getInputElem('default_assigned_to').val()) : null,
        google_tag_id: validator.getInputElem('google_tag_id').val(),
        additional_email_recipients: validator.getInputElem('additional_email_recipients').val(),
        is_active: true,
        fields: {}
    };

    if (settingsElem.is(':visible')) {
        Object.values(FIELD_NAMES).forEach(fname => {
            const vis   = findChild(root, jsSelector(`${fname}_visibility`)).is(':checked');
            const req   = findChild(root, jsSelector(`${fname}_requirement`)).is(':checked');
            const label = findChild(root, jsSelector(`${fname}_label`)).val();
            const instr = findChild(root, jsSelector(`${fname}_instruction_label`)).val();

            entity.fields[fname] = { is_enabled: vis, is_required: req };
            if (label)  entity.fields[fname].label       = label;
            if (instr)  entity.fields[fname].instruction = instr;

            if ([FIELD_NAMES.MARKETING_SOURCE, FIELD_NAMES.PROJECT_TYPE].includes(fname)) {
                const isDropdown = findChild(root, jsSelector(`${fname}_dropdown`)).is(':checked');
                entity.fields[fname].field_type = isDropdown ? TYPES.DROPDOWN : TYPES.FREEFORM;
            }
        });
    }

    return entity;
}

module.exports = { isValidForm, buildEntityPayload };