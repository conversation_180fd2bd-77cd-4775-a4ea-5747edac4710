"use strict";

const uuid4 = require("uuid/v4");

const Page = require("@ca-package/router/src/page");
const { findChild, jsSelector } = require("@ca-package/dom");

const import_tpl = require("@cam-company-profile-tpl/pages/main-pages/additional-costs-pages/import.hbs");

/**
 * Additional Costs Import Page
 *
 * Multistep wizard interface for importing additional costs using CSV files.
 * Steps include uploading, previewing, error review, and finalizing the import.
 */
class Import extends Page {
    /**
     * Initializes the Import page.
     *
     * @param {module:Router.Controller} router - Router controller instance.
     * @param {string} name - Route name.
     * @param {module:Router.Page|null} [parent=null] - Optional parent route.
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);

        Object.assign(this.state, {
            parent,
            csv_data: {},
            import_data: {},
            additional_costs_rules: {
                id_user_defined: {
                    maxlength: 100,
                    maxlengthMessage: "Invalid length - 100 chars. max",
                },
                name: {
                    required: true,
                    maxlength: 100,
                    maxlengthMessage: "Invalid length - 100 chars. max",
                },
                cost: {
                    required: true,
                    pattern: "^\\d+(\\.\\d{1,2})?$",
                    patternMessage: "Number must be formatted as a price.",
                },
                markup: {
                    pattern: "^\\d+(\\.\\d{1,2})?$",
                    patternMessage: "Number must be formatted as a percentage.",
                },
                unit: {
                    required: true,
                    maxlength: 20,
                    maxlengthMessage: "Invalid length - 20 chars. max",
                },
            },
        });
    }

    /**
     * Returns available sub-routes for the import steps.
     *
     * @returns {object} Routes object mapping import steps to their pages.
     */
    static get routes() {
        return {
            upload: {
                default: true,
                page: require("./import-pages/upload"),
            },
            preview: {
                path: "/preview",
                page: require("./import-pages/preview"),
            },
            review_errors: {
                path: "/review-errors",
                page: require("./import-pages/review_errors"),
            },
            final_review: {
                path: "/final-review",
                page: require("./import-pages/final_review"),
            },
        };
    }
    /**
     * Shows the loader to indicate processing.
     */
    startWorking() {
        this.elem.loader.show();
    }
    /**
     * Hides the loader to indicate processing has finished.
     */
    resetWorking() {
        this.elem.loader.hide();
    }
    /**
     * Sanitizes and normalizes an additional cost object by applying defaults and formatting numeric values.
     *
     * - Ensures `cost` and `markup` fields are valid decimal strings with two digits after the decimal point.
     * - Generates a new UUID if `id_user_defined` is missing.
     * - Applies default values for all missing or undefined fields.
     * - Guarantees return values are safe strings, ready for UI or backend operations.
     *
     * @param {Object} data - The raw additional cost data.
     * @param {string} [data.id_user_defined] - User-defined additional cost ID. If absent, a UUID is generated.
     * @param {string} [data.name] - The additional cost name.
     * @param {string|number} [data.cost] - The raw cost value, which may be invalid or unset.
     * @param {string|number} [data.markup] - The raw markup value, which may be invalid or unset.
     * @param {string} [data.unit] - The unit of measurement for the cost.
     *
     * @returns {Object} A sanitized and normalized additional cost object.
     * @returns {string} return.id_user_defined - Sanitized or generated user-defined ID (max 100 characters).
     * @returns {string} return.name - Sanitized additional cost name.
     * @returns {string} return.cost - Safe stringified cost with two decimal places, or empty string if unset.
     * @returns {string} return.markup - Safe stringified markup with two decimal places, or empty string if unset.
     * @returns {string} return.unit - Sanitized unit string.
     */
    safeAdditionalCostType(data) {
        const {
            id_user_defined: raw_id_user_defined = "",
            name = "",
            cost: raw_cost = "",
            markup: raw_markup = "",
            unit = "",
        } = data;

        let cost = raw_cost.replace('$', '');
        if (cost !== "" && cost !== null && cost !== undefined && !isNaN(Number(cost)) && Number.isFinite(Number(cost))) {
            const cost_value = Number(cost);
            cost = cost_value.toFixed(2);
        } else {
            cost = "";
        }

        let markup = raw_markup.replace('%', '');
        if (markup !== "" && markup !== null && markup !== undefined && !isNaN(Number(markup)) && Number.isFinite(Number(markup))) {
            const markup_value = Number(markup);
            markup = markup_value.toFixed(2);
        } else {
            markup = "";
        }

        let id_user_defined;
        if (!raw_id_user_defined) {
            id_user_defined = uuid4();
        } else {
            id_user_defined = String(raw_id_user_defined);
        }

        return {
            id_user_defined: String(id_user_defined ?? "").substring(0, 100),
            name: String(name ?? ""),
            cost: String(cost ?? ""),
            markup: String(markup ?? ""),
            unit: String(unit ?? ""),
        };
    }
    /**
     * Loads the import page and fetches necessary data
     *
     * @param {object} request - Request object.
     * @param {function} next - Callback after loading is complete.
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);
    }

    /**
     * Unloads the page and resets CSV-related state data.
     *
     * @param {object} request - Request object.
     * @param {function} next - Callback after unloading is complete.
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.state.csv_columns = null;
        this.state.csv_data = null;
        this.state.import_data = null;
        await super.unload(request, next);
    }

    /**
     * Initializes DOM elements for the import page.
     *
     * @param {jQuery} root - Root element for the page.
     */
    async boot(root) {
        super.boot(root);
        this.elem.page_container = root.fxFind(
            "additional-costs-import-container",
        );
        this.elem.loader = findChild(root, jsSelector("loader"));
    }

    /**
     * Renders the import page.
     *
     * @returns {string} HTML content rendered from the template.
     */
    render() {
        return import_tpl();
    }
}

module.exports = Import;
