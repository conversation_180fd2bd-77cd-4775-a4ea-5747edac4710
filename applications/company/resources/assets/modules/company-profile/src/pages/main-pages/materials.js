'use strict';

const Page = require('@ca-package/router/src/page');

const {MaterialAdd} = require('@cam-company-profile-js/pages/main-pages/materials-pages/add');
const {MaterialDelete} = require('@cam-company-profile-js/pages/main-pages/materials-pages/delete');

const materials_tpl = require('@cam-company-profile-tpl/pages/main-pages/materials.hbs');

class Materials extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
    };

    /**
     * Get available routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            add: {
                path: '/add',
                modal: MaterialAdd
            },
            delete: {
                path: '/delete/{material_id}',
                modal: MaterialDelete,
                bindings: {
                    material_id: 'uuid'
                }
            },
            items: {
                default: true,
                page: require('./materials-pages/items')
            },
            import: {
                path: '/import',
                page: require('./materials-pages/import')
            }
        };
    };

    /**
     * Show loader overlay
     *
     * @param {boolean} [show=true]
     */
    showLoader(show = true) {
        this.elem.loader.toggle(show);
    };

    /**
     * Hide loader overlay
     */
    hideLoader() {
        return this.showLoader(false);
    };



    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.loader = this.elem.root.fxFind('loader');
        this.elem.page_container = root.fxFind('page-container');
    };

    /**
     * Get container element for sub pages
     *
     * @returns {*}
     */
    getPageContainer() {
        return this.elem.page_container;
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return materials_tpl();
    };
}

module.exports = Materials;