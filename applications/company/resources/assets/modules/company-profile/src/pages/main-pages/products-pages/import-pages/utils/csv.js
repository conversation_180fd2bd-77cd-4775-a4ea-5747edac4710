const Papa = require("papaparse");

/**
 * Parses a CSV string into JSON objects, correctly handling
 * quoted fields, embedded newlines, etc.
 *
 * @param {string} csvString
 * @returns {Array<Object>}
 */
function csvToJson(csvString) {
    const { data, errors } = Papa.parse(csvString, {
        header: true,
        skipEmptyLines: true,
        dynamicTyping: false,
    });

    if (errors.length) {
        console.error("CSV parse errors:", errors);
    }
    return data;
}

/**
 * Extracts and returns the CSV column headers as an array of strings.
 *
 * @param {string} csv - The full CSV text.
 * @returns {string[]} - Array of header names.
 */
function csvColumns(csv) {
    // grab first non-empty line (to guard against leading newlines)
    const firstLine = csv.split("\n").find((line) => line.trim() !== "");

    if (!firstLine) {
        return [];
    }

    return firstLine
        .split(",")
        .map((header) => header.trim().replace(/"/g, ""));
}

/**
 * Remaps the keys of a single object according to a mapping.
 *
 * @param {Object.<string,string|null>} mapping
 *   An object whose keys are the desired output field names, and whose
 *   values are the input field names (or null to force a null).
 * @param {Object.<string,*>} input
 *   The original object.
 * @returns {Object.<string,*>}
 *   A new object with exactly the keys of `mapping`, each pulling
 *   from `input[mappedName]` or being `null`.
 */
function remapObject(mapping, input) {
    const output = {};
    for (const [outKey, inKey] of Object.entries(mapping)) {
        if (inKey == null) {
            // explicitly mapped to null
            output[outKey] = null;
        } else {
            // pull from input if present, else fall back to null
            output[outKey] = Object.prototype.hasOwnProperty.call(input, inKey)
                ? input[inKey]
                : null;
        }
    }
    return output;
}

module.exports = { csvColumns, csvToJson, remapObject};
