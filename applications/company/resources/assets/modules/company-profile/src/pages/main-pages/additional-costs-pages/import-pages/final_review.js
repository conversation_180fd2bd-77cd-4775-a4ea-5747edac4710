"use strict";

const accounting = require("accounting");

const Api = require("@ca-package/api");
const { findChild, jsSelector } = require("@ca-package/dom");

const {
    createSuccessMessage,
} = require("@cas-notification-toast-js/message/success");
const {
    createErrorMessage,
} = require("@cas-notification-toast-js/message/error");
const {
    validateRecords,
} = require("@cam-company-profile-js/pages/main-pages/products-pages/import-pages/utils/validation");

const FormInput = require("@ca-submodule/form-input");
const NumberInput = require("@ca-submodule/form-input/src/number");
FormInput.use(NumberInput);
FormInput.use(require("@ca-submodule/form-input/src/hidden_textarea"));

const BaseReview = require("@cam-company-profile-js/pages/main-pages/additional-costs-pages/import-pages/base_review");

const final_review_tpl = require("@cam-company-profile-tpl/pages/main-pages/additional-costs-pages/import-pages/final_review.hbs");

/**
 * FinalReview Page
 *
 * Fourth and final step of the additional costs import wizard.
 * Allows users to review validated records, edit or delete them, and submit the import.
 */
class FinalReview extends BaseReview {
    /**
     * Returns all imported additional costs as Array.
     */
    getAdditionalCosts() {
        return Array.from(this.state.parent.state.import_data?.values() || []);
    };

    /**
     * Returns the table columns configuration.
     */
    getTableColumns() {
        return {
            id: { label: "Id", visible: false },
            name: {
                label: "Name",
                value: (data) =>
                    data.name
                        ? this.state.table.trimColumn(data.name, 35, true, true)
                        : "",
            },
            cost: {
                label: "Cost",
                value: (data) =>
                    data.cost === undefined ||
                    data.cost === null ||
                    data.cost === ""
                        ? ""
                        : accounting.formatMoney(data.cost),
            },
            markup: {
                label: "Markup",
                value: (data) =>
                    data.markup === undefined ||
                    data.markup === null ||
                    data.markup === ""
                        ? ""
                        : `${accounting.formatNumber(data.markup)}%`,
            },
            unit: { label: "Unit" },
        };
    };

    /**
     * Returns the table items (additional costs) to be displayed.
     */
    getTableItems() {
        return this.getAdditionalCosts();
    };

    /**
     * Refreshes the table data and updates error and queued record counts.
     * If all errors are fixed, shows a success message and redirects to the final review.
     */
    reloadTable() {
        this.state.parent.state.import_data = validateRecords(
            this.state.parent.state.import_data,
            this.parent.state.additional_costs_rules,
        );
        const invalid_records = this.getInvalidAdditionalCosts();
        this.state.table.setTableData(this.getAdditionalCosts());
        this.elem.records_queued_count.text(
            this.state.parent.state.import_data?.size - invalid_records?.length,
        );
    };

    /**
     * Submits valid additional costs to the backend API for import.
     */
    importAdditionalCosts() {
        try {
            this.parent.startWorking();

            const validAdditionalCosts = [
                ...(this.parent.state.import_data?.values() || []),
            ].filter((record) => !record.error);

            if (!validAdditionalCosts || validAdditionalCosts.length === 0) {
                const message = createErrorMessage(
                    "No valid additional costs to import",
                );
                this.router.main_route.layout.toasts.addMessage(message);
                return;
            }

            const formattedAdditionalCosts = validAdditionalCosts.map(
                (additional_cost) => ({
                    id_user_defined: additional_cost.id_user_defined || "",
                    name: additional_cost.name,
                    cost: String(additional_cost.cost),
                    markup: String(additional_cost.markup),
                    unit: additional_cost.unit,
                }),
            );

            // Prepare data for API
            const data = {
                additional_costs: formattedAdditionalCosts,
            };

            // Send request to API using the Api client
            Api.Resources.ImportAdditionalCosts()
                .timeout(60)
                .store(data)
                .then((response) => {
                    const stats = response.data.load_stats || {};
                    const { inserted = 0, updated = 0, errored = 0 } = stats;

                    const parts = [];
                    if (inserted > 0) {
                        parts.push(`${inserted} additional cost(s) added`);
                    }
                    if (updated > 0) {
                        parts.push(`${updated} additional cost(s) updated`);
                    }
                    if (errored > 0) {
                        parts.push(`${errored} additional cost(s) with errors`);
                    }

                    this.router.navigate("additional_costs.items");

                    this.parent.resetWorking();

                    setTimeout(() => {
                        const message = createSuccessMessage(
                            `Import successfully completed: ${parts.join(", ")}`,
                        );
                        this.router.main_route.layout.toasts.addMessage(
                            message,
                        );
                    }, 500);
                })
                .catch(() => {
                    const errorMessage =
                        "Unexpected error occurred, please contact support.";
                    this.parent.resetWorking();

                    const message = createErrorMessage(errorMessage);
                    this.router.main_route.layout.toasts.addMessage(message);
                });
        } catch (error) {
            const message = createErrorMessage(
                "Unexpected error occurred, please contact support.",
            );
            console.error(error);
            this.router.main_route.layout.toasts.addMessage(message);
            this.parent.resetWorking();
        }
    };

    /**
     * Loads the final review page and initializes or redraws the additional costs table.
     */
    async load(request, next) {
        await super.load(request, next);

        if (!(this.state.parent.state.import_data?.size > 0)) {
            this.router.navigate("additional_costs.import.upload");
            return;
        }

        const invalid_records = this.getInvalidAdditionalCosts();
        this.elem.errors_count.text(invalid_records.length);
        this.elem.records_queued_count.text(
            this.state.parent.state.import_data?.size - invalid_records?.length,
        );
        this.state.child_row_open = false;
        if (!this.state.table_loaded) {
            this.createTable();
        } else {
            this.state.table.draw();
        }
    };

    /**
     * Boot page
     *
     * @param {jQuery} root - Root DOM element.
     */
    boot(root) {
        super.boot(root);
        this.elem.table = findChild(root, jsSelector("final-review-table"));
        this.elem.errors_count = findChild(root, jsSelector("errors-count"));
        this.elem.records_queued_count = findChild(
            root,
            jsSelector("records-queued-count"),
        );
        this.elem.import_button = findChild(root, jsSelector("import-button"));

        this.elem.import_button.on("click", () => {
            this.importAdditionalCosts();
        });
    };

    /**
     * Renders the final review HTML template.
     */
    render() {
        return final_review_tpl({
            cancel_route: "additional_costs.import.upload",
            finish_route: "additional_costs.import.finish",
        });
    };
}

module.exports = FinalReview;
