"use strict";

const uuid4 = require('uuid/v4');

const Page = require("@ca-package/router/src/page");
const { findChild, jsSelector } = require("@ca-package/dom");

const import_tpl = require("@cam-company-profile-tpl/pages/main-pages/products-pages/import.hbs");

/**
 * Product Import Page
 *
 * Multistep wizard interface for importing products using CSV files.
 * Steps include uploading, previewing, error review, and finalizing the import.
 */
class Import extends Page {
    /**
     * Initializes the Import page.
     *
     * @param {module:Router.Controller} router - Router controller instance.
     * @param {string} name - Route name.
     * @param {module:Router.Page|null} [parent=null] - Optional parent route.
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);

        Object.assign(this.state, {
            parent,
            csv_data: {},
            import_data: {},
            product_rules: {
                id_user_defined: {
                    maxlength: 100,
                    maxlengthMessage: "Invalid length - 100 chars. max",
                },
                name: {
                    required: true,
                    maxlength: 100,
                    maxlengthMessage: "Invalid length - 100 chars. max",
                },
                price: {
                    required: true,
                    pattern: "^\\d+(\\.\\d{1,2})?$",
                    patternMessage: "Number must be formatted as a price.",
                },
                unit: {
                    required: true,
                    maxlength: 100,
                    maxlengthMessage: "Invalid length - 100 chars. max",
                },
                description: {
                    maxlength: 1000,
                    maxlengthMessage: "Invalid length - 1000 chars. max",
                },
                pricing_disclaimer: {
                    maxlength: 500,
                    maxlengthMessage: "Invalid length - 500 chars. max",
                },
                category: {
                    commaSeparatedMaxlength: 200,
                },
            },
        });
    };

    /**
     * Loads the import page and fetches necessary data (e.g. product units).
     *
     * @param {object} request - Request object.
     * @param {function} next - Callback after loading is complete.
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);
    };

    /**
     * Unloads the page and resets CSV-related state data.
     *
     * @param {object} request - Request object.
     * @param {function} next - Callback after unloading is complete.
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.state.csv_columns = null;
        this.state.csv_data = null;
        this.state.import_data = null;
        await super.unload(request, next);
    };

    /**
     * Initializes DOM elements for the import page.
     *
     * @param {jQuery} root - Root element for the page.
     */
    async boot(root) {
        super.boot(root);
        this.elem.page_container = root.fxFind("import-container");
        this.elem.loader = findChild(root, jsSelector("loader"));
    };

    /**
     * Renders the import page.
     *
     * @returns {string} HTML content rendered from the template.
     */
    render() {
        return import_tpl();
    };

    /**
     * Returns available sub-routes for the import steps.
     *
     * @returns {object} Routes object mapping import steps to their pages.
     */
    static get routes() {
        return {
            upload: {
                default: true,
                page: require("./import-pages/upload"),
            },
            preview: {
                path: "/preview",
                page: require("./import-pages/preview"),
            },
            review_errors: {
                path: "/review-errors",
                page: require("./import-pages/review_errors"),
            },
            final_review: {
                path: "/final-review",
                page: require("./import-pages/final_review"),
            },
        };
    };

    /**
     * Shows the loader to indicate processing.
     */
    startWorking() {
        this.elem.loader.show();
    };

    /**
     * Hides the loader to indicate processing has finished.
     */
    resetWorking() {
        this.elem.loader.hide();
    };


     /**
     * Sanitizes and normalizes a product object by ensuring default values and safe numeric formatting.
     *
     * - Converts the `price` field to a valid decimal string with two digits after the decimal point.
     * - Applies default values for missing or undefined fields.
     * - Ensures returned fields are consistent and safe to use in UI or backend operations.
     *
     * @param {Object} data - The raw product data.
     * @param {string} [data.id_user_defined] - user-defined product ID.
     * @param {string} [data.name] - The product name.
     * @param {string} [data.description] - The product description.
     * @param {string|number} [data.price] - The product price, possibly unvalidated.
     * @param {string} [data.unit] - The unit of measure for the product.
     * @param {string} [data.pricing_disclaimer] - Pricing type or method.
     * @param {string} [data.category] - The product category.
     *
     * @returns {Object} A sanitized product object with safe string and price values.
     * @returns {string} return.id_user_defined - Sanitized user-defined ID.
     * @returns {string} return.name - Sanitized product name.
     * @returns {string} return.description - Sanitized product description.
     * @returns {string} return.price - Safe price string with two decimal places.
     * @returns {string} return.unit - Sanitized unit of measure.
     * @returns {string} return.pricing_disclaimer - Sanitized pricing_disclaimer type.
     * @returns {string} return.category - Sanitized product category.
     */
    safeProductType(data) {
        const {
            id_user_defined: raw_id_user_defined = "",
            name = "",
            description = "",
            price: raw_price = "",
            unit = "",
            pricing_disclaimer = "",
            category = "",
        } = data;

         let price = raw_price.replace('$', '');
         if (price !== "" && price !== null && price !== undefined && !isNaN(Number(price)) && Number.isFinite(Number(price))) {
             const price_value = Number(price);
             price = price_value.toFixed(2);
         } else {
             price = "";
         }

        let id_user_defined;
        if (!raw_id_user_defined) {
            id_user_defined = uuid4();
        } else {
            id_user_defined = String(raw_id_user_defined);
        }

         return {
             id_user_defined: String(id_user_defined ?? "").substring(0, 100),
             name: this.removeFormulaEscapeChar(String(name ?? "")),
             description: this.removeFormulaEscapeChar(String(description ?? "")),
             price: String(price ?? ""),
             unit: this.removeFormulaEscapeChar(String(unit ?? "")),
             pricing_disclaimer: this.removeFormulaEscapeChar(String(pricing_disclaimer ?? "")),
             category: this.removeFormulaEscapeChar(String(category ?? "")),
         }
    };

    /**
     * Removes Excel formula escape characters from the beginning of a string.
     * Remove the apostrophe (') from the beginning of the string when the second char is - or =
     *
     * @param {string} str - The string to clean.
     * @returns {string} The cleaned string.
     */
    removeFormulaEscapeChar(str) {
        if (str.startsWith("'") && (str[1] === "-" || str[1] === "=")) {
            return str.slice(1);
        }
        return str;
    };


}

module.exports = Import;
