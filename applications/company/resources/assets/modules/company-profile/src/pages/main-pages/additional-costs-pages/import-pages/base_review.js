"use strict";

const Page = require("@ca-package/router/src/page");
const { findChild, jsSelector, onEvent } = require("@ca-package/dom");

const { Base: Table } = require("@ca-submodule/table");
const FormValidator = require("@cas-validator-js");
const {
    createErrorMessage,
} = require("@cas-notification-toast-js/message/error");

const FormInput = require("@ca-submodule/form-input");
const NumberInput = require("@ca-submodule/form-input/src/number");
FormInput.use(NumberInput);
FormInput.use(require("@ca-submodule/form-input/src/hidden_textarea"));

const DeleteModal = require("@cam-company-profile-js/modals/additional-costs/import/delete");

const edit_tpl = require("@cam-company-profile-tpl/pages/main-pages/additional-costs-pages/import-pages/additional-costs-pages/edit.hbs");

/**
 * Base Review Page
 *
 * This page is a Base version (mother Class) for Review Errors and Final Review pages.
 */
class BaseReview extends Page {
    /**
     * Constructs the ReviewErrors page.
     *
     * @param {module:Router.Controller} router - Router controller instance.
     * @param {string} name - Route name.
     * @param {module:Router.Page|null} [parent=null] - Optional parent page.
     */
    constructor(router, name, parent = null) {
        if (new.target === BaseReview) {
            throw new TypeError("Cannot instantiate BaseReview directly");
        }

        super(router, name, parent);
        Object.assign(this.state, {
            parent,
            number_of_invalid_records: null,
            table: null,
            table_loaded: false,
            table_scope: {
                sorts: {
                    id: Table.Sort.ASC,
                },
            },
            child_row_open: false,
        });
    };

    /**
     * Retrieves an additional costs from the import_data Map by its ID.
     *
     * @param {number} id - The additional costs ID (used as key in the Map).
     * @returns {Object|undefined} The additional costs object if found, otherwise undefined.
     */
    getAdditionalCostData(id) {
        return this.parent.state.import_data?.get(id);
    };

    /**
     * Returns only the invalid additional costs records from import data (stored as a Map).
     *
     * @returns {Array<object>} Array of invalid additional costs records.
     */
    getInvalidAdditionalCosts() {
        const importData = this.state.parent.state.import_data;
        if (!importData || !(importData instanceof Map)) {
            return [];
        }

        return [...importData.values()].filter((record) => record.error);
    };

    /**
     * Saves (updates) a additional costs in import data (Map) by its ID.
     *
     * @returns {boolean} True if the additional costs was found and updated, false otherwise.
     * @param additional_cost
     */
    saveAdditionalCostData(additional_cost) {
        const importData = this.parent.state.import_data;

        if (!importData || !(importData instanceof Map)) {
            return false;
        }

        const id = additional_cost.id;

        if (!importData.has(id)) {
            return false;
        }

        const existing = importData.get(id);
        importData.set(id, Object.assign(existing, additional_cost));

        return true;
    };

    /**
     * Renders the child row with the edit form for a given additional costs ID.
     *
     * @param {number|string} id - Additional Cost ID to edit.
     * @returns {Promise<void>}
     */
    async renderChildRow(id) {
        const {
            name = "",
            cost: raw_cost = "",
            markup: raw_markup = "",
            unit = "",
        } = this.getAdditionalCostData(id);

        let cost;
        if (
            raw_cost !== "" &&
            raw_cost !== null &&
            raw_cost !== undefined &&
            !isNaN(Number(raw_cost)) &&
            Number.isFinite(Number(raw_cost))
        ) {
            const cost_value = Number(raw_cost);
            cost = cost_value.toFixed(2);
        } else {
            cost = "";
        }

        let markup;
        if (
            raw_markup !== "" &&
            raw_markup !== null &&
            raw_markup !== undefined &&
            !isNaN(Number(raw_cost)) &&
            Number.isFinite(Number(raw_markup))
        ) {
            const markup_value = Number(raw_markup);
            markup = markup_value.toFixed(2);
        } else {
            markup = raw_markup ? "." : "";
        }

        let disable_save = false;
        const row = this.state.table.getRowById(id);

        const template = edit_tpl({
            disable_save,
            id,
            name,
            cost,
            markup,
            unit,
        });

        this.state.table.toggleChildRow(row, {
            title: "Edit Additional Cost",
            id,
            content: template,
            disable_save,
            footer: {
                label: "Delete Additional Cost",
                icon: "system--delete-bin-2-line",
            },
        });

        this.state.child_row_open = true;
    };

    /**
     * Calculate the unit price based on cost and markup
     */
    calculateUnitPrice(content) {
        let cost = parseFloat(findChild(content, jsSelector("cost")).val()),
            markup = findChild(content, jsSelector("markup")).val(),
            unit_price_field = findChild(content, jsSelector("unit_price")),
            value = 0;
        if (markup == 0 || markup === "") {
            markup = null;
        }

        if (markup !== null) {
            value = cost * (parseFloat(markup) / 100) + cost;
        } else {
            value = cost;
        }
        value = value.toFixed(2);
        unit_price_field.val(value);
    };

    /**
     * Retrieves the column definitions for the additional costs datatable.
     *
     * @returns {Object} Column definitions.
     */
    getTableColumns() {
        throw new Error("You must implement getTableColumns()");
    };

    /**
     * Retrieves the data for the additional costs datatable.
     *
     * @returns {Array} Table data.
     */
    getTableItems() {
        throw new Error("You must implement getTableItems()");
    };

    /**
     * Creates and configures the additional costs datatable, including event handlers and column definitions.
     */
    createTable() {
        this.state.table = new Table(this.elem.table, {
            row_edit: true,
            scrolling_enabled: true,
            scrollY: true,
            no_header: true,
        })
            .on("row_click", (data) => {
                if (this.state.child_row_open) {
                    return;
                }
                this.renderChildRow(data.id);
            })
            .on("child_row_rendered", (row, content) => {
                this.elem.form = findChild(content, jsSelector("form"));

                const fields = Object.fromEntries(
                    Object.entries(
                        this.state.parent.state.additional_costs_rules,
                    ).filter(([key]) => key !== "id_user_defined"),
                );

                this.elem.input = {};
                for (let name in fields) {
                    this.elem.input[name] = this.elem.form.fxFind(name);
                }

                const data = row.data();

                this.state.validator = FormValidator.init(this.elem.form).on(
                    "form:submit",
                    () => {
                        try {
                            this.parent.startWorking();
                            if (!this.editAdditionalCost(data?.id, content)) {
                                const msg = createErrorMessage(
                                    "Failed to save additional cost.",
                                );
                                this.router.main_route.layout.toasts.addMessage(
                                    msg,
                                );
                            }
                            this.state.table.toggleChildRow(row, {}, true);
                            this.state.child_row_open = false;
                            this.reloadTable();
                            this.parent.resetWorking();
                            return false;
                        } catch (error) {
                            this.parent.resetWorking();
                            console.error(error);
                            const message = createErrorMessage(
                                "Unable to save additional cost",
                            );
                            this.router.main_route.layout.toasts.addMessage(
                                message,
                            );
                        }
                    },
                );

                FormInput.init(findChild(content, jsSelector("cost")), {
                    type: NumberInput.Type.CURRENCY,
                    right_align: true,
                    allow_minus: false,
                });

                this.state.field = {};
                for (let name in fields) {
                    this.state.field[name] = this.elem.input[name].parsley(
                        fields[name],
                    );
                }

                FormInput.init(findChild(content, jsSelector("markup")), {
                    type: NumberInput.Type.PERCENTAGE,
                    right_align: true,
                    allow_minus: false,
                });

                FormInput.init(findChild(content, jsSelector("unit_price")), {
                    type: NumberInput.Type.CURRENCY,
                    right_align: true,
                    allow_minus: false,
                });

                onEvent(
                    findChild(content, jsSelector("cost")),
                    "change",
                    (e) => {
                        e.preventDefault();
                        this.calculateUnitPrice(content);
                        return false;
                    },
                );

                onEvent(
                    findChild(content, jsSelector("markup")),
                    "change",
                    (e) => {
                        this.calculateUnitPrice(content);
                        return false;
                    },
                );

                //Trigger validation immediately after form is rendered
                setTimeout(() => {
                    this.elem.form.parsley().validate();
                    if (this.state.field["markup"].validate() !== true) {
                        this.elem.input["markup"].val("");
                    }
                    this.calculateUnitPrice(content);
                }, 100);
            })
            .on("row_save", () => {
                this.elem.form.trigger("submit");
            })
            .on("row_cancel", (row) => {
                this.state.table.toggleChildRow(row, {}, true);
                this.state.child_row_open = false;
            })
            .on("row_delete", (row) => {
                const additional_cost_id = this.state.table.getRowData(row).id;
                this.openDeleteModal(additional_cost_id);
            });

        this.state.table.setToolbar({
            filter: false,
            settings: false,
        });

        this.state.table.setColumns(this.getTableColumns());

        this.state.table.setData(this.getTableItems());

        if (this.state.table_scope) {
            this.state.table.setState(this.state.table_scope);
        }

        // build table
        this.state.table.build();
        this.state.table_loaded = true;
    };

    /**
     * Deletes a additional cost from import data (Map) by its ID and reloads the table.
     *
     * @param {number|string} id - Additional Cost ID to delete.
     */
    deleteAdditionalCost(id) {
        const importData = this.parent.state.import_data;

        if (importData instanceof Map) {
            importData.delete(id);
        }

        this.reloadTable();
    };

    /**
     * Refreshes the table data and updates error and queued record counts.
     * If all errors are fixed, shows a success message and redirects to the final review.
     */
    reloadTable() {
        throw new Error("You must implement reloadTable()");
    };

    /**
     * Extracts edited values from a form and updates the additional cost.
     *
     * @param id
     * @param {jQuery} content - The form content element.
     * @returns {boolean} True if the additional cost was saved, false otherwise.
     */
    editAdditionalCost(id, content) {
        const data = {
            id: id,
            name: findChild(content, jsSelector("name")).val(),
            unit: findChild(content, jsSelector("unit")).val(),
            cost: findChild(content, jsSelector("cost")).val(),
            markup: findChild(content, jsSelector("markup")).val(),
        };
        return this.saveAdditionalCostData(data);
    };

    /**
     * Opens the delete confirmation modal for a additional cost.
     *
     * @param additional_cost_id
     */
    openDeleteModal(additional_cost_id) {
        if (!this.state.delete_modal) {
            this.state.delete_modal = new DeleteModal();
        }
        const additional_cost = this.getAdditionalCostData(additional_cost_id);
        this.state.delete_modal.open({
            additional_cost_id: additional_cost_id,
            additional_cost_name: additional_cost?.name || "--",
            onConfirm: (id) => {
                this.deleteAdditionalCost(id);
                this.state.child_row_open = false;
            },
        });
    };

    /**
     * Loads the page, checks for invalid records, updates counts, and initializes or redraws the table.
     *
     * @param {object} request - Request object.
     * @param {function} next - Callback to proceed after loading.
     */
    async load(request, next) {
        await super.load(request, next);
    };

    /**
     * Unloads the page and destroys the table.
     *
     * @param {object} request - Request object.
     * @param {function} next - Callback to proceed after unloading.
     */
    async unload(request, next) {
        this.state?.table?.destroy();
        this.state.table_loaded = false;
        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root - Root DOM element.
     */
    boot(root) {
        super.boot(root);
    };
}

module.exports = BaseReview;
