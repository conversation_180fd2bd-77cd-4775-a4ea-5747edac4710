"use strict";

const Page = require("@ca-package/router/src/page");
const { findChild, jsSelector } = require("@ca-package/dom");
const uuid4 = require('uuid/v4');
const import_tpl = require("@cam-company-profile-tpl/pages/main-pages/materials-pages/import.hbs");

/**
 * Materials Import Page
 *
 * Multistep wizard interface for importing materials using CSV files.
 * Steps include uploading, previewing, error review, and finalizing the import.
 */
class Import extends Page {
    /**
     * Initializes the Import page.
     *
     * @param {module:Router.Controller} router - Router controller instance.
     * @param {string} name - Route name.
     * @param {module:Router.Page|null} [parent=null] - Optional parent route.
     */
    constructor(router, name, parent = null) {
        super(router, name, parent);

        Object.assign(this.state, {
            parent,
            csv_data: {},
            import_data: {},
            materials_rules: {
                id_user_defined: {
                    maxlength: 100,
                    maxlengthMessage: "Invalid length - 100 chars. max",
                },
                name: {
                    required: true,
                    maxlength: 100,
                    maxlengthMessage: "Invalid length - 100 chars. max",
                },
                cost: {
                    required: true,
                    pattern: "^\\d+(\\.\\d{1,2})?$",
                    patternMessage: "Number must be formatted as a price.",
                },
                markup: {
                    pattern: "^\\d+(\\.\\d{1,2})?$",
                    patternMessage: "Number must be formatted as a percentage.",
                },
                unit: {
                    required: true,
                    maxlength: 20,
                    maxlengthMessage: "Invalid length - 20 chars. max",
                },
            },
        });
    }

    /**
     * Loads the import page and fetches necessary data
     *
     * @param {object} request - Request object.
     * @param {function} next - Callback after loading is complete.
     * @returns {Promise<void>}
     */
    async load(request, next) {
        await super.load(request, next);
    }

    /**
     * Unloads the page and resets CSV-related state data.
     *
     * @param {object} request - Request object.
     * @param {function} next - Callback after unloading is complete.
     * @returns {Promise<void>}
     */
    async unload(request, next) {
        this.state.csv_columns = null;
        this.state.csv_data = null;
        this.state.import_data = null;
        await super.unload(request, next);
    }

    /**
     * Initializes DOM elements for the import page.
     *
     * @param {jQuery} root - Root element for the page.
     */
    async boot(root) {
        super.boot(root);
        this.elem.page_container = root.fxFind("materials-import-container");
        this.elem.loader = findChild(root, jsSelector("loader"));
    }
    /**
     * Renders the import page.
     *
     * @returns {string} HTML content rendered from the template.
     */
    render() {
        return import_tpl();
    }
    /**
     * Returns available sub-routes for the import steps.
     *
     * @returns {object} Routes object mapping import steps to their pages.
     */
    static get routes() {
        return {
            upload: {
                default: true,
                page: require("./import-pages/upload"),
            },
            preview: {
                path: "/preview",
                page: require("./import-pages/preview"),
            },
            review_errors: {
                path: "/review-errors",
                page: require("./import-pages/review_errors"),
            },
            final_review: {
                path: "/final-review",
                page: require("./import-pages/final_review"),
            },
        };
    }

    /**
     * Shows the loader to indicate processing.
     */
    startWorking() {
        this.elem.loader.show();
    }

    /**
     * Hides the loader to indicate processing has finished.
     */
    resetWorking() {
        this.elem.loader.hide();
    }

    /**
     * Sanitizes and normalizes a materials object by ensuring default values and safe numeric formatting.
     *
     * - Converts the `cost` field to a valid decimal string with two digits after the decimal point.
     * - Applies default values for missing or undefined fields.
     * - Ensures returned fields are consistent and safe to use in UI or backend operations.
     *
     * @param {Object} data - The raw material data.
     * @param {string} [data.id_user_defined] - user-defined material ID.
     * @param {string} [data.name] - The material name.
     * @param {string} [data.description] - The material description.
     * @param {string|number} [data.price] - The material price, possibly unvalidated.
     * @param {string} [data.unit] - The unit of measure for the material.
     * @param {string} [data.pricing] - Pricing type or method.
     * @param {string} [data.category] - The material category.
     *
     * @returns {Object} A sanitized material object with safe string and price values.
     * @returns {string} return.id_user_defined - Sanitized user-defined ID.
     * @returns {string} return.name - Sanitized material name.
     * @returns {string} return.markup - Safe markup string with two decimal places.
     * @returns {string} return.cost - Safe price string with two decimal places.
     * @returns {string} return.unit - Sanitized unit of measure.

     */
    safeMaterialType(data) {
        const {
            id_user_defined: raw_id_user_defined = "",
            name = "",
            cost: raw_cost = "",
            markup: raw_markup = "",
            unit = "",
        } = data;

        let cost = raw_cost.replace('$', '');
        if (cost !== "" && cost !== null && cost !== undefined && !isNaN(Number(cost)) && Number.isFinite(Number(cost))) {
            const cost_value = Number(cost);
            cost = cost_value.toFixed(2);
        } else {
            cost = "";
        }

        let markup = raw_markup.replace('%', '');
        if (markup !== "" && markup !== null && markup !== undefined && !isNaN(Number(markup)) && Number.isFinite(Number(markup))) {
            const markup_value = Number(markup);
            markup = markup_value.toFixed(2);
        } else {
            markup = "";
        }

        let id_user_defined;
        if (!raw_id_user_defined) {
            id_user_defined = uuid4();
        } else {
            id_user_defined = String(raw_id_user_defined);
        }

        return {
            id_user_defined: String(id_user_defined ?? "").substring(0, 100),
            name: String(name ?? ""),
            cost: String(cost ?? ""),
            markup: String(markup ?? ""),
            unit: String(unit ?? ""),

        };
    }
}

module.exports = Import;
