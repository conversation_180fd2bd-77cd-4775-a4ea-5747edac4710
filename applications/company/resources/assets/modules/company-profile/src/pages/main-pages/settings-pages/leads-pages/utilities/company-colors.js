'use strict';

/**
 * Paint CSS custom-properties on a jQuery collection.
 */
function paint ($elems, colors) {
    if (!$elems || !colors) return;

    $elems.css({
        '--lform-accent-bg':     colors.button_background_color,
        '--lform-accent-border':  colors.button_background_color,
        '--lform-accent-text':    colors.button_text_color,

        '--lform-hover-bg':       colors.hover_background_color,
        '--lform-hover-border':   colors.hover_background_color,
        '--lform-hover-text':     colors.hover_text_color
    });
}

function validateColorsInput (colors) {
    return colors && colors.button_background_color &&
           colors.button_text_color &&
           colors.hover_background_color &&
           colors.hover_text_color;
}

module.exports = { paint, validateColorsInput };