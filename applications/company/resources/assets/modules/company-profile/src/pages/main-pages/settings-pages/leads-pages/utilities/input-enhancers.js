'use strict';
const InputMask            = require('inputmask');
const autosize             = require('autosize');
const FormInput            = require('@ca-submodule/form-input');
const { initSelectPlaceholder } = require('@cac-js/utils/select_placeholder');
const { findChild, jsSelector } = require('@ca-package/dom');
const { FIELD_NAMES }           = require('./constants');

function maskPhone ($root) {
    const $phone = findChild($root, jsSelector('phone'));
    if ($phone.length) InputMask({ mask: '(*************' }).mask($phone);
}

function autoGrow ($elem) {
    if (!$elem.length) return;
    autosize($elem);
    $elem.on('change', () => autosize.update($elem));
}

function init ($root) {
    maskPhone($root);
    ['customer_notes',
        'additional_email_recipients',
        'appointment_request_instruction_label'
    ].forEach(name => autoGrow(findChild($root, jsSelector(name))));
    const today = new Date();

    // Setup Date Inputs
    let multiplier = 0;
    $('.js-date-input').each((i, el) => {
        const d = new Date(today);

        // add a day based on the iteration and if there is a previous iteration multiplier
        d.setDate(d.getDate() + (i + multiplier + 1));

        // if it's a weekend we need to add additional days to prevent the default day selection to be a weekend
        const day_number = d.getDay();
        switch(day_number) {
            // Saturday
            case 6:
                multiplier = 2;
                d.setDate(d.getDate() + (multiplier));
                break;
            // Sunday
            case 0:
                multiplier = 1;
                d.setDate(d.getDate() + (multiplier));
                break;
        }

        FormInput.init($(el), {
            pickr_config: {
                dateFormat: 'Y-m-d',
                altFormat: 'm/d/Y',
                altInput: false,
                minDate: today,
                defaultDate: d
            }
        });
    });

    // Setup Select inputs
    [FIELD_NAMES.STATE, FIELD_NAMES.MARKETING_SOURCE, FIELD_NAMES.PROJECT_TYPE]
        .forEach(name => {
            const $el = findChild($root, jsSelector(name));
            if ($el.length) initSelectPlaceholder($el);
        });

    if ($('.c-ci-image .file-item').length === 0) {
        $('[data-js="thumbnail"]').hide();
    }
}

async function assignedToFieldInit (root, users) {
    const $select = findChild(root, jsSelector('default_assigned_to'))
        .filter('select')
        .first();

    const input = FormInput.init($select, {
        data_provider : () => users.map(u =>
            ({
                id: u.id,
                text: `${u.first_name} ${u.last_name}`
            })
        ),
        placeholder   : '-- Select One --',
        closeOnSelect : true
    });

    await input.promise;
}

module.exports = { init, assignedToFieldInit };