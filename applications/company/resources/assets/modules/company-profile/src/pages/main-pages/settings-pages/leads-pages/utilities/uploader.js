'use strict';

const Api = require('@ca-package/api');
const {findChild, jsSelector} = require("@ca-package/dom");
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10 MB
const MAX_FILES = 5;
let uppyScriptPromise = null;
let uppyCore          = null;
function loadUppyScriptOnce () {
    if (!uppyScriptPromise) {
        uppyScriptPromise = $.getScript(
            'https://releases.transloadit.com/uppy/v1.12.0/uppy.min.js'
        );
    }
    return uppyScriptPromise;
};

function injectCssOnce () {
    if (!document.getElementById('uppy-css')) {
        const link   = document.createElement('link');
        link.id      = 'uppy-css';
        link.rel     = 'stylesheet';
        link.href    = 'https://releases.transloadit.com/uppy/v1.12.0/uppy.min.css';
        document.head.appendChild(link);
    }
};

function iconByExt (ext) {
    switch (ext) {
        case 'txt':  return 'editor--text-snippet';
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':  return 'media--image-line';
        default:     return 'document--file-pdf-2-line';
    }
};

async function init (triggerSel = '#uppy-upload-file') {
    injectCssOnce();
    await loadUppyScriptOnce();

    if (!uppyCore) {
        uppyCore = new Uppy.Core({
            id: 'lead-form-uploader',
            allowMultipleUploads: true,
            autoProceed: true,
            restrictions: {
                allowedFileTypes: ['image/jpeg','image/png','application/pdf'],
                maxNumberOfFiles: MAX_FILES,
                maxFileSize: MAX_FILE_SIZE
            }
        });
    }

    // reset Dashboard (allows multiple instances on same page)
    const prev = uppyCore.getPlugin('Dashboard');
    if (prev) uppyCore.removePlugin(prev);

    uppyCore.use(Uppy.Dashboard, {
        id: 'Dashboard',
        inline: false,
        trigger: triggerSel,
        closeAfterFinish: true,
        showProgressDetails: true,
        proudlyDisplayPoweredByUppy: false,
        note: 'Images and PDFs only (up to 5 files)',
        height: 150
    });

    /* thumbnails / list UI */
    uppyCore.on('file-added',  file => { addFileItem(file) });
    uppyCore.on('file-removed', ({id}) => {
        $(`[data-id="${id}"]`).remove();
        if ($('.c-ci-image .file-item').length === 0) {
            $('[data-js="thumbnail"]').hide();
        }
    });

    return uppyCore;
};

function getInstance() {
    return uppyCore;
};

function addXHRUpload(uppy) {
    uppy.use(Uppy.XHRUpload, {
        endpoint: window.website_leads_form_data.lead_form.form_files_url,
        method: 'POST',
        fieldName: 'file',
        metaFields: ['lead_id', 'token', 'name']
    });
};

function removeXHRUpload(uppy) {
    if (!uppy) return;
    const plugin = uppy.getPlugin('XHRUpload');
    if (plugin) {
        uppy.removePlugin(plugin);
    }

}

async function manualUppyUpload(uppy, lead_id, token) {
    if (!uppy) return;

    Object.values(uppy.getState().files).forEach(f => {
        uppy.setFileMeta(f.id, {lead_id: parseInt(lead_id), token: token});
});

    uppy.setOptions({ autoProceed: true });
    if (Object.keys(uppy.getState().files).length === 0) return;
    await uppy.upload();
};



/* —————————————————————————— thumbnail rendering —————————————————————————— */

function addFileItem (file) {
    const $wrap = $('.c-ci-image[data-js="thumbnail"]');
    if ($wrap.find(`[data-id="${file.id}"]`).length) return;

    const ext  = file.name.split('.').pop().toLowerCase();
    const icon = `<svg data-icon><use xlink:href="#remix-icon--${iconByExt(ext)}"></use></svg>`;

    const html = `
    <div class="file-item" data-id="${file.id}">
      <div class="content-section">
        <div class="thumb-icon">${icon}</div>
        <div class="file-name">${file.name}</div>
      </div>
      <div class="delete-section">
        <a class="thumb-icon t-delete file-remove-btn">
          <svg data-icon><use xlink:href="#remix-icon--system--delete-bin-2-line"></use></svg>
        </a>
      </div>
    </div>`;

    $('[data-js="thumbnail"]').show();

    $wrap.append(html)
        .find(`[data-id="${file.id}"] .file-remove-btn`)
        .on('click', () => { uppyCore.removeFile(file.id); });
};

/* —————————————————————————————— exports —————————————————————————————— */

module.exports = { init, getInstance, addXHRUpload, removeXHRUpload, manualUppyUpload };