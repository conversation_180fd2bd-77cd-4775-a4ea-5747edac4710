"use strict";

const accounting = require("accounting");

const { findChild, jsSelector } = require("@ca-package/dom");
const FormInput = require("@ca-submodule/form-input");
const NumberInput = require("@ca-submodule/form-input/src/number");
FormInput.use(NumberInput);
FormInput.use(require("@ca-submodule/form-input/src/hidden_textarea"));
const review_errors_tpl = require("@cam-company-profile-tpl/pages/main-pages/materials-pages/import-pages/review_errors.hbs");
const {
    validateRecords,
} = require("@cam-company-profile-js/pages/main-pages/products-pages/import-pages/utils/validation");
const BaseReview = require("@cam-company-profile-js/pages/main-pages/materials-pages/import-pages/base_review");

/**
 * Product Import Review Errors Page
 *
 * This page is the third step in the material import wizard. It displays invalid material records,
 * allows users to review and fix errors, and provides editing and deletion capabilities for problematic materials.
 */
class ReviewErrors extends BaseReview {
    /**
     * Retrieves the table columns configuration.
     *
     * @returns {Object} Table columns configuration.
     */
    getTableColumns() {
        return {
            errors: {
                label: "",
                width: "5%",
                class_name: "errors-column",
                value: (data) => {
                    // Check if data.error exists and is an object
                    if (data.error && typeof data.error === "object") {
                        // Get the number of properties in the error object
                        const errorCount = Object.keys(data.error).length;
                        return `<span class="h-text h-text-circle t-red">${errorCount}</span>`;
                    }
                    return ""; // Return empty string if no errors
                },
                sortable: false,
            },
            id: {
                label: "Id",
                width: "10%",
                visible: false,
            },
            name: {
                label: "Name",
                width: "20%",
                value: (data) => {
                    return this.getSpanErrorOrValue(
                        "name",
                        data,
                        this.state.table.trimColumn(data.name, 20, true, true),
                        20,
                    );
                },
            },
            cost: {
                label: "Cost",
                width: "20%",
                value: (data) => {
                    return this.getSpanErrorOrValue(
                        "cost",
                        data,
                        data.cost === undefined ||
                            data.cost === null ||
                            data.cost === ""
                            ? ""
                            : accounting.formatMoney(data.cost),
                        10,
                    );
                },
            },
            markup: {
                label: "Markup",
                width: "20%",
                value: (data) => {
                    return this.getSpanErrorOrValue(
                        "markup",
                        data,
                        data.markup === undefined ||
                            data.markup === null ||
                            data.markup === ""
                            ? ""
                            : `${accounting.formatNumber(data.markup)}%`,
                        10,
                    );
                },
            },
            unit: {
                label: "Unit",
                width: "10%",
                value: (data) => {
                    return this.getSpanErrorOrValue(
                        "unit",
                        data,
                        this.state.table.trimColumn(data.unit, 10, true, true),
                        10,
                    );
                },
            },
        };
    };

    /**
     * Retrieves the table items (invalid materials) to be displayed in the table.
     *
     * @returns {Array} Array of invalid material records.
     */
    getTableItems() {
        return this.getInvalidMaterials();
    };

    /**
     * Refreshes the table data and updates error and queued record counts.
     * If all errors are fixed, shows a success message and redirects to the final review.
     */
    reloadTable() {
        this.state.parent.state.import_data = validateRecords(
            this.state.parent.state.import_data,
            this.parent.state.materials_rules,
        );

        const invalid_records = this.getInvalidMaterials();
        // Check if there are no more invalid records after editing
        if (!invalid_records || invalid_records.length === 0) {
            this.router.navigate("materials.import.final_review");
            return; // Exit early since we're redirecting
        }

        // Update the table with the remaining invalid records
        this.state.table.setTableData(invalid_records);
        this.elem.errors_count.text(invalid_records?.length);
        this.elem.records_queued_count.text(
            this.state.parent.state.import_data?.size - invalid_records?.length,
        );
    };

    /**
     * Checks if a field has an error in the provided errors object.
     *
     * @param {string} field - Field name.
     * @param {object} errors - Errors object.
     * @returns {boolean} True if the field has an error, false otherwise.
     */
    isFieldHaveError(field, errors) {
        return errors && errors[field];
    };

    /**
     * Returns an HTML span for a value, highlighting errors if present for the field.
     *
     * @param {string} field - Field name.
     * @param {object} data - Data object for the row.
     * @param {string} value - Value to display if no error.
     * @param {number} [limitStringLength=40] - Maximum string length before truncation.
     * @returns {string} HTML string for the value or error-highlighted value.
     */
    getSpanErrorOrValue(field, data, value, limitStringLength = 40) {
        if (this.isFieldHaveError(field, data?.error)) {
            let fieldValue;
            if (data[field] && data[field].length > limitStringLength) {
                fieldValue =
                    data[field].substring(0, limitStringLength) + "...";
            } else {
                fieldValue = data[field];
            }
            return `<span title="${data?.error[field]}" class="t-red">${data[field] ? fieldValue : "--"}</span>`;
        }
        return value;
    };

    /**
     * Loads the page, checks for invalid records, updates counts, and initializes or redraws the table.
     *
     * @param {object} request - Request object.
     * @param {function} next - Callback to proceed after loading.
     */
    async load(request, next) {
        await super.load(request, next);

        if (!(this.state.parent.state.import_data?.size > 0)) {
            this.router.navigate("materials.import.upload");
            return;
        }

        const invalid_records = this.getInvalidMaterials();

        // Check if there are no invalid records and redirect if needed
        if (!invalid_records || invalid_records.length === 0) {
            this.router.navigate("materials.import.final_review");
            return; // Exit early since we're redirecting
        }

        this.elem.errors_count.text(invalid_records.length);
        this.elem.records_queued_count.text(
            this.state.parent.state.import_data?.size - invalid_records?.length,
        );

        this.state.child_row_open = false;

        if (!this.state.table_loaded) {
            this.createTable();
        } else {
            this.state.table.draw();
        }
    };

    /**
     * Boot page
     *
     * @param {jQuery} root - Root DOM element.
     */
    boot(root) {
        super.boot(root);
        this.elem.table = findChild(root, jsSelector("review-errors-table"));
        this.elem.errors_count = findChild(root, jsSelector("errors-count"));
        this.elem.records_queued_count = findChild(
            root,
            jsSelector("records-queued-count"),
        );
    };

    /**
     * Renders the review errors page.
     *
     * @returns {string} Rendered HTML string.
     */
    render() {
        return review_errors_tpl({
            back_route: "materials.import.preview",
        });
    };
}

module.exports = ReviewErrors;
