'use strict';

const Page = require('@ca-package/router/src/page');

const {AdditionalCostAdd} = require('@cam-company-profile-js/pages/main-pages/additional-costs-pages/add');
const {AdditionalCostDelete} = require('@cam-company-profile-js/pages/main-pages/additional-costs-pages/delete');

const additional_costs_tpl = require('@cam-company-profile-tpl/pages/main-pages/additional_costs.hbs');

class AdditionalCosts extends Page {
    /**
     * Constructor
     *
     * @param {module:Router.Controller} router
     * @param {string} name
     * @param {module:Router.Page|null} [parent=null]
     */
    constructor(router, name, parent) {
        super(router, name, parent);
    };

    /**
     * Get available routes
     *
     * @returns {object}
     */
    static get routes() {
        return {
            add: {
                path: '/add',
                modal: AdditionalCostAdd
            },
            delete: {
                path: '/delete/{additional_cost_id}',
                modal: AdditionalCostDelete,
                bindings: {
                    additional_cost_id: 'uuid'
                }
            },
            items: {
                default: true,
                page: require('./additional-costs-pages/items')
            },
            import: {
                path: '/import',
                page: require('./additional-costs-pages/import')
            }
        };
    };

    /**
     * Show loader overlay
     *
     * @param {boolean} [show=true]
     */
    showLoader(show = true) {
        this.elem.loader.toggle(show);
    };

    /**
     * Hide loader overlay
     */
    hideLoader() {
        return this.showLoader(false);
    };



    /**
     * Unload page
     *
     * @param {object} request
     * @param {function} next
     */
    async unload(request, next) {
        await super.unload(request, next);
    };

    /**
     * Boot page
     *
     * @param {jQuery} root
     */
    boot(root) {
        super.boot(root);
        this.elem.loader = this.elem.root.fxFind('loader');
        this.elem.page_container = root.fxFind('page-container');
    };

    /**
     * Get container element for sub pages
     *
     * @returns {*}
     */
    getPageContainer() {
        return this.elem.page_container;
    };

    /**
     * Render page
     *
     * @returns {string}
     */
    render() {
        return additional_costs_tpl();
    };
}

module.exports = AdditionalCosts;