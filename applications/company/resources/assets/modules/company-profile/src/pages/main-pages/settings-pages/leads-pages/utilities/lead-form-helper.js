'use strict';

const debounce = require('@cac-js/utils/debounce');
const {findChild, jsSelector, onEvent} = require("@ca-package/dom");
const {FIELD_NAMES, TYPES} = require("./constants");
const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/checkbox'));
FormInput.use(require('@ca-submodule/form-input/src/static_dropdown'));
FormInput.use(require('@ca-submodule/form-input/src/date'));

const uploader   = require('./uploader');
const colorsUtil = require('./company-colors');
const validators = require('./validators');
const api        = require('./api');
const enhancers  = require('./input-enhancers');


class LeadFormHelper {
    constructor(root, refresh) {
        this.root = root;
        this.refreshForm = refresh;
        this.uppy = null;
        this.inputs = [];
    }

    setupAllListeners() {
        this._setupRadioFieldToggles();
        this._setupFieldsToggles();
        this._setupLabelOptions();
    }

    _setupRadioFieldToggles() {
        const fields = [FIELD_NAMES.MARKETING_SOURCE, FIELD_NAMES.PROJECT_TYPE];
        fields.forEach((field) => {
            const visibility_elem = findChild(this.root, jsSelector(`${field}_visibility`));
            const dropdown_radio = findChild(this.root, jsSelector(`${field}_dropdown`));
            const freeform_radio = findChild(this.root, jsSelector(`${field}_freeform`));

            const toggleRadios = () => {
                const is_available = visibility_elem.is(':checked');
                dropdown_radio.prop('disabled', !is_available);
                freeform_radio.prop('disabled', !is_available);
                this.refreshForm();
            };

            onEvent(visibility_elem, 'change', toggleRadios);
            onEvent(dropdown_radio, 'change', this.refreshForm);
            onEvent(freeform_radio, 'change', this.refreshForm);

            toggleRadios();
        });
    }

    _setupFieldsToggles() {
        Object.values(FIELD_NAMES).forEach((field) => {
            const visibility_elem = findChild(this.root, jsSelector(`${field}_visibility`));
            const requirement_elem = findChild(this.root, jsSelector(`${field}_requirement`));
            const options_title = findChild(this.root, jsSelector(`${field}_options_title`));
            const options_container_elem = findChild(this.root, jsSelector(`${field}_options_container`));

            if (visibility_elem.length) {
                this.inputs[field] = FormInput.init(visibility_elem);
            }

            onEvent(requirement_elem, 'change', () => {
                if (!visibility_elem.is(':checked')) {
                    visibility_elem.prop('checked', true).trigger('change');
                }
                this.refreshForm();
            });

            onEvent(visibility_elem, 'change.fx', () => {
                if (!this.inputs[field].checked) {
                    requirement_elem.prop('checked', false);
                    options_container_elem.addClass('t-hidden');
                    options_title.addClass('t-inactive');
                }
                else {
                    options_container_elem.removeClass('t-hidden');
                    options_title.removeClass('t-inactive');
                }

                // hide everything below.
                this.refreshForm();
            });
        });
    }

    _setupLabelOptions() {
        const fields = [FIELD_NAMES.MARKETING_SOURCE, FIELD_NAMES.PROJECT_TYPE, FIELD_NAMES.CUSTOMER_NOTES];
        fields.forEach(field => {
            const visibility_elem = findChild(this.root, jsSelector(`${field}_visibility`));
            const label_elem = findChild(this.root, jsSelector(`${field}_label`));

            label_elem.prop('disabled', !visibility_elem.is(':checked'));

            onEvent(visibility_elem, 'change', () => {
                label_elem.prop('disabled', !visibility_elem.is(':checked'));
                this.refreshForm();
            });
        });

        const label_fields = [
            FIELD_NAMES.MARKETING_SOURCE,
            FIELD_NAMES.PROJECT_TYPE,
            FIELD_NAMES.CUSTOMER_NOTES,
            FIELD_NAMES.EMAIL_CHECKBOX,
            FIELD_NAMES.UPLOAD_FILE,
            FIELD_NAMES.APPOINTMENT_REQUEST,
            FIELD_NAMES.APPOINTMENT_REQUEST_INSTRUCTION,
            'title', 'save_button'];

        label_fields.forEach(field => {
            const label_elem = findChild(this.root, jsSelector(`${field}_label`));
            label_elem.fxEvent('keyup', debounce(this.refreshForm, 300));
        });
    }


    /* ─────────────────────────── validation & payload ─────────────────────── */

    validateFields () {
        return validators.isValidForm(this.root);
    }

    buildEntity (...args) {
        return validators.buildEntityPayload(this.root, ...args);
    }


    /* ───────────────────────── component setup  ───────────────────────────── */

    async initializeFormComponents () {
        enhancers.init(this.root);
        this.uppy = await uploader.init('#uppy_upload_file');

        window.__lead_form_uppy  = this.uppy;
        window.__lead_uploader    = uploader;

        this._bindFileCountSync();
    }

    _bindFileCountSync() {
        const uppy = this.uppy;
        const $counter = $('#upload_file');

        const syncFileCount = () => {
            const count = uppy.getFiles().length;
             $counter
                .attr('data-file-count', count)
                .val(count || '')
                .trigger('input');
        };

        // on any change to the Uppy queue, run it
        uppy.on('file-added',   syncFileCount);
        uppy.on('file-removed', syncFileCount);
        uppy.on('reset',        syncFileCount);

        // initialize once in case there are already files
        syncFileCount();
    }




    async setupAssignedToField (users) {
        await enhancers.assignedToFieldInit(this.root, users);
    }

    /* ─────────────────────────── company colours ─────────────────────────── */


    setupAppointmentButtonColors(company_colors) {
        if (!colorsUtil.validateColorsInput(company_colors)) {
            return;
        }

        const $activeSlots = $('.time-slot.active');
        const $inputs = $('.f-fidt-input');
        colorsUtil.paint($activeSlots, company_colors);
        colorsUtil.paint($inputs, company_colors);

        const self = this;
        $(document).on('click', '.time-slot', function () {
            requestAnimationFrame(
                () => {
                    colorsUtil.paint($('.time-slot.active'), company_colors);
                });
        });
    }

    setupUploaderColors(company_colors) {
        if (!colorsUtil.validateColorsInput(company_colors)) {
            return;
        }
        const $uploadFileButton = $('.upload-files-section');
        if ($uploadFileButton) {
            colorsUtil.paint($uploadFileButton, company_colors);
        }
    }

    setupSubmitButtonCompanyColors(company_colors) {
        const submit_button = findChild(this.root, jsSelector('save_button'));
        if (!submit_button || !colorsUtil.validateColorsInput(company_colors)) {
            return;
        }

        colorsUtil.paint(submit_button, company_colors);
    }
}

module.exports = LeadFormHelper;