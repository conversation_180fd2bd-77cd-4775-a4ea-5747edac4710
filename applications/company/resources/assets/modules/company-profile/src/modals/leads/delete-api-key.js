'use strict';

const Api = require('@ca-package/api');
const Confirm = require('@ca-submodule/modal').Confirm;
const content_tpl = require('@cam-company-profile-tpl/modals/leads/delete_api_key.hbs');
const {Message} = require("@cas-modal-js");

class DeleteApiKeyModal extends Confirm {
    constructor() {
        super({
            size: Message.Size.SMALL,
        });
        this.setTitle('Delete Leads API Key');
        this.setContent(content_tpl());
    };

    /**
     * Open modal
     *
     * @param {object} $0.promise
     * @returns {Modal}
     */
    open({promise} = {}) {
        this.state.promise = promise;
        return super.open();
    };

    /**
     * Handle 'yes' response
     */
    async handleYes() {
        this.startWorking();
        try {

            const data = await Api.Resources.LeadFormsAction()
                .method(Api.Request.Method.DELETE)
                .custom('api-key');

            if (!data || !data.api_key) {
                this.showErrorMessage('Unable to delete Leads API Key')
            }

            this.resetWorking();
            this.state.promise.resolve(true);
            this.close();
        } catch (e) {
            console.warn(e)
            this.resetWorking();
            this.state.promise.resolve(null);
            this.showErrorMessage('Unable to delete Leads API Key')
        }
    };

    /**
     * Handle 'no' response
     */
    handleNo() {
        this.resetWorking();
        this.state.promise.resolve(false);
        this.close();
    };
}

module.exports = DeleteApiKeyModal;
