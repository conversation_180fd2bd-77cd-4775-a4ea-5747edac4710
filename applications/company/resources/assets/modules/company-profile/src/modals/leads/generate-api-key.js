'use strict';

const Message = require('@ca-submodule/modal').Message;
const template = require('@cam-company-profile-tpl/modals/leads/generate_api_key.hbs');
const ClipboardJS = require('clipboard');
const {createSuccessMessage} = require("@cas-notification-toast-js/message/success");
const {createErrorMessage} = require("@cas-notification-toast-js/message/error");

class GenerateApiKeyModal extends Message {
    constructor() {
        super({
            wrapper: true,
            closable: true,
            size: Message.Size.SMALL,
        });
        this.setTitle('Your Leads API Key');
    }

    /**
     * Opens the modal with the given API key.
     *
     * @param {Object} param0
     * @param {string} param0.api_key
     * @param {Object} param0.toast
     * @returns {Modal}
     */
    open({ api_key ,toast}) {
        const html = template({ api_key: api_key });
        this.setContent(html);
        super.open();

        setTimeout(() => {
            const $btn   = $('#copy-button');
            const $input = $('#api-key-input');

            if (!$btn.length || !$input.length) {
                console.error('Copy button or input not found in modal');
            }

            // initialize ClipboardJS
            const clipboard = new ClipboardJS($btn.get(0), {
                text: () => $input.val()
            });

            clipboard.on('success', () => {
                console.log('copied');
                toast.addMessage(
                    createSuccessMessage('Leads API Key copied to clipboard!')
                );
            });

            clipboard.on('error', () => {
                try {
                    $input.get(0).select();
                    document.execCommand('copy');
                    toast.addMessage(
                        createSuccessMessage('Leads API Key copied to clipboard!')
                    );
                } catch {
                    toast.addMessage(
                        createErrorMessage('Failed to copy API Key. Please copy manually.')
                    );
                }
            });
        }, 0);

        return this;
    }
}

module.exports = GenerateApiKeyModal;