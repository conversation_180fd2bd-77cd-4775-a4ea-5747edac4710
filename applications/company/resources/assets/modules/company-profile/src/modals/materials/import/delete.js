'use strict';

const Confirm = require('@ca-submodule/modal').Confirm;

const content_tpl = require('@cam-company-profile-tpl/modals/materials/import/delete.hbs');

class Delete extends Confirm {
    constructor() {
        super();
        this.setTitle('Delete Material');
        this.setContent(content_tpl());
        this.elem.material = this.elem.content.fxFind('material');
    };

    /**
     * Opens the modal and sets the material information.
     *
     * @param {Object} options - Modal configuration options.
     * @param {string|number} options.material_id - The ID of the material to display.
     * @param {string} options.material_name - The name of the material to display.
     * @param {Function} [options.onConfirm] - Optional callback function to execute on confirmation.
     * @returns {Modal} The opened modal instance.
     */
    open({ material_id, material_name, onConfirm = () => {}}) {
        this.state.material_id = material_id;
        this.state.onConfirm = onConfirm;
        this.elem.material.text(material_name || '--');
        return super.open();
    }

    /**
     * Handle 'yes' response
     */
    handleYes() {
        try {
            this.state.onConfirm(this.state.material_id);
        } catch (err) {
            console.error('Error in DeleteModal onConfirm:', err);
        }
        super.close();
    };

    /**
     * Handle 'no' response
     */
    handleNo() {
        super.close();
    };

}

module.exports = Delete;