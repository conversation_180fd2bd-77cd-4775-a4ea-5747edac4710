'use strict';

const Confirm = require('@ca-submodule/modal').Confirm;

const content_tpl = require('@cam-company-profile-tpl/modals/additional-costs/import/delete.hbs');

class Delete extends Confirm {
    constructor() {
        super();
        this.setTitle('Delete Additional Cost');
        this.setContent(content_tpl());
        this.elem.additional_cost = this.elem.content.fxFind('additional-cost');
    };

    /**
     * Opens the modal and sets the additional cost information.
     *
     * @param {Object} options - Modal configuration options.
     * @param {string|number} options.additional_cost_id - The ID of the additional cost to display.
     * @param {string} options.additional_cost_name - The name of the additional cost to display.
     * @param {Function} [options.onConfirm] - Optional callback function to execute on confirmation.
     * @returns {Modal} The opened modal instance.
     */
    open({ additional_cost_id, additional_cost_name, onConfirm = () => {}}) {
        this.state.additional_cost_id = additional_cost_id;
        this.state.onConfirm = onConfirm;
        this.elem.additional_cost.text(additional_cost_name || '--');
        return super.open();
    }

    /**
     * Handle 'yes' response
     */
    handleYes() {
        try {
            this.state.onConfirm(this.state.additional_cost_id);
        } catch (err) {
            console.error('Error in DeleteModal onConfirm:', err);
        }
        super.close();
    };

    /**
     * Handle 'no' response
     */
    handleNo() {
        super.close();
    };

}

module.exports = Delete;