'use strict';

import './resources/sass/main.scss';

const $ = require('jquery');
window.$ = window.jQuery = $;

require('@ca-package/dom/src/jquery_plugin');

require('remixicon/icons/Media/play-line.svg');
require('remixicon/icons/Media/shuffle-line.svg');
require('remixicon/icons/System/add-circle-line.svg');
require('remixicon/icons/System/close-circle-line.svg');
require('remixicon/icons/System/eye-line.svg');
require('remixicon/icons/System/external-link-line.svg');
require('remixicon/icons/System/download-line.svg');

require('remixicon/icons/Document/file-pdf-2-line.svg');
require('remixicon/icons/Editor/text-snippet.svg');
require('remixicon/icons/Media/image-line.svg');

import {layout} from '@ca-submodule/layout';
import {Controller} from './src/index';

window.Lead = new Controller(layout);
