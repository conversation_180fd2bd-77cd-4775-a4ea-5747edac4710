<div class="c-ldm-header">
    <h1 class="c-ldmh-text">Lead Details</h1>
    <a class="c-ldmh-close" data-close>
        <svg class="c-ldmhc-icon"><use xlink:href="#remix-icon--system--close-line"></use></svg>
    </a>
</div>
<div class="m-content">
    <div class="c-c-wrapper">
        <div class="c-cw-header">
            <h4 data-js="name"></h4>
            <button class="c-cwh-button" data-js="edit-button">
                <div data-text>Edit</div>
                <svg data-icon><use xlink:href="#remix-icon--design--edit-2-line"></use></svg>
            </button>
        </div>
        <div class="c-cw-section t-status">
            <div class="c-cws-row">
                <p class="c-cwsr-title">Status</p>
                <div class="c-cwsr-line"></div>
                <div class="c-cwsr-content-wrapper t-status">
                    <div data-js="status"></div>
                </div>
            </div>
            <div class="c-cws-actions" data-js="actions-wrapper">
                <button class="c-cwsa-button t-working" data-js="work-button">
                    <div data-text>Work</div>
                    <svg data-icon><use xlink:href="#remix-icon--media--play-line"></use></svg>
                </button>
                <button class="c-cwsa-button t-convert" data-js="convert-button">
                    <div data-text>Convert</div>
                    <svg data-icon><use xlink:href="#remix-icon--media--shuffle-line"></use></svg>
                </button>
                <button class="c-cwsa-button t-kill" data-js="kill-button">
                    <div data-text>Kill</div>
                    <svg data-icon><use xlink:href="#remix-icon--system--close-line"></use></svg>
                </button>
            </div>
        </div>
        <div class="c-cw-section t-hidden" data-js="contact_content">
            <h5>Contact Info</h5>
            <div class="c-cws-row t-hidden" data-js="business_content">
                <p class="c-cwsr-title">Business Name</p>
                <div class="c-cwsr-line"></div>
                <div class="c-cwsr-content-wrapper" data-js="business_name"></div>
            </div>
            <div class="c-cws-row t-hidden" data-js="phone_content">
                <p class="c-cwsr-title">Phone</p>
                <div class="c-cwsr-line"></div>
                <div class="c-cwsr-content-wrapper" data-js="phone"></div>
            </div>
            <div class="c-cws-row t-hidden" data-js="email_content">
                <p class="c-cwsr-title">Email</p>
                <div class="c-cwsr-line"></div>
                <div class="c-cwsr-content-wrapper t-email" data-js="email"></div>
            </div>
        </div>
        <div class="c-cw-section t-hidden" data-js="address_content">
            <h5>Location</h5>
            <div class="c-cws-row t-hidden" data-js="address_content">
                <p class="c-cwsr-title">Address</p>
                <div class="c-cwsr-line"></div>
                <div class="c-cwsr-content-wrapper" data-js="address"></div>
            </div>
        </div>
        <div class="c-cw-section t-hidden" data-js="details_content">
            <h5>Details</h5>
            <div class="c-cws-row t-hidden" data-js="project_type_content">
                <p class="c-cwsr-title">Project Type</p>
                <div class="c-cwsr-line"></div>
                <div class="c-cwsr-content-wrapper" data-js="project_type"></div>
            </div>
            <div class="c-cws-row t-hidden" data-js="priority_content">
                <p class="c-cwsr-title">Priority</p>
                <div class="c-cwsr-line"></div>
                <div class="c-cwsr-content-wrapper" data-js="priority"></div>
            </div>
            <div class="c-cws-row t-hidden" data-js="assigned_to_content">
                <p class="c-cwsr-title">Assigned To</p>
                <div class="c-cwsr-line"></div>
                <div class="c-cwsr-content-wrapper" data-js="assigned_to"></div>
            </div>
            <div class="c-cws-row t-hidden" data-js="marketing_source_content">
                <p class="c-cwsr-title">Marketing Source</p>
                <div class="c-cwsr-line"></div>
                <div class="c-cwsr-content-wrapper" data-js="marketing_source"></div>
            </div>
            <div class="c-cws-row t-hidden" data-js="origin_content">
                <p class="c-cwsr-title">Origin</p>
                <div class="c-cwsr-line"></div>
                <div class="c-cwsr-content-wrapper" data-js="origin"></div>
            </div>
            <div class="c-cws-row t-hidden" data-js="is_unsubscribed_content">
                <p class="c-cwsr-title">Unsubscribed</p>
                <div class="c-cwsr-line"></div>
                <div class="c-cwsr-content-wrapper" data-js="is_unsubscribed"></div>
            </div>
            <div class="c-cws-row t-hidden" data-js="is_sent_notifications_content">
                <p class="c-cwsr-title">Sent Notifications</p>
                <div class="c-cwsr-line"></div>
                <div class="c-cwsr-content-wrapper" data-js="is_sent_notifications"></div>
            </div>

            <div class="c-cws-row t-hidden" data-js="customer_content">
                <p class="c-cwsr-title">Customer</p>
                <div class="c-cwsr-line"></div>
                <div class="c-cwsr-content-wrapper" data-js="customer"></div>
            </div>
        </div>
    </div>
    <div class="c-c-tasks">
        <a class="c-ca-button t-primary" target="_blank" data-js="view-tasks-link" href="">
            <div data-text>View Task</div>
            <svg data-icon><use xlink:href="#remix-icon--media--eye-line"></use></svg>
        </a>
        <a class="c-ca-button t-tertiary" data-js="create-task-link" target="_blank" href="">
            <div data-text>Add Task</div>
            <svg data-icon><use xlink:href="#remix-icon--system--add-circle-line"></use></svg>
        </a>
    </div>

    <div class="c-c-files details">
        <div class="c-cf-header">
            <h5 class="c-cfh-text">Uploaded Files</h5>
        </div>
        <div class="c-cf-no-files" data-js="no-files">No Lead Files</div>
        <div class="c-cf-table" data-js="files-table"></div>
    </div>

    <div class="c-c-notes t-hidden" data-js="notes_wrapper">
        <div class="c-cn-content t-hidden" data-js="notes_content">
            <h4 class="c-cnc-title t-header">Notes</h4>
            <div class="c-cns-content-wrapper t-notes" data-js="notes"></div>
        </div>
        <div class="c-cn-line t-hidden" data-js="notes_line"></div>
        <div class="c-cn-content t-working t-hidden" data-js="working_notes_content">
            <h4 class="c-cnc-title t-header">Working Notes</h4>
            <div class="c-cnc-content-wrapper t-notes" data-js="working_notes"></div>
        </div>
        <div class="c-cn-line t-hidden" data-js="dead_notes_line"></div>
        <div class="c-cn-content t-dead t-hidden" data-js="dead_notes_content">
            <h4 class="c-cnc-title t-header">Dead Notes</h4>
            <div class="c-cnc-content-wrapper t-notes" data-js="dead_notes"></div>
        </div>
    </div>
</div>
