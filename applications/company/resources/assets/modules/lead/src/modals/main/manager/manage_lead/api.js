'use strict';

const $ = require('jquery');
const Api = require('@ca-package/api');

module.exports = {
    async getUsers() {
        let { entities: users } = await Api.Resources.Users()
            .fields(['id','first_name','last_name'])
            .filter('is_active', true)
            .filter('is_user_invited', false)
            .all();
        return users.map(u => u.data);
    },

    async fetchMarketingSources() {
        let result = await $.ajax({
            url: window.fx_url.API + 'company/marketing-sources',
            dataType: "json",
            type: "GET",
            contentType: "application/x-www-form-urlencoded"
        });
        return result.marketing_types;
    },

    async fetchProjectTypes() {
        let { entities: types } = await Api.Resources.ProjectTypes()
            .fields(['id','name'])
            .filter('status', Api.Constants.ProjectTypes.Status.ACTIVE)
            .sort('name','asc')
            .all();
        return types.map(t => t.data);
    },

    async fetchLeadFiles(lead_id) {
        if (!lead_id) return [];
        const entity = await Api.Resources.Leads()
            .accept('application/vnd.adg.fx.lead-files-v1+json')
            .fields(['id', 'lead_uuid', 'files'])
            .retrieve(lead_id)

        const lead = entity.get();
        if (!lead || !lead.files) return [];
        return lead.files;
    },

    async fetchID(leadUuid) {
        let { entities: leads } = await Api.Resources.Leads()
            .fields(['id','lead_uuid'])
            .filter('lead_uuid', leadUuid)
            .all();
        return leads.length ? leads[0].data : null;
    }
};