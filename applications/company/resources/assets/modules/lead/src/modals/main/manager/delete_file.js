'use strict';

const Api = require('@ca-package/api');
const Confirm = require('@ca-submodule/modal').Confirm;
const content_tpl = require('@cam-lead-tpl/modals/main/manager/manage-lead/delete_file.hbs');

class Delete extends Confirm {
    constructor() {
        super();
        this.setTitle('Delete Lead File');
        this.setContent(content_tpl());
    };

    /**
     * Open modal
     *
     * @param {object} $0
     * @param {string} $0.file_id
     * @param {string} $0.file_name
     * @param {object} $0.promise
     * @param {boolean} [$0.dry_run=false] - If true, the modal will not perform any actions
     * @returns {Modal}
     */
    async open({file_id, file_name, promise, dry_run = false}) {
        Object.assign(this.state, { file_id, promise, dry_run });
        this.setContent(content_tpl({file_name: file_name}));
        return super.open();
    };

    /**
     * Handle 'yes' response
     */
    handleYes() {
        if (this.state.dry_run) {
            this.state.promise.resolve(true);
            return this.close();
        }

        this.startWorking();
        Api.Resources.LeadFiles().delete(this.state.file_id)
            .then(() => {
                this.resetWorking();
                this.state.promise.resolve(true);
                this.close();
            }, (error) => {
                if (error.code === 1014) {
                    error.message = 'Unable to delete lead file';
                }
                this.showErrorMessage(error.message);
                this.resetWorking();
            });
    };

    /**
     * Handle 'no' response
     */
    handleNo() {
        this.resetWorking();
        this.close();
        this.state.promise.resolve(null);

    };
}

module.exports = Delete;
