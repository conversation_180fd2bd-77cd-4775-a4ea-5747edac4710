'use strict';

const moment = require("moment-timezone");

const Api = require('@ca-package/api');

const Modal = require('@ca-submodule/modal').Base;

const modal_tpl = require('@cam-lead-tpl/modals/main/manager/details.hbs');
const customer_link_tpl = require('@cam-lead-tpl/modals/main/manager/details-components/customer_link.hbs');
const content_tpl = require('@cam-lead-tpl/modals/main/manager/details-components/content.hbs');
const notes_content_tpl = require('@cam-lead-tpl/modals/main/manager/details-components/notes_content.hbs');
const Table = require('@ca-submodule/table').Base;
const $ = require('jquery');
window.$      = window.jQuery = $;
const filesize = require('filesize');
require('@fancyapps/fancybox');
const DeleteFileModal = require("@cam-lead-js/modals/main/manager/delete_file");
const Uploader = require("@cam-lead-js/modals/main/manager/manage_lead/uploader");


class Details extends Modal {
    /**
     * Constructor
     *
     * @param {module:Lead} module
     */
    constructor(module) {
        super(modal_tpl({
            task_link: fx_pages.TASKS_CREATE
        }), {
            size: Modal.Size.TINY,
            closable: true,
            wrapper: false,
            classes: ['m-lead-details-modal']
        });

        Object.assign(this.state, {
            module,
            config: {
                status: new Map([
                    [Api.Constants.Leads.Status.NEW, '<span class="h-text t-blue">New</span>'],
                    [Api.Constants.Leads.Status.WORKING, '<span class="h-text t-yellow">Working</span>'],
                    [Api.Constants.Leads.Status.CONVERTED, '<span class="h-text t-green">Converted</span>'],
                    [Api.Constants.Leads.Status.DEAD, '<span class="h-text t-grey">Dead</span>']
                ]),
                priority: new Map([
                    [null, ''],
                    [Api.Constants.Leads.Priority.HOT, '<span class="h-text t-red">Hot</span>'],
                    [Api.Constants.Leads.Priority.WARM, '<span class="h-text t-yellow">Warm</span>'],
                    [Api.Constants.Leads.Priority.COLD, '<span class="h-text t-blue">Cold</span>'],
                    [Api.Constants.Leads.Priority.DEAD, '<span class="h-text t-grey">Dead</span>']
                ]),
                origin: new Map([
                    [Api.Constants.Leads.Origin.STANDARD, 'Standard'],
                    [Api.Constants.Leads.Origin.WEBSITE_LEADS_FORM, 'Website Leads Form'],
                    [Api.Constants.Leads.Origin.API_INTEGRATION, 'API Integration'],
                ])
            },
            files_table: null,
            no_files: null,
            gallery_extensions: ['jpg', 'jpeg', 'png', 'gif']
        });

        this.elem.edit_button =  this.elem.content.fxFind('edit-button');
        this.elem.convert_button =  this.elem.content.fxFind('convert-button');
        this.elem.work_button =  this.elem.content.fxFind('work-button');
        this.elem.kill_button =  this.elem.content.fxFind('kill-button');
        this.elem.create_task_link =  this.elem.content.fxFind('create-task-link');
        this.elem.view_tasks_link =  this.elem.content.fxFind('view-tasks-link');
        this.elem.actions_wrapper =  this.elem.content.fxFind('actions-wrapper');
        this.elem.no_files = this.elem.content.fxFind('no-files');
        this.elem.files_table = this.elem.content.fxFind('files-table');

        this.elem.view_tasks_link.hide();

        for (let name of [
            'status', 'name', 'business_name', 'business_content', 'contact_content', 'contact', 'phone',
            'phone_content', 'email', 'email_content', 'address_content', 'address', 'project_type_content', 'project_type',
            'priority_content', 'priority', 'assigned_to_content', 'assigned_to', 'marketing_source_content',
            'marketing_source', 'details_content', 'customer_content', 'customer', 'notes_wrapper', 'notes_content',
            'notes', 'notes_line', 'dead_notes_content', 'dead_notes', 'dead_notes_line', 'working_notes_content',
            'working_notes', 'origin', 'origin_content', 'is_unsubscribed', 'is_unsubscribed_content', 'is_sent_notifications',
            'is_sent_notifications_content'
        ]) {
            this.elem[name] = this.elem.content.fxFind(name);
        }

        this.elem.edit_button.on('click', (e) => {
            e.preventDefault();
            this.state.promise.resolve({
                route: 'manager.edit',
                lead_id: this.state.lead.lead_uuid
            });
        });
        this.elem.convert_button.on('click', (e) => {
            e.preventDefault();
            window.location.href = window.fx_pages.NEW_CUSTOMER.replace('{lead_id}', this.state.lead.lead_uuid);
        });
        this.elem.work_button.on('click', (e) => {
            e.preventDefault();
            this.state.promise.resolve({
                route: 'manager.working',
                lead_id: this.state.lead.lead_uuid
            });
        });
        this.elem.kill_button.on('click', (e) => {
            e.preventDefault();
            this.state.promise.resolve({
                route: 'manager.kill',
                lead_id: this.state.lead.lead_uuid
            });
        });

        this.on('close', () => {
            if (this.state.promise !== null && !this.state.external_close) {
                this.state.promise.resolve(null);
            }
        });
    };

    /**
     * Populate data into form
     *
     * @param {object} data
     * @returns {Promise<void>}
     */
    populate(data) {
        if (data.tasks.length > 0) {
            this.elem.view_tasks_link.show();
            this.elem.view_tasks_link.prop('href', fx_pages.LEAD_TASKS.replace('{lead_id}', data.id));
            let content = 'View 1 Task';
            if (data.tasks.length > 1) {
                content = `View ${data.tasks.length} Tasks`;
            }
            this.elem.view_tasks_link.text(content);
        }
        this.elem.create_task_link.prop('href', fx_pages.TASKS_CREATE.replace('{lead_id}', data.id));
        switch(data.status) {
            case Api.Constants.Leads.Status.NEW:
                this.elem.convert_button.addClass('t-show');
                this.elem.work_button.addClass('t-show');
                this.elem.kill_button.addClass('t-show');
                break;
            case Api.Constants.Leads.Status.WORKING:
                this.elem.convert_button.addClass('t-show');
                this.elem.kill_button.addClass('t-show');
                break;
            case Api.Constants.Leads.Status.CONVERTED:
                this.elem.actions_wrapper.hide();
                this.elem.edit_button.hide();
                break;
            case Api.Constants.Leads.Status.DEAD:
                this.elem.convert_button.addClass('t-show');
                this.elem.work_button.addClass('t-show');
                break;
        }
        this.elem.status.append(content_tpl({
            content: this.state.config.status.get(data.status)
        }));

        this.elem.name.text(`${data.first_name} ${data.last_name}`);
        if (data.business_name !== null) {
            this.elem.business_content.removeClass('t-hidden');
            this.elem.business_name.text(data.business_name);
        }
        if (data.phone_number !== null) {
            this.elem.phone_content.removeClass('t-hidden');
            this.elem.phone.text(data.phone_number);
        }
        if (data.email !== null) {
            this.elem.email_content.removeClass('t-hidden');
            this.elem.email.text(data.email);
        }
        if (data.business_name !== null || data.phone_number !== null || data.email !== null) {
            this.elem.contact_content.removeClass('t-hidden');
        }
        if (data.address !== null || data.city !== null || data.state !== null || data.zip !== null) {
            let address = '';
            if (data.address !== null) {
                address = data.address;
            }
            if (data.address_2 !== null) {
                if (address !== '') {
                    address = `${address}, `;
                }
                address = `${address} ${data.address_2}`;
            }
            if (data.city !== null) {
                if (address !== '') {
                    address = `${address}<br>`;
                }
                address = `${address} ${data.city}`;
            }
            if (data.state !== null) {
                if (address !== '') {
                    address = `${address}, `;
                }
                address = `${address} ${data.state}`;
            }
            if (data.zip !== null) {
                if (address !== '') {
                    address = `${address}, `;
                }
                address = `${address} ${data.zip}`;
            }
            this.elem.address_content.removeClass('t-hidden');
            this.elem.address.append(content_tpl({
                content: address
            }));
        }

        if (data.project_type_id !== null) {
            this.elem.project_type_content.removeClass('t-hidden');
            this.elem.project_type.append(content_tpl({
                content: data.project_type_name
            }));
        }

        if (data.assigned_to_user_id !== null) {
            this.elem.assigned_to_content.removeClass('t-hidden');
            this.elem.assigned_to.append(content_tpl({
                content: data.assigned_to_user_name
            }));
        }

        if (data.marketing_type_id !== null) {
            this.elem.marketing_source_content.removeClass('t-hidden');
            this.elem.marketing_source.append(content_tpl({
                content: data.marketing_source
            }));
        }

        if (data.priority !== null) {
            this.elem.priority_content.removeClass('t-hidden');
            this.elem.priority.append(content_tpl({
                content: this.state.config.priority.get(data.priority)
            }));
        }

        if (data.origin !== null) {
            this.elem.origin_content.removeClass('t-hidden');
            this.elem.origin.append(content_tpl({
                content: this.state.config.origin.get(data.origin)
            }));
        }

        if (data.is_unsubscribed !== null) {
            this.elem.is_unsubscribed_content.removeClass('t-hidden');
            this.elem.is_unsubscribed.append(content_tpl({
                content: data.is_unsubscribed ? 'Yes' : 'No'
            }));
        }

        if (data.is_sent_notifications !== null) {
            this.elem.is_sent_notifications_content.removeClass('t-hidden');
            this.elem.is_sent_notifications.append(content_tpl({
                content: data.is_sent_notifications ? 'Yes' : 'No'
            }));
        }

        if (data.priority !== null || data.marketing_type_id !== null || data.assigned_to_user_id !== null || data.project_type_id !== null || data.origin !== null || data.is_unsubscribed !== null || data.is_sent_notifications !== null) {
            this.elem.details_content.removeClass('t-hidden');
        }

        if (data.customer !== null) {
            this.elem.customer_content.removeClass('t-hidden');
            let template = customer_link_tpl({
                content: `${data.customer.first_name} ${data.customer.last_name}`,
                link: window.fx_pages.CUSTOMER_MANAGEMENT.replace('{customer_id}', data.customer.id)
            });
            this.elem.customer.append(template);
        }
        if (data.notes !== null) {
            this.elem.notes_content.removeClass('t-hidden');
            this.elem.notes.append(notes_content_tpl({
                content: data.notes
            }));
        }
        if (data.working_notes !== null) {
            this.elem.working_notes_content.removeClass('t-hidden');
            this.elem.working_notes.append(notes_content_tpl({
                content: data.working_notes
            }));
        }
        if (data.dead_notes !== null) {
            this.elem.dead_notes_content.removeClass('t-hidden');
            this.elem.dead_notes.append(notes_content_tpl({
                content: data.dead_notes
            }));
        }
        if (data.notes !== null && data.working_notes !== null && data.dead_notes === null){
            this.elem.notes_line.removeClass('t-hidden');
        }
        if (data.notes !== null && data.working_notes === null && data.dead_notes !== null){
            this.elem.dead_notes_line.removeClass('t-hidden');
        }
        if (data.notes === null && data.working_notes !== null && data.dead_notes !== null){
            this.elem.dead_notes_line.removeClass('t-hidden');
        }
        if (data.notes !== null && data.working_notes !== null && data.dead_notes !== null){
            this.elem.notes_line.removeClass('t-hidden');
            this.elem.dead_notes_line.removeClass('t-hidden');
        }
        if (data.notes !== null || data.working_notes !== null || data.dead_notes !== null) {
            this.elem.notes_wrapper.removeClass('t-hidden');
        }

        this.state.lead_files = data.files || [];
        this.populateFiles();
    };

    /**
     * Populate a files section with existing files
     * @returns {void}
     */
    async populateFiles() {
        const files = Array.isArray(this.state.lead_files)
            ? this.state.lead_files
            : [];

        if (files.length === 0) {
            this.toggleNoFiles(true);
            return;
        }

        this.toggleNoFiles(false);
        await this.createTable();
        this.state.files_table.setData(files);
        this.state.files_table.build()
    };


    createTable() {
        if (!this.state.files_table) {
            if (this.state.no_files) {
                this.toggleNoFiles(false);
            }

            this.elem.files_table.show();
            this.state.files_table = new Table(this.elem.files_table, {
                paging_enabled: false,
            });

            this.state.files_table.setToolbar({
                filter: false,
                settings: false
            });

            this.state.files_table.setColumns({
                name: {
                    label: 'Name',
                    responsive: 1,
                    width: '70%',
                    orderable: false,
                    value: (data, type) => {
                        let name = this.state.files_table.trimColumn(data.name, 30, true, true);
                        return `${name}`;
                    }
                },
                size: {
                    label: 'Size',
                    width: '10%',
                    orderable: false,
                    value: (data, type) => type === 'display' || type === 'filter' ? filesize(data.file.size) : data.file.size
                },
                created_at: {
                    label: 'Uploaded At',
                    width: '20%',
                    orderable: false,
                    value: (data, type) => {
                        if (!data.created_at) {
                            return '';
                        }

                        let date = data.created_at;
                        if (type === 'display') {
                            return moment(date).tz(this.state.module.router.main_route.layout.user.timezone).format('MM/DD/YYYY h:mm a');
                        }
                        return date;
                    }
                },

            });

            this.state.files_table.setRowActions({
                view: {
                    label: 'View',
                    action: data => {
                        if (this.state.gallery_extensions.indexOf(data.file.extension) !== -1) {
                            this.openGallery(data.id);
                            return;
                        }
                        window.open(data.file_media_urls.original, '_blank');
                    }
                },
                delete: {
                    label: 'Delete',
                    action: async data => {
                        try {
                            const ok = await new Promise((resolve, reject) =>
                                new DeleteFileModal().open({
                                    file_id: data.id,
                                    file_name: data.name,
                                    promise: { resolve, reject },
                                    dry_run: false
                                })
                            );

                            if (ok) {
                                this.state.files_table.deleteRow(data.id);

                                if (this.state.files_table.getRows().length === 0) {
                                    this.toggleNoFiles(true);
                                }
                            }
                        } catch (err) {
                            console.error('Error deleting file:', err);
                        }
                    }
                },
            });

            this.state.files_table.setState({
                pagination: {
                    per_page: 5
                },
                sorts: {
                    name: Table.Sort.ASC
                }
            });

            this.state.files_table.on('row_click', data => {
                if (this.state.gallery_extensions.indexOf(data.file.extension) !== -1) {
                    this.openGallery(data.id);
                    return;
                }
                window.open(data.file_media_urls.original, '_blank');
            });
        }
    }

    /**
     * Toggle if no files display is shown
     *
     * If table is in use, then it will be destroyed.
     *
     * @param {boolean} [active=true]
     */
    toggleNoFiles(active = true) {
        if (this.state.no_files === active) {
            return;
        }
        if (active) {
            if (this.state.files_table === null) {
                return;
            }

            if (this.state.files_table) {
                this.state.files_table.destroy();
                this.state.files_table = null;
            }
            this.elem.files_table.hide();
        }
        this.state.no_files = active;
        this.elem.no_files.toggle(active);
    };

    /**
     * Open gallery of all images in table
     *
     * @param {string} file_id - UUID of file to start gallery on
     */
    openGallery(file_id) {

        if (this.state.files_table) {
            let rows = this.state.files_table.getRows(),
                images = [],
                file_index = null,
                index = 0;

            for (let i = 0; i < rows.length; i++) {
                let row = rows[i];
                if (this.state.gallery_extensions.indexOf(row.file.extension) === -1) {
                    continue;
                }
                images.push({
                    src: row.file_media_urls.original,
                    opts: {
                        caption: row.name,
                        thumb: row.file_media_urls.thumbnail
                    }
                });
                if (row.id === file_id) {
                    file_index = index;
                }
                index++;
            }
            $.fancybox.open(images, {
                buttons: ['thumbs', 'download', 'close'],
                thumbs: {
                    autoStart: true
                }
            }, file_index);

        }
    };

    /**
     * Fetch id for lead
     *
     * @param {string} lead_uuid
     * @returns {Promise<*>}
     */
    async fetchID(lead_uuid) {
        let {entities: leads} = await await Api.Resources.Leads().fields(['id', 'lead_uuid'])
            .filter('lead_uuid', lead_uuid)
            .all();
        let data = leads.map(lead => lead.data);

        return data[0];
    };

    /**
     * Open modal
     *
     * @param {object} $0
     * @param {string} $0.lead_id
     * @param {object} $0.promise
     * @returns {Modal}
     */
    async open({lead_id, promise}) {
        this.startWorking();
        let data = await this.fetchID(lead_id);

        Api.Resources.Leads()
            .accept('application/vnd.adg.fx.lead-v1+json')
            .retrieve(data.id)
            .then(({data}) => {
                this.state.lead = data;
                this.populate(data);
                this.resetWorking();
            }, error => {
                this.showErrorMessage('Unable to fetch lead info');
                console.log(error);
            });
        this.state.promise = promise;
        return super.open();
    };

    /**
     * Close modal
     *
     * @returns {module:Modal.Message}
     */
    close() {
        this.elem.contact_content.addClass('t-hidden');
        this.elem.phone_content.addClass('t-hidden');
        this.elem.email_content.addClass('t-hidden');
        this.elem.address_content.addClass('t-hidden');
        this.elem.project_type_content.addClass('t-hidden');
        this.elem.priority_content.addClass('t-hidden');
        this.elem.origin_content.addClass('t-hidden');
        this.elem.is_unsubscribed_content.addClass('t-hidden');
        this.elem.is_sent_notifications_content.addClass('t-hidden');
        this.elem.assigned_to_content.addClass('t-hidden');
        this.elem.marketing_source_content.addClass('t-hidden');
        this.elem.customer_content.addClass('t-hidden');
        this.elem.business_content.addClass('t-hidden');
        this.elem.details_content.addClass('t-hidden');
        this.elem.actions_wrapper.show();
        this.elem.edit_button.show();

        this.elem.notes_content.addClass('t-hidden');
        this.elem.notes_line.addClass('t-hidden');
        this.elem.dead_notes_content.addClass('t-hidden');
        this.elem.dead_notes_line.addClass('t-hidden');
        this.elem.working_notes_content.addClass('t-hidden');
        this.elem.notes_wrapper.addClass('t-hidden');

        this.elem.status.text('');
        this.elem.name.text('');
        this.elem.business_name.html('');
        this.elem.contact.text('');
        this.elem.phone.text('');
        this.elem.email.text('');
        this.elem.address.text('');
        this.elem.project_type.text('');
        this.elem.priority.text('');
        this.elem.origin.text('');
        this.elem.assigned_to.text('');
        this.elem.marketing_source.text('');
        this.elem.customer.text('');
        this.elem.notes.html('');
        this.elem.dead_notes.html('');
        this.elem.working_notes.html('');
        this.elem.is_unsubscribed.html('');
        this.elem.is_sent_notifications.html('');

        this.elem.convert_button.removeClass('t-show');
        this.elem.work_button.removeClass('t-show');
        this.elem.kill_button.removeClass('t-show');

        this.elem.view_tasks_link.hide();
        this.elem.view_tasks_link.text('View Tasks');

        if (this.state.files_table) {
            this.state.files_table.destroy();
            this.state.files_table = null;
        }

        super.close();
        if (this.state.promise !== null) {
            this.state.promise.resolve();
            this.state.promise = null;
        }
        return this;
    };
}

module.exports = Details;
