'use strict';

const Inputmask = require("inputmask");

const Api = require('@ca-package/api');

const FormInput = require('@ca-submodule/form-input');
FormInput.use(require('@ca-submodule/form-input/src/dynamic_dropdown'));
FormInput.use(require('@ca-submodule/form-input/src/static_dropdown'));
FormInput.use(require('@ca-submodule/form-input/src/wysiwyg'));
FormInput.use(require('@ca-submodule/form-input/src/checkbox'));
const Modal = require('@ca-submodule/modal').Base;
const Tooltip = require('@ca-submodule/tooltip');

const {createSuccessMessage} = require('@cas-notification-toast-js/message/success');
const {createErrorMessage} = require('@cas-notification-toast-js/message/error');

const FormValidator = require('@ca-submodule/validator');

const {initSelectPlaceholder} = require('@cac-js/utils/select_placeholder');

const states = require('@cac-js/data/states');
const us_territories = require('@cac-js/data/us_territories');
const provinces = require('@cac-js/data/provinces');

const modal_tpl = require('@cam-lead-tpl/modals/main/manager/manage_lead.hbs');
const marketing_tpl = require('@cam-lead-tpl/modals/main/manager/manage-lead/marketing.hbs');
const project_type_tpl = require('@cam-lead-tpl/modals/main/manager/manage-lead/project_type.hbs');
const files_tpl = require('@cam-lead-tpl/modals/main/manager/manage-lead/file.hbs');
const DeleteFileModal = require("@cam-lead-js/modals/main/manager/delete_file");

const ManageLeadAPI = require('./manage_lead/api');
const Uploader = require('./manage_lead/uploader');

const Actions = {
    ADD: 1,
    DUPLICATE: 2,
    EDIT: 3
};
const Priorities = {
    HOT: {
        label: 'Hot',
        value: Api.Constants.Leads.Priority.HOT
    },
    WARM: {
        label: 'Warm',
        value: Api.Constants.Leads.Priority.WARM
    },
    COLD: {
        label: 'Cold',
        value: Api.Constants.Leads.Priority.COLD
    },
    DEAD: {
        hidden: true,
        label: 'Dead',
        value: Api.Constants.Leads.Priority.DEAD
    }
};

function iconByExt (ext) {
    switch (ext) {
        case 'txt':  return 'editor--text-snippet';
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':  return 'media--image-line';
        default:     return 'document--file-pdf-2-line';
    }
}


/**
 * @memberof module:Lead/Modals
 */
class ManageLead extends Modal {

    static get Action() {
        return Actions;
    };

    constructor(module) {
        super(modal_tpl({
            priorities: Priorities,
            states,
            us_territories,
            provinces
        }), {
            size: Modal.Size.TINY,
            classes: ['t-manage-lead']
        });
        Object.assign(this.state, {
            module,
            action: null,
            lead: null,
            lead_files: [],
            external_close: false,
            active_users: null,
            assign_to_users: new Map,
            editor: null,
            marketing_types: null,
            marketing_types_dropdown: null,
            project_types: null,
            project_types_dropdown: null,
            gallery_extensions: ['jpg', 'jpeg', 'png', 'gif']
        });
        this.addAction({
            type: Modal.Action.CANCEL,
            handler: () => this.close()
        });
        this.state.save_action = this.addAction({
            type: Modal.Action.SAVE,
            label: 'Save',
            handler: () => this.elem.form.submit()
        });

        Tooltip.initAll(this.elem.content);
        this.elem.form = this.elem.content.fxFind('form');
        this.elem.input = {};
        for (let name of [
            'project_type_id', 'priority', 'marketing_type_id', 'assigned_to_user_id', 'email', 'phone_number',
            'business_name', 'first_name', 'last_name', 'address', 'address_2', 'city', 'state', 'zip', 'notes',
            'is_unsubscribed', 'is_sent_notifications'
        ]) {
            this.elem.input[name] = this.elem.form.fxFind(name);
        }

        this.state.is_unsubscribed = FormInput.init(this.elem.input.is_unsubscribed, {});

        this.elem.input.is_unsubscribed.on('change.fx', () => {
            if (this.state.is_unsubscribed.checked) {
                this.state.is_sent_notifications.setChecked(false);
                this.state.is_sent_notifications.setDisabled(true);
            } else {
                this.state.is_sent_notifications.setChecked(true);
                this.state.is_sent_notifications.setDisabled(false);
            }
        });

        this.state.is_sent_notifications = FormInput.init(this.elem.input.is_sent_notifications, {});
        this.elem.welcome_email_checkbox = this.elem.form.fxFind('welcome-email-checkbox');

        this.state.uppy = null;
        this.elem.btn_add_files = this.elem.form.fxFind('upload-title-section');

        this.state.validator = FormValidator.create(this.elem.form, {
            project_type_id: {},
            priority: {},
            marketing_type_id: {},
            assigned_to_user_id: {},
            email: {
                maxlength: 100,
                type: 'email'
            },
            phone_number: {
                pattern: '^\\(\\d{3}\\)\\s\\d{3}-\\d{4}',
                patternMessage: 'Number must be formatted as (XXX) XXX-XXXX.'
            },
            business_name: {
                maxlength: 200
            },
            first_name: {
                maxlength: 50,
                required: true
            },
            last_name: {
                maxlength: 50,
                required: true
            },
            address: {
                maxlength: 100
            },
            address_2: {
                maxlength: 100
            },
            city: {
                maxlength: 50
            },
            state: {
                maxlength: 15
            },
            zip: {
                maxlength: 12
            },
            notes: {
                maxlength: 50000
            },
            is_unsubscribed: {},
            is_sent_notifications: {},
        })
            .on('submit', () => {
                this.save();
                return false;
            });

        Inputmask({
            "mask": "(*************"
        }).mask(this.elem.input.phone_number);

        initSelectPlaceholder(this.state.validator.getInputElem('state'));
        initSelectPlaceholder(this.state.validator.getInputElem('priority'));
        initSelectPlaceholder(this.state.validator.getInputElem('marketing_type_id'));
        initSelectPlaceholder(this.state.validator.getInputElem('project_type_id'));

        this.on('close', () => {
            if (this.state.promise !== null && !this.state.external_close) {
                this.state.promise.resolve(null);
            }
            this.reset();
        });

        this.elem.form.on('click', '.file-remove-btn', async (e) => {
            const $btn   = $(e.currentTarget);
            const $item  = $btn.closest('.file-item');
            const fileId = $item.data('file-id');
            const name   = $item.data('file-name');

            if (!fileId) return;

            try {
                const edit_mode = this.state.action === Actions.EDIT && this.state.lead?.id;

                const ok = await new Promise((resolve, reject) =>
                    new DeleteFileModal().open({
                        file_id: fileId,
                        file_name: name,
                        promise: { resolve, reject },
                        dry_run: !edit_mode
                    })
                );

                if (ok) {
                    $item.remove();

                    this.state.lead_files = this.state.lead_files.filter(f => f.id !== fileId);
                    this.state.uppy.removeFile?.(fileId);
                    Uploader.updateUppyRestrictions(this.state.uppy, this.state.lead_files);

                    if ($('.c-ci-image .file-item').length === 0) {
                        $('[data-js="thumbnail"]').hide();
                    }
                }
            } catch (err) {
                console.error('Error deleting file:', err);
            }
        });
    };

    /**
     * Create dropdown of projects
     *
     * @returns {Promise<boolean>}
     */
    async createAssignToDropdown() {
        let input = this.state.validator.getInputElem('assigned_to_user_id');
        input.prop('disabled', true);

        let assign_to_input = FormInput.init(input, {
            data_provider: () => {
                let users = [];
                for (let user of this.state.active_users) {
                    users.push({
                        id: user.id,
                        text: `${user.first_name} ${user.last_name}`
                    });
                }
                return users;
            },
            placeholder: '-- Select One --',
            closeOnSelect: true,
        });
        await assign_to_input.promise;
        input.prop('disabled', false);
        return assign_to_input;
    };

    async loadWysiwyg() {
        if (this.state.editor === null) {
            let textarea_config = {
                preset: 'simple',
                height: 200,
                remove_empty_paragraphs: true,
                no_image: true
            };
            this.state.notes = FormInput.init(this.state.validator.getInputElem('notes'), textarea_config);
            this.state.editor = await this.state.notes.promise;
        }
        return this.state.editor;
    };

    initUppy() {
        if (this.state.uppy) {
            this.state.uppy.close();
        }

        const edit_mode = this.state.action === Actions.EDIT && this.state.lead?.id;
        this.state.uppy = Uploader.initUppy(edit_mode, this.state.lead_files || []);

        if (edit_mode) {
            Uploader.addXHRUpload(this.state.uppy);
        }

        this.elem.btn_add_files.on('click', e => {
            e.preventDefault();
            const allowed = this.state.uppy.opts.restrictions.maxNumberOfFiles;
            if (allowed <= 0) {
                let message = createErrorMessage('You have reached the maximum of 5 files.');
                this.state.module.router.main_route.layout.toasts.addMessage(message);
                return;
            }
            const dashboard = this.state.uppy.getPlugin('Dashboard');
            dashboard.openModal();
        });

        this.state.uppy.on('file-added', (file) => {
            if (edit_mode) {
                this.state.uppy.setFileMeta(file.id, {lead_id: parseInt(this.state.lead.id)});
            }
            else {
                const svg = `<svg data-icon><use xlink:href="#remix-icon--${iconByExt(file.extension)}"></use></svg>`;
                $('.c-ci-image').append(files_tpl({
                    files: [{
                        id: file.id,
                        name: file.name,
                        icon_html: svg
                    }]
                }));
                $('.c-ci-image').show();
            }
        })

        this.state.uppy.on('upload-success', (file, response) => {

            let data
            if (response.status === 200) {
                data = response.body.data;
            } else if (response.status === 201) {
                data = response;
            } else {
                console.error('Upload failed:', response);
                return;
            }

            this._appendFileItem({
                id: data.id,
                name: data.name,
                extension: file.extension
            })
        })

        this.state.uppy.on('complete', async (result) => {
            if (edit_mode) {
                this.state.lead_files = await ManageLeadAPI.fetchLeadFiles(this.state.lead.id);
                this.populateFiles();
                Uploader.updateUppyRestrictions(this.state.uppy, this.state.lead_files);
                this.state.uppy.reset();
            }
        })
    };

    populateMarketingTypes() {
        if (this.state.marketing_types_dropdown === null) {
            let dropdown = marketing_tpl({
                marketing_types: this.state.marketing_types
            });
            this.state.marketing_types_dropdown = dropdown;
            this.elem.input.marketing_type_id.append(dropdown);
        }
    };

    populateProjectTypes() {
        if (this.state.project_types_dropdown === null) {
            let dropdown = project_type_tpl({
                project_types: this.state.project_types
            });
            this.state.project_types_dropdown = dropdown;
            this.elem.input.project_type_id.append(dropdown);
        }
        this.elem.input.project_type_option = this.elem.form.fxFind('project_type_option');
    };

    checkOptions(value) {
        let value_exists = false;
        this.elem.input.project_type_option.each(function() {
            if (this.value === value) {
                value_exists = true;
                return false;
            }
        });
        return value_exists;
    };

    async populate() {
        const data = this.state.lead;
        if (data.priority !== null) {
            this.elem.input.priority.val(data.priority).trigger('change');
        }

        if (data.project_type_id !== null && this.checkOptions(data.project_type_id)) {
            this.elem.input.project_type_id.val(data.project_type_id).trigger('change');
        }

        if (data.assigned_to_user_id !== null) {
            this.elem.input.assigned_to_user_id.val(data.assigned_to_user_id).trigger('change');
        }

        if (data.marketing_type_id !== null) {
            this.elem.input.marketing_type_id.val(data.marketing_type_id).trigger('change');
        }

        if (data.notes !== null) {
            this.elem.input.notes.val(data.notes).trigger('change');
        }

        if (data.email !== null) {
            this.elem.input.email.val(data.email);
        }

        if (data.is_unsubscribed !== null) {
            this.elem.input.is_unsubscribed.prop('checked', data.is_unsubscribed);
            this.elem.input.is_unsubscribed.trigger('change');
        }

        if (data.is_sent_notifications !== null) {
            this.elem.input.is_sent_notifications.prop('checked', data.is_sent_notifications);
            this.elem.input.is_sent_notifications.trigger('change');
        }

        if (data.phone_number !== null) {
            this.elem.input.phone_number.val(data.phone_number);
        }

        if (data.business_name !== null) {
            this.elem.input.business_name.val(data.business_name);
        }

        if (data.first_name !== null) {
            this.elem.input.first_name.val(data.first_name);
        }

        if (data.last_name !== null) {
            this.elem.input.last_name.val(data.last_name);
        }

        if (data.address !== null) {
            this.elem.input.address.val(data.address);
        }

        if (data.address_2 !== null) {
            this.elem.input.address_2.val(data.address_2);
        }

        if (data.city !== null) {
            this.elem.input.city.val(data.city);
        }

        if (data.state !== null) {
            this.elem.input.state.val(data.state);
        }

        if (data.zip !== null) {
            this.elem.input.zip.val(data.zip);
        }

        this.state.lead_files = data.files || [];
        this.populateFiles();
    };

    /**
     * Populate a files section with existing files
     * @returns {void}
     */
    populateFiles() {
        const raw_files = Array.isArray(this.state.lead_files)
            ? this.state.lead_files
            : [];

        const files = raw_files.map(file => {
            const ext = file.file.extension.toLowerCase();
            const svg = `<svg data-icon><use xlink:href="#remix-icon--${iconByExt(ext)}"></use></svg>`;

            return {
                id:   file.id,
                name: file.name,
                icon_html: svg
            };
        });

        // Render files data to HTML.
        $('.c-ci-image').html(files_tpl({ files: files }));
        $('.c-ci-image').show();
    };

    _appendFileItem({ id, name, extension }) {
        const svg = `<svg data-icon><use xlink:href="#remix-icon--${iconByExt(extension)}"></use></svg>`;
        $('.c-ci-image').append(files_tpl({
            files: [{
                id: id,
                name: name,
                icon_html: svg
            }]
        }));
        $('.c-ci-image').show();
    }


    /**
     * Open modal
     *
     * @param {object} $0
     * @param {object} $0.config
     * @param {Promise} $0.promise
     */
    async open({config, promise}) {
        this.state.promise = promise;
        this.state.action = config.action;

        // Load all necessary data and state for this Modal to work.
        await this.load();

        if (config.action === Actions.ADD) {
            this.setTitle('Create Lead');
        }
        else if (config.action === Actions.EDIT) {
            this.setTitle('Edit Lead');
            if (config.lead_id) {
                try {
                    this.startWorking();
                    const lead_data = await ManageLeadAPI.fetchID(config.lead_id);

                    const entity = await Api.Resources.Leads()
                        .accept('application/vnd.adg.fx.lead-v1+json')
                        .retrieve(lead_data.id);

                    this.state.lead = entity.get();
                    await this.populate();
                } catch (error) {
                    console.log(error);
                    this.showErrorMessage('Unable to fetch lead info');
                } finally {
                    this.resetWorking();
                }
            }
            this.elem.welcome_email_checkbox.hide();
        }
        this.initUppy();
        const $thumb = this.elem.form.fxFind('thumbnail');
        if (this.state.lead_files.length > 0) $thumb.show();
        else $thumb.hide();

        super.open();
    };

    /**
     * Load data for the modal
     *
     * @returns {Promise<void>}
     */
    async load() {
        await this.loadWysiwyg();

        this.state.active_users = await ManageLeadAPI.getUsers();
        this.state.marketing_types = await ManageLeadAPI.fetchMarketingSources();
        this.populateMarketingTypes(this.state.marketing_types);

        this.state.project_types      = await ManageLeadAPI.fetchProjectTypes();
        this.populateProjectTypes();

        await this.createAssignToDropdown();
    };

    /**
     * Pull data from form inputs
     *
     * @returns {object}
     */
    buildEntity() {
        let entity = {};

        let priority = this.state.validator.getInputElem('priority').val(),
            project_type_id = this.state.validator.getInputElem('project_type_id').val(),
            assigned_to_user_id = this.state.validator.getInputElem('assigned_to_user_id').val(),
            marketing_type_id = this.state.validator.getInputElem('marketing_type_id').val(),
            email = this.state.validator.getInputElem('email').val(),
            phone_number = this.state.validator.getInputElem('phone_number').val(),
            business_name = this.state.validator.getInputElem('business_name').val(),
            first_name = this.state.validator.getInputElem('first_name').val(),
            last_name = this.state.validator.getInputElem('last_name').val(),
            address = this.state.validator.getInputElem('address').val(),
            address_2 = this.state.validator.getInputElem('address_2').val(),
            city = this.state.validator.getInputElem('city').val(),
            state = this.state.validator.getInputElem('state').val(),
            zip = this.state.validator.getInputElem('zip').val(),
            notes = this.state.validator.getInputElem('notes').val(),
            is_unsubscribed = this.state.validator.getInputElem('is_unsubscribed').is(':checked'),
            is_sent_notifications = this.state.validator.getInputElem('is_sent_notifications').is(':checked');

        entity['priority'] = priority !== '' ? parseInt(priority) : null;
        entity['project_type_id'] = project_type_id !== '' ? project_type_id : null;
        entity['assigned_to_user_id'] = assigned_to_user_id !== '' ? parseInt(assigned_to_user_id) : null;
        entity['marketing_type_id'] = marketing_type_id !== '' ? parseInt(marketing_type_id) : null;
        entity['email'] = email !== '' ? email : null;
        entity['phone_number'] = phone_number !== '' ? phone_number : null;
        entity['business_name'] = business_name !== '' ? business_name : null;
        entity['first_name'] = first_name !== '' ? first_name : null;
        entity['last_name'] = last_name !== '' ? last_name : null;
        entity['address'] = address !== '' ? address : null;
        entity['address_2'] = address_2 !== '' ? address_2 : null;
        entity['city'] = city !== '' ? city : null;
        entity['state'] = state !== '' ? state : null;
        entity['zip'] = zip !== '' ? zip : null;
        entity['notes'] = notes !== '' ? notes : null;
        entity['is_unsubscribed'] = is_unsubscribed;

        if (this.state.action === Actions.ADD) {
            if (is_unsubscribed) {
                is_sent_notifications = false;
            }
            entity['is_sent_notifications'] = is_sent_notifications;
            entity['origin'] = Api.Constants.Leads.Origin.STANDARD;
        }

      return entity;
    };

    /**
     * Save changes
     */
    async save() {
        this.startWorking();
        let payload = this.buildEntity();

        let resource = Api.Resources.Leads(),
            request = this.state.action === Actions.ADD
                ? resource.store(payload)
                : resource.partialUpdate(this.state.lead.id, payload);

        try {
            const { data: lead } = await request;
            this.state.lead = lead;

            // XHRUpload is not present only on ADD action. It's necessary to manually upload files.
            if (!this.state.uppy.getPlugin('XHRUpload')) {
                Uploader.addXHRUpload(this.state.uppy);
                await Uploader.manualUppyUpload(this.state.uppy, this.state.lead.id);
            }

            this.state.promise.resolve(payload);
            this.state.promise = null;
            this.close();
        } catch (error) {
            console.log(error);
            let message = createErrorMessage('Unable to save lead, please contact support');
            this.state.module.router.main_route.layout.toasts.addMessage(message);
        } finally {
            this.resetWorking();
        }
    };

    /**
     * Reset modal to default state
     */
    reset() {
        this.elem.content.scrollTop(0);
        this.resetWorking();
        this.state.action = null;
        this.state.lead = null;
        this.state.validator.reset();

        if (this.state.uppy) {
            this.state.uppy.close();
            this.state.uppy = null;
        }

        this.elem.input.marketing_type_id.val('').trigger('change');
        this.elem.input.project_type_id.val('').trigger('change');

        this.elem.welcome_email_checkbox.show();

        this.elem.input.is_unsubscribed.prop('checked', false);
        this.elem.input.is_unsubscribed.trigger('change');
        this.elem.input.is_sent_notifications.prop('checked', true);
        this.elem.input.is_sent_notifications.trigger('change');

        this.elem.form[0].reset();
    };

}

module.exports = ManageLead;
