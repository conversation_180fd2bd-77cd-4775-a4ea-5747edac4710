<?php

declare(strict_types=1);

namespace App\Http\ViewBuilders;

use App\Classes\Color;
use App\Resources\AppNotificationDistributionResource;
use App\Resources\AppNotificationResource;
use Common\Models\Company;
use Common\Models\WisetackMerchant;
use App\Services\{CompanyFeatureService, DomainService, WisetackService};
use Carbon\Carbon;
use Common\Models\Feature;
use Core\Classes\Config;
use Core\Components\Auth\Classes\Auth;
use Core\Components\Http\Classes\URI;
use Common\Models\User as UserModel;
use Core\Components\Http\Interfaces\{RequestInterface, RouterInterface, ViewInterface};
use Ramsey\Uuid\Uuid;

/**
 * Class LayoutAppBuilder
 *
 * @package App\Http\ViewBuilders
 */
class LayoutAppBuilder
{
    /**
     * LayoutAppBuilder constructor
     *
     * @param Auth $auth
     * @param RequestInterface $request
     * @param Config $config
     * @param DomainService $domain_service
     * @param RouterInterface $router
     */
    public function __construct(
        protected Auth $auth,
        protected RequestInterface $request,
        protected Config $config,
        protected DomainService $domain_service,
        protected RouterInterface $router
    ) {}

    /**
     * Build navigation config
     *
     * @param array $items
     * @param string|null $current_route
     * @param URI $uri
     * @return array
     * @throws \Core\Components\Http\Exceptions\URIException
     */
    protected function buildNav(array $items, ?string $current_route, URI $uri): array
    {
        $active = false;
        foreach ($items as &$item) {
            if (isset($item['menu'])) {
                [$item['menu'], $item['active']] = $this->buildNav($item['menu'], $current_route, $uri);
            } elseif (isset($item['route'])) {
                $item['active'] = $item['route']['name'] === $current_route;
                $item['url'] = $uri->route($item['route']['name'], $item['route']['data'] ?? [])->build();
                unset($item['route']);
            } else {
                $item['active'] = false;
            }
            if ($item['active']) {
                $active = true;
            }
            unset($item);
        }
        return [$items, $active];
    }


    /**
     * Create the reporting menu based on user and Wisetack configuration
     *
     * @param object $user
     * @param CompanyFeatureService $feature_service
     * @return array
     */
    protected function createReportingMenu($user, $feature_service): array
    {
        $reporting_menu = [];

        if ($user->primary || $user->metrics) {
            $reporting_menu['metrics'] = [
                'title' => 'Metrics',
                'route' => [
                    'name' => 'page.app.metrics',
                ]
            ];

            if ($feature_service->has(Feature::CUSTOM_REPORTS)) {
                $uri = $this->request->uri();
                $reporting_menu['custom-reports'] = [
                    'title' => 'Custom Reports',
                    'icon' => 'remix-icon--system--list-settings-line',
                    'url' => $uri->route('page.app.custom-reports', ['path' => ''])->build(),
                    'beta' => true
                ];
            }

            $merchant = WisetackMerchant::where('companyUUID', $user->company->companyUUID)
                ->whereNull('deletedAt')
                ->first();

            if ($merchant) {
                $is_financing_enabled = WisetackService::isWisetackEnabledAndApproved($user->companyID, $merchant);

                if ($is_financing_enabled) {
                    $reporting_menu['financing'] = [
                        'title' => 'Financing',
                        'route' => [
                            'name' => 'page.app.financing',
                        ]
                    ];
                }
            }
        }

        return $reporting_menu;
    }


    /**
     * Retrieve user notifications and separate them into banners and notification center.
     *
     * @param UserModel $user
     * @return array
     */
    protected function getUserNotifications(UserModel $user): array
    {
        try {
            $banner_notifications = [];
            $notification_center_notifications = [];

            $feature_service = new CompanyFeatureService($user->companyID);
            $is_notification_center_enabled = $feature_service->has(Feature::NOTIFICATION_CENTER, true);

            if ($is_notification_center_enabled) {
                $notifications = AppNotificationDistributionResource::getUserNotifications($user);

                foreach ($notifications as $distribution) {
                    $notification = $distribution->notification;
                    if (!$notification) {
                        // Skip if no related notification exists
                        continue;
                    }

                    $notification->distribution = [
                        'id' => $distribution->id,
                        'status' => $distribution->status,
                    ];

                    switch (true) {
                        case $notification->placement === AppNotificationResource::PLACEMENT_GLOBAL:
                            $banner_notifications[] = $notification;
                            $notification_center_notifications[] = $notification;
                            break;
                        case $notification->placement === AppNotificationResource::PLACEMENT_BANNER:
                            $banner_notifications[] = $notification;
                            break;
                        case $notification->placement === AppNotificationResource::PLACEMENT_NOTIFICATION_CENTER:
                            $notification_center_notifications[] = $notification;
                            break;
                    }
                }
            }

            return [
                'is_notification_center_enabled' => $is_notification_center_enabled,
                'banner_notifications' => $banner_notifications,
                'notification_center_notifications' => $notification_center_notifications,
            ];
        } catch (\Throwable $e) {
            error_log('Error fetching user notifications: ' . $e->getMessage());
            return [
                'is_notification_center_enabled' => false,
                'banner_notifications' => [],
                'notification_center_notifications' => [],
            ];
        }
    }

    /**
     * Build vars for view
     *
     * @param ViewInterface $view
     * @throws \Core\Components\Http\Exceptions\URIException
     * @throws \Core\Exceptions\AppException
     */
    public function build(ViewInterface $view): void
    {
        $user = $this->auth->user();
        $uri = $this->request->uri();
        $session = $this->request->session();

        $is_demo_mode = $this->request->input()->cookie('__DEMO_MODE__') === 'true';

        // [Notifications Center and Banner]
        $notifications = $this->getUserNotifications($user);
        $view->set('is_notification_center_enabled', $notifications['is_notification_center_enabled']);
        $view->set('banner_notifications', $notifications['banner_notifications']);
        $view->set('notification_center_notifications', $notifications['notification_center_notifications']);
        $view->set('notification_center_count', count($notifications['notification_center_notifications']));



        $admin_user_id = $session->get('adminUserID');
        $view->set('is_trial', $user->companyStatus === Company::STATUS_TRIAL);
        $view->set('has_new_button', $user->primary || $user->projectManagement || $user->sales);
        $view->set('is_primary', $user->primary);
        $view->set('is_impersonating', !$is_demo_mode && $admin_user_id !== null && $user->userID != $admin_user_id);
        $view->set('company_profile_link', $uri->route('page.app.company.profile', ['path' => '/general'])->build());

        $env = SERVER_ROLE;
        $env_color = null;
        if ($env !== 'PROD' && !$is_demo_mode) {
            $env_color = match($env) {
                'STAGING' => 'hotpink',
                'DEV' => 'blue',
                'LOCAL' => '#db00fb',
                default => null
            };
        }
        $view->set('env_color', $env_color);

        $helphero = $this->config->get('helphero');
        $layout = [
            'env' => $env,
            'restricted' => $this->auth->get('restricted', false),
            'config' => [
                'walkthrough' => [
                    'enabled' => $helphero['enabled'],
                    'app_id' => $helphero['app_id'],
                    'user_id_env' => $helphero['user_id_env']
                ]
            ],
            'message' => $this->request->session()->flash('layout_message')
        ];

        $domain = $this->domain_service->current();
        $view_user = [
            'id' => $user->userID,
            'uuid' => Uuid::fromBytes($user->userUUID)->toString(),
            'first_name' => $user->userFirstName,
            'last_name' => $user->userLastName,
            'email' => $user->userEmail,
            'timezone' => $user->systemTimezone,
            'created_at' => $user->userAdded->format('c'),
            'days_since_creation' => $user->userAdded->diffInDays(Carbon::now('UTC')),
            'brand_slug' => $domain['brand']['slug'],
            'brand_name' => $domain['brand']['name'],
            'company_name' => $user->companyName,
            'company_bg_color' => $user->companyColor,
            'company_fg_color' => Color::getContrastColor($user->companyColor),
            'is_trial' => $user->companyStatus === Company::STATUS_TRIAL,
            'trial_expires' => $user->companyTrialExpiresAt,
            'notifications' => [
                'banner' => $notifications['banner_notifications'],
                'notification_center' => $notifications['notification_center_notifications'],
                'is_notification_center_enabled' => $notifications['is_notification_center_enabled']
            ]
        ];
        $roles = [
            'primary' => 'primary',
            'sales_management' => 'projectManagement',
            'sales' => 'sales',
            'installation' => 'installation',
            'bid_creation' => 'bidCreation',
            'bid_verification' => 'bidVerification',
            'marketing' => 'marketing',
            'metrics' => 'metrics'
        ];
        foreach ($roles as $role => $column) {
            $view_user["role_{$role}"] = $user->{$column};
        }
        $layout['user'] = $view_user;

        $feature_service = new CompanyFeatureService($user->companyID);

        $main_nav = $crm_menu = $sales_menu = [];

        if ($user->isTraining) {
            $main_nav['training'] = [
                'title' => 'Training',
                'route' => [
                    'name' => 'page.app.training',
                    'data' => ['path' => '']
                ],
                'button' => true
            ];
        }

        if ($user->primary && $user->isCompanySetupWizard) {
            $main_nav['setup-wizard'] = [
                'title' => 'Setup',
                'route' => [
                    'name' => 'page.app.setup-wizard',
                    'data' => ['path' => '']
                ],
                'button' => true
            ];
        }

        $crm_user = $user->primary || $user->projectManagement || $user->sales;
        // crm menu
        if ($feature_service->has(Feature::TASKS, true)) {
            $crm_menu['tasks'] = [
                'title' => 'Tasks',
                'route' => [
                    'name' => 'page.app.tasks',
                    'data' => ['path' => '?_sorts=due_date&status=eq:1&_per_page=25&_page=1']
                ]
            ];
        }
        if ($feature_service->has(Feature::LEADS, true) && $crm_user) {
            $crm_menu['leads'] = [
                'title' => 'Leads',
                'route' => [
                    'name' => 'page.app.leads',
                    'data' => ['path' => '?_sorts=-created_at&status=in:1|2&_per_page=25&_page=1']
                ]
            ];
        }
        if ($crm_user) {
            $crm_menu['customers'] = [
                'title' => 'Customers',
                'route' => [
                    'name' => 'page.app.customers',
                    'data' => ['path' => '?_sorts=first_name,last_name&_per_page=25&_page=1']
                ]
            ];
            $crm_menu['properties'] = [
                'title' => 'Properties',
                'route' => [
                    'name' => 'page.app.properties',
                    'data' => ['path' => '?_sorts=created_at&_per_page=25&_page=1']
                ]
            ];

            // sales menu
            $crm_menu['projects'] = [
                'title' => 'Projects',
                'route' => [
                    'name' => 'page.app.projects',
                    'data' => ['path' => '?_sorts=created_at&status=eq:1&_per_page=25&_page=1']
                ]
            ];
        }

        $todays_date = Carbon::now('UTC')->format("Y-m-d");
        $crm_menu['appointments'] = [
            'title' => 'Appointments',
            'route' => [
                'name' => 'page.app.appointments',
                'data' => ['path' => "?_sorts=start&start=gt:{$todays_date}T00:00:00Z&status=eq:1&_per_page=25&_page=1"]
            ]
        ];

        if (count($crm_menu) > 0) {
            $main_nav['crm'] = [
                'title' => 'CRM',
                'class' => 't-accordion',
                'menu' => $crm_menu
            ];
        }

        if ($feature_service->has(Feature::DRAWING_APP, true)) {
            $sales_menu['drawing'] = [
                'title' => 'Drawings',
                'route' => [
                    'name' => 'page.app.drawings',
                    'data' => ['path' => '']
                ]
            ];
        }
//        $sales_menu['bids'] = [
//            'title' => 'Bids',
//            'route' => [
//                'name' => 'page.app.appointments',
//                'data' => ['path' => '']
//            ]
//        ];

        // @todo insert Invoices
        if (count($sales_menu) > 0) {
            $main_nav['sales'] = [
                'title' => 'Sales',
                'class' => 't-accordion',
                'menu' => $sales_menu
            ];
        }

        if ($user->primary || $user->marketing) {
            $main_nav['marketing'] = [
                'title' => 'Marketing',
                'route' => [
                    'name' => 'page.app.marketing'
                ]
            ];
        }
        if (
            $feature_service->has(Feature::CREW_MANAGEMENT, false) &&
            ($user->primary || $user->timecardApprover)
        ) {
            $main_nav['crew-management'] = [
                'title' => 'Crew Management',
                'url' => $uri->create()->path('crew-management.php')->build()
            ];
        }

        $reporting_menu = $this->createReportingMenu($user, $feature_service);

        if (!empty($reporting_menu)) {
            $main_nav['reporting'] = [
                'title' => 'Reporting',
                'class' => 't-accordion',
                'menu' => $reporting_menu
            ];
        }

        $current_route = $this->router->getCurrentRoute()?->getName();
        [$layout['main_nav']] = $this->buildNav($main_nav, $current_route, $uri);

        $layout['new_menu'] = [];

        if ($user->primary) {
            $layout['new_menu']['products'] = [
                'title' => 'New Product',
                'icon' => 'remix-icon--others--box-3-line',
                'url' => $uri->route('page.app.company.profile', ['path' => '/products/create'])->build()
            ];
        }

        if ($feature_service->has(Feature::TASKS, true)) {
            $layout['new_menu']['tasks'] = [
                'title' => 'New Task',
                'icon' => 'remix-icon--system--check-line',
                'url' => $uri->route('page.app.tasks', ['path' => '/create'])->build()
            ];
        }
        if ($feature_service->has(Feature::LEADS, true) && $crm_user) {
            $layout['new_menu']['leads'] = [
                'title' => 'New Lead',
                'icon' => 'remix-icon--business--inbox-archive-line',
                'url' => $uri->route('page.app.leads', ['path' => '/create'])->build()
            ];
        }

        if ($crm_user) {
            $layout['new_menu']['customer'] = [
                'title' => 'New Customer',
                'icon' => 'remix-icon--user & faces--user-line',
                'url' => $uri->route('page.app.customer-add')->build()
            ];
        }

        $user_quick_links = [
            'profile' => [
                'title' => 'Profile',
                'icon' => 'remix-icon--user & faces--account-circle-line',
                'url' => $uri->route('page.app.user.profile', ['path' => ''])->build()
            ]
        ];
        if ($feature_service->has(Feature::MEDIA_LIBRARY, true)) {
            $user_quick_links['media'] = [
                'title' => 'Media',
                'icon' => 'remix-icon--document--folders-line',
                'url' => $uri->route('page.app.media-library')->build()
            ];
        }
        $user_quick_links['help'] = [
            'title' => 'Help',
            'icon' => 'remix-icon--business--customer-service-line',
            'url' => 'https://support.contractoraccelerator.com',
            'attrs' => ['target' => '_blank']
        ];

        $user_menu_1 = [];
        if ($user->primary) {
            $user_menu_1['company-profile'] = [
                'title' => 'Company Profile',
                'icon' => 'remix-icon--system--settings-3-line',
                'url' => $uri->route('page.app.company.profile', ['path' => '/general'])->build()
            ];
            $user_menu_1['subscription-billing'] = [
                'title' => 'Subscription & Billing',
                'icon' => 'remix-icon--finance--money-dollar-circle-line',
                'url' => $uri->route('page.app.company.account', ['path' => ''])->build()
            ];
        }
        if ($feature_service->has(Feature::CUSTOM_REPORTS) && ($user->primary || $user->metrics)) {
            $user_menu_1['custom-reports'] = [
                'title' => 'Custom Reports',
                'icon' => 'remix-icon--system--list-settings-line',
                'url' => $uri->route('page.app.custom-reports', ['path' => ''])->build(),
                'beta' => true
            ];
        }

        $layout['user_menu'] = [
            'quick_links' => $user_quick_links,
            'menu_1' => $user_menu_1,
            'menu_2' => [
                'logout' => [
                    'title' => 'Logout',
                    'icon' => 'remix-icon--system--logout-circle-r-line',
                    'url' => $uri->route('page.auth.logout')->build()
                ]
            ]
        ];

        /** @var \Core\Components\Asset\Classes\ViewHelper $asset */
        $asset = $view->helper('asset');
        $asset->rawStyle(<<<CSS
        :root {
            --company-bg-color: {$layout['user']['company_bg_color']};
            --company-fg-color: {$layout['user']['company_fg_color']};
        }
        CSS);
        $asset->scriptData('layout', $layout);
        $view->set('restricted', $layout['restricted']);
    }
}
