<?php
declare(strict_types=1);

namespace App\Http\Middleware;

use Core\Components\Http\Exceptions\HttpResponseException;
use Core\Components\Http\Interfaces\RequestInterface;
use RateLimit\Rate;
use RateLimit\RedisRateLimiter;
use RateLimit\Exception\LimitExceeded;
use App\Classes\Queue\Drivers\RedisDriver;

class ApiRateLimitMiddleware
{
    public function __construct(RedisDriver $driver)
    {
        $this->redis = $driver->getRedisClient();
    }
    /**
     * @throws HttpResponseException
     */
    public function handle(RequestInterface $request, \Closure $next)
    {

        $apiKey = trim($request->input()->header('X-Ca-Lead-Api-Key', ''));
        if ($apiKey === '') {
            return $next($request);
        }

        $limiter = new RedisRateLimiter(Rate::perMinute(100), $this->redis);
        try {
            $limiter->limit($apiKey);
        } catch (LimitExceeded $e) {
            throw new HttpResponseException(
                429,
                'Too Many Requests. Please retry in 60 seconds.'
            );
        }

        return $next($request);
    }
}