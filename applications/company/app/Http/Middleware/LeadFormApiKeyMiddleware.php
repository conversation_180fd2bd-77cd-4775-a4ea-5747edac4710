<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Classes\LeadFormLogger;
use Common\Models\LeadForm;
use Core\Components\Http\Classes\Response;
use Core\Components\Http\Exceptions\HttpResponseException;
use Core\Components\Http\Interfaces\RequestInterface;
use Ramsey\Uuid\Uuid;

/**
 * Class LeadFormApiKeyMiddleware
 *
 * This middleware validates the lead‐form API key and logs each request
 * and response into the leadFormRequests table via LeadFormLogger.
 */
class LeadFormApiKeyMiddleware
{
    private LeadFormLogger $logger;

    public function __construct(LeadFormLogger $logger)
    {
        $this->logger = $logger;
    }

    /**
     * @throws HttpResponseException
     */
    public function handle(RequestInterface $request, \Closure $next)
    {
        $api_key = trim($request->input()->header('X-Ca-Lead-Api-Key', ''));

        if (!$api_key) {
            return $next($request);
        }

        try {
            $api_key_bytes = Uuid::fromString($api_key)->getBytes();
        } catch (\Throwable $e) {
            $this->logErrorResponse($request, 'Malformed API Key', 400);
            throw new HttpResponseException(400, 'Malformed API Key.');
        }

        $leadForm = LeadForm::where('apiKey', $api_key_bytes)
            ->where('isActive', true)
            ->first();

        if (!$leadForm) {
            $this->logErrorResponse($request, 'Invalid API Key', 403);
            throw new HttpResponseException(403, 'Invalid API Key.');
        }

        $request->setDatum('is_api_request', true);
        $request->setDatum('lead_form_by_api', $leadForm);
        $request->setDatum('api_key_bytes', $api_key_bytes);

        $this->logger->request($request);
        $response = $next($request);

        $this->logger->response($response);
        return $response;
    }

    /**
     * Log an error response for the lead form API key validation.
     *
     * @param RequestInterface $request
     * @param string $message
     * @param int $statusCode
     */
    private function logErrorResponse(RequestInterface $request, string $message, int $statusCode): void
    {
        $errorPayload = ['error' => $message];
        $errorContent = json_encode($errorPayload, JSON_UNESCAPED_SLASHES);
        $errorResponse = new Response($errorContent, $statusCode, $request);
        $this->logger->response($errorResponse);
    }
}