<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Classes\ApiLogger;
use Common\Models\LeadFormRequest;

class LeadFormRequestLogger extends ApiLogger
{
protected function saveLog(array $data): void
{
    if ($this->model === null) {
        $this->model = new LeadFormRequest($data);
    } else {
        $this->model->fill($data);
    }
        $this->model->quietSave();
    }
}