<?php

namespace App\Http\Controllers\ApiV1\Project;

use App\Resources\Project\EventResource;
use App\Traits\Resource\Controller\ActionTrait;

class EventController
{
    use ActionTrait;

    protected $resource = EventResource::class;

    public function __construct()
    {
        $this->registerFormat('event-collection-v1', 'application/vnd.adg.fx.event-collection-v1+json');
        $this->registerFormat('event-collection-limited-v1', 'application/vnd.adg.fx.event-collection-limited-v1+json');
        $this->registerFormat('drawing-app-v1', 'application/vnd.adg.fx.drawing-app-v1+json');
        $this->registerFormat('drawing-app-v1-sync', 'application/vnd.adg.fx.drawing-app-v1-sync+json');
    }
}
