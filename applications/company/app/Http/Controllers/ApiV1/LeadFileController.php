<?php

declare(strict_types=1);

namespace app\Http\Controllers\ApiV1;

use App\Resources\Lead\FileResource;
use App\Traits\Resource\Controller\PolyActionTrait;

class LeadFileController
{
    use PolyActionTrait;

    protected $resource = FileResource::class;

    public function __construct()
    {
        $this->registerFormat('collection-v1', 'application/vnd.adg.fx.collection-v1+json');
    }
}
