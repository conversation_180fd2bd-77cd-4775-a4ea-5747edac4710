<?php

namespace App\Http\Controllers\ApiV1;

use App\Resources\LeadResource;
use App\Traits\Resource\Controller\ActionTrait;

class LeadController
{
    use ActionTrait;

    protected $resource = LeadResource::class;

    public function __construct()
    {
        $this->registerFormat('collection-v1', 'application/vnd.adg.fx.collection-v1+json');
        $this->registerFormat('collection-v2', 'application/vnd.adg.fx.collection-v2+json');
        $this->registerFormat('lead-v1', 'application/vnd.adg.fx.lead-v1+json');
        $this->registerFormat('lead-files-v1', 'application/vnd.adg.fx.lead-files-v1+json');
    }
}
