<?php

namespace App\Http\Controllers\App\API\Project;

use App\Classes\Log;
use App\NotificationJobs\Project\Event\CreatedNotificationJob;
use App\ResourceJobs\Project\Event\CalendarPushJob;
use App\Resources\Project\EventResource;
use Carbon\Carbon;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Classes\Entity;
use Exception;

class EventController
{
    /**
     * Add event for project
     *
     * This is a port of the old event-add.php legacy ajax page.
     *
     * @param RequestInterface $request
     * @return \Core\Components\Http\Responses\JSONResponse
     */
    public function add(RequestInterface $request)
    {
        $user = Auth::user();

        if (!$user->primary && !$user->projectManagement && !$user->sales && !$user->installation) {
            return Response::json(['error' => 'Access denied'], 403);
        }

        try {
            $all_day = $request->get('startTimeAllDay') === '1';

            try {
                $start_at = Carbon::parse(
                    $request->get('startDate') . ' ' . ($all_day ? '00:00:00' : $request->get('startTime')),
                    $user->systemTimezone
                );
                $end_at = Carbon::parse(
                    $request->get('endDate') . ' ' . ($all_day ? '23:59:59' : $request->get('endTime')),
                    $user->systemTimezone
                );
            } catch (Exception $e) {
                throw new Exception('Unable to parse start or end date', 0, $e);
            }

            $event_type_map = EventResource::getTypeMap();
            $event_type = $request->get('scheduleType');
            if (!isset($event_type_map[$event_type])) {
                throw new Exception('Invalid schedule type');
            }
            $event_type = $event_type_map[$event_type];

            $event = Entity::make([
                'source' => EventResource::SOURCE_PROJECT_SCHEDULE,
                'project_id' => (int) $request->get('projectID', 0),
                'type' => $event_type,
                'user_id' => (int) $request->get('scheduledUserID', 0),
                'start_at' => $start_at->toIso8601String(),
                'end_at' => $end_at->toIso8601String(),
                'is_all_day' => $all_day,
                'description' => $request->get('description'),
                'is_pending' => false,
                'send_notifications' => $request->get('notifyCustomer') === '1'
            ]);

            if ($event['user_id'] === 0) {
                return Response::json(['success' => false, 'error' => 'Unable to schedule this event with a company.']);
            }

            EventResource::make(Auth::acl())->create($event)->run();

            return Response::json(['success' => true]);
        } catch (Exception $e) {
            Log::create('project_event_add')->error('Unable to add project event', [
                'exception' => $e
            ]);
            return Response::json(['error' => 'Unable to add project event'], 500);
        }
    }

    /**
     * Reschedule event
     *
     * @param RequestInterface $request
     * @return \Core\Components\Http\Responses\JSONResponse
     */
    public function reschedule(RequestInterface $request)
    {
        $user = Auth::user();

        if (!$user->primary && !$user->projectManagement && !$user->sales && !$user->installation) {
            return Response::json(['status' => 0, 'error' => ['code' => 1, 'message' => 'Access denied']], 403);
        }

        // if reschedule comes from a drag and drop event, we verify the user is allowed to do that
        $drag_and_drop = $request->get('dragAndDrop') === '1';
        if ($drag_and_drop) {
            $allowed = false;
            $allowed_roles = ['primary', 'projectManagement'];
            foreach ($allowed_roles as $allowed_role) {
                if (!$user[$allowed_role]) {
                    continue;
                }
                $allowed = true;
                break;
            }
            if (!$allowed) {
                return Response::json(['status' => 0, 'error' => ['code' => 2, 'message' => 'User does not have permission to use drag and drop feature']]);
            }
        }

        try {
            $all_day = $request->get('startTimeAllDay') === '1';

            try {
                $start_at = Carbon::parse(
                    $request->get('startDate') . ' ' . ($all_day ? '00:00:00' : $request->get('startTime')),
                    $user->systemTimezone
                );
                $end_at = Carbon::parse(
                    $request->get('endDate') . ' ' . ($all_day ? '23:59:59' : $request->get('endTime')),
                    $user->systemTimezone
                );
            } catch (Exception $e) {
                throw new Exception('Unable to parse start or end date', 0, $e);
            }

            $event_type_map = EventResource::getTypeMap();
            $event_type = $request->get('scheduleType');
            if (!isset($event_type_map[$event_type])) {
                throw new Exception('Invalid schedule type');
            }
            $event_type = $event_type_map[$event_type];

            $event = Entity::make([
                'source' => $drag_and_drop ? EventResource::SOURCE_CALENDAR_RESCHEDULE : EventResource::SOURCE_PROJECT_RESCHEDULE,
                'type' => $event_type,
                'user_id' => (int) $request->get('scheduledUserID', 0),
                'start_at' => $start_at->toIso8601String(),
                'end_at' => $end_at->toIso8601String(),
                'is_all_day' => $all_day,
                'description' => $request->get('description'),
                'is_pending' => $drag_and_drop,
                'send_notifications' => $request->get('notifyCustomer') === '1'
            ]);
            if (!$drag_and_drop) {
                $event['project_id'] = (int) $request->get('projectID', 0);
            }

            if ($event['user_id'] === 0) {
                return Response::json(['success' => false, 'error' => 'Unable to schedule this event with a company.']);
            }

            $old_event_id = (int) $request->get('projectScheduleID', 0);

            $id = EventResource::make(Auth::acl())->reschedule($old_event_id, $event);

            return Response::json(['status' => 1, 'id' => $id]);
        } catch (Exception $e) {
            Log::create('project_event_add')->error('Unable to add project event', [
                'exception' => $e
            ]);
            return Response::json(['status' => 0, 'error' => ['code' => $e->getCode(), 'message' => 'Unable to add project event']], 500);
        }
    }

    /**
     * Edit Description
     *
     * Edit only the event description
     *
     * @param RequestInterface $request
     * @return \Core\Components\Http\Responses\JSONResponse
     */
    public function edit(RequestInterface $request)
    {
        $user = Auth::user();

        if (!$user->primary && !$user->projectManagement && !$user->sales && !$user->installation) {
            return Response::json(['error' => 'Access denied'], 403);
        }

        try {
            EventResource::make(Auth::acl())->partialUpdate(Entity::make([
                'id' => (int) $request->get('projectScheduleID', 0),
                'description' => $request->get('description', null),
            ]))->run();

            return Response::json(['success' => true]);
        } catch (Exception $e) {
            Log::create('project_event_complete')->error('Unable to edit project event', [
                'exception' => $e
            ]);
            return Response::json(['error' => 'Unable to edit project event'], 500);
        }
    }

    /**
     * Resend notifications for event
     *
     * This is a port of the old resend-schedule.php legacy ajax page.
     *
     * @param RequestInterface $request
     * @return \Core\Components\Http\Responses\JSONResponse
     */
    public function resendNotifications(RequestInterface $request)
    {
        $user = Auth::user();

        if (!$user->primary && !$user->projectManagement && !$user->sales && !$user->installation) {
            return Response::json(['error' => 'Access denied'], 403);
        }

        $event_id = (int) $request->get('idToResend', 0);
        if (!EventResource::make(Auth::acl())->entityExists($event_id)) {
            return Response::json(['error' => 'Project event not found'], 404);
        }

        try {
            CreatedNotificationJob::enqueue($event_id);

            return Response::json(['success' => true]);
        } catch (Exception $e) {
            Log::create('project_event_resend_notifications')->error('Unable to resend project event notifications', [
                'exception' => $e
            ]);
            return Response::json(['error' => 'Unable to resend project event notifications'], 500);
        }
    }

    /**
     * Push event to any associated calendars if possible
     *
     * This is a port of the old event-sync-google.php legacy ajax page.
     *
     * @param RequestInterface $request
     * @return \Core\Components\Http\Responses\JSONResponse
     * @throws \Core\Components\Resource\Exceptions\EntityNotFoundException
     */
    public function calendarPush(RequestInterface $request)
    {
        $user = Auth::user();

        if (!$user->primary && !$user->projectManagement && !$user->sales && !$user->installation) {
            return Response::json(['error' => 'Access denied'], 403);
        }

        $event_id = (int) $request->get('projectScheduleID', 0);
        if (($event = EventResource::make(Auth::acl())->find($event_id)) === null) {
            return Response::json(['error' => 'Project event not found'], 404);
        }

        try {
            // set push status to in progress before we enqueue job, this ensures the sync related ui buttons are
            // disabled while the job is processing
            $event->isCalendarPushInProgress = true;
            $event->save();

            CalendarPushJob::enqueue($event_id);

            return Response::json(['success' => true]);
        } catch (Exception $e) {
            Log::create('project_event_calendar_push')->error('Unable to push project event to calendar', [
                'exception' => $e
            ]);
            return Response::json(['error' => 'Unable to push project event to calendar'], 500);
        }
    }

    /**
     * Cancel event
     *
     * This is a port of the old event-cancel.php legacy ajax page.
     *
     * @param RequestInterface $request
     * @return \Core\Components\Http\Responses\JSONResponse
     */
    public function cancel(RequestInterface $request)
    {
        $user = Auth::user();

        if (!$user->primary && !$user->projectManagement && !$user->sales && !$user->installation) {
            return Response::json(['error' => 'Access denied'], 403);
        }

        try {
            EventResource::make(Auth::acl())->partialUpdate(Entity::make([
                'id' => (int) $request->get('projectScheduleID', 0),
                'status' => EventResource::STATUS_CANCELLED
            ]))->run();

            return Response::json(['success' => true]);
        } catch (Exception $e) {
            Log::create('project_event_cancel')->error('Unable to cancel project event', [
                'exception' => $e
            ]);
            return Response::json(['error' => 'Unable to cancel project event'], 500);
        }
    }

    /**
     * Complete event
     *
     * This is a port of the old completeInstallation.php legacy ajax page
     *
     * @param RequestInterface $request
     * @return \Core\Components\Http\Responses\JSONResponse
     */
    public function complete(RequestInterface $request)
    {
        $user = Auth::user();

        if (!$user->primary && !$user->projectManagement && !$user->sales && !$user->installation) {
            return Response::json(['error' => 'Access denied'], 403);
        }

        try {
            EventResource::make(Auth::acl())->partialUpdate(Entity::make([
                'id' => (int) $request->get('projectScheduleID', 0),
                'status' => EventResource::STATUS_COMPLETED
            ]))->run();

            return Response::json(['success' => true]);
        } catch (Exception $e) {
            Log::create('project_event_complete')->error('Unable to complete project event', [
                'exception' => $e
            ]);
            return Response::json(['error' => 'Unable to complete project event'], 500);
        }
    }

    /**
     * Mark event as active
     *
     * This is a port of the old completeInstallation.php legacy ajax page
     *
     * @param RequestInterface $request
     * @return \Core\Components\Http\Responses\JSONResponse
     */
    public function active(RequestInterface $request)
    {
        $user = Auth::user();

        if (!$user->primary && !$user->projectManagement && !$user->sales && !$user->installation) {
            return Response::json(['error' => 'Access denied'], 403);
        }

        try {
            EventResource::make(Auth::acl())->partialUpdate(Entity::make([
                'id' => (int) $request->get('projectScheduleID', 0),
                'status' => EventResource::STATUS_ACTIVE
            ]))->run();

            return Response::json(['success' => true]);
        } catch (Exception $e) {
            Log::create('project_event_active')->error('Unable to activate project event', [
                'exception' => $e
            ]);
            return Response::json(['error' => 'Unable to activate project event'], 500);
        }
    }
}
