<?php

namespace App\Http\Controllers\App\API;

use App\Classes\Func;
use App\Classes\Log;
use App\NotificationJobs\Customer\CreatedNotificationJob;
use App\ResourceMediaHandlers\Project\FileHandler;
use App\Resources\Customer\PhoneResource;
use App\Resources\CustomerResource;
use App\Resources\Lead\FileResource;
use App\Resources\Project\EventResource;
use App\Resources\Project\NoteResource;
use App\Resources\ProjectResource;
use App\Resources\Project\FileResource AS ProjectFileResource;
use App\Resources\PropertyResource;
use App\Resources\SearchResource;
use App\Services\CustomerSearchService;
use Carbon\Carbon;
use Common\Models\User;
use Core\Classes\Arr;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\RequestValidation\Classes\Validation as RequestValidation;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Validation;
use Core\Exceptions\AppException;
use Core\StaticAccessors\Config;
use Core\StaticAccessors\Path;
use Exception;

/**
 * Class CustomerController
 *
 * @package App\Http\Controllers\App\API
 */
class CustomerController
{
    /**
     * Check if info matches an existing customer
     *
     * @param RequestValidation $validation
     * @return \Core\Components\Http\Responses\JSONResponse
     */
    public function duplicateCustomer(RequestValidation $validation)
    {
        $json = [
            'status' => 1
        ];
        try {
            $user = Auth::user();

            $config = FieldConfig::fromArray([
                'businessName' => [
                    'label' => 'businessName',
                    'rules' => 'trim|optional|max_length[200]'
                ],
                'firstName' => [
                    'label' => 'firstName',
                    'rules' => 'trim|required|max_length[50]'
                ],
                'lastName' => [
                    'label' => 'lastName',
                    'rules' => 'trim|required|max_length[50]'
                ],
                'address' => [
                    'label' => 'address',
                    'rules' => 'trim|required|max_length[100]'
                ],
                'address2' => [
                    'label' => 'address2',
                    'rules' => 'trim|optional|max_length[100]'
                ],
                'city' => [
                    'label' => 'city',
                    'rules' => 'trim|required|max_length[50]'
                ],
                'state' => [
                    'label' => 'state',
                    'rules' => 'trim|required|in_array[states]'
                ],
                'zip' => [
                    'label' => 'zip',
                    'rules' => 'trim|required|max_length[11]'
                ],
                'email' => [
                    'label' => 'email',
                    'rules' => 'trim|optional|email|max_length[100]'
                ],
                'phone' => [
                    'label' => 'phone',
                    'rules' => 'trim|required|max_length[15]'
                ]
            ]);
            $lists = Config::get('lists');
            $config->store('states', array_merge(array_keys($lists['states']), array_keys($lists['us_territories']), array_keys($lists['provinces'])));

            $validation->config($config);

            $validator = $validation->run();

            //strip out any address street types
            $street_types = [
                'Alley', 'Allee', 'Ally', 'Aly', 'Av', 'Avenue', 'Ave', 'Aven', 'Avenu', 'Avn', 'Avnue', 'Boulevard',
                'Boul', 'Boulv', 'Blvd', 'Causeway', 'Causwa', 'Cswy', 'Center', 'Cen', 'Cent', 'Centr', 'Centre',
                'Cnter', 'Cntr', 'Ctr', 'Circle', 'Circ', 'Circl', 'Crcl', 'Crcle', 'Cir', 'Court', 'Ct', 'Cove', 'Cv',
                'Crossing', 'Crssng', 'Xing', 'Drive', 'Driv', 'Drv', 'Dr', 'Expressway', 'Expr', 'Express', 'Expw',
                'Expy', 'Exp', 'Extension', 'Extn', 'Extnsn', 'Ext', 'Freeway', 'Freewy', 'Frway', 'Frwy', 'Fwy',
                'Grove', 'Groves', 'Grv', 'Grov', 'Highway', 'Highwy', 'Hiway', 'Hiwy', 'Hway', 'Hwy', 'Hollow', 'Hollows',
                'Holw', 'Holws', 'Hllw', 'Junction', 'Jction', 'Jctn', 'Junctn', 'Juncton', 'Jct', 'Lane', 'Ln',
                'Motorway', 'Mtwy', 'Overpass', 'Opas', 'Park', 'Prk', 'Parkway', 'Parkwy', 'Pkway', 'Pkwy', 'Pky',
                'Place', 'Pl', 'Plaza', 'Plz', 'Plza', 'Point', 'Pt', 'Road', 'Rd', 'Route', 'Rte', 'Skyway', 'Skwy',
                'Square', 'Sqr', 'Sqre', 'Squ', 'Sq', 'Street', 'Strt', 'St', 'Str', 'Terrace', 'Terr', 'Ter', 'Trail',
                'Trails', 'Trl', 'Trls', 'Way', 'Wy'
            ];
            $sub_address = str_replace($street_types, "", $validator->data('address'));

            if (!$validator->valid()) {
                $json['result'] = [
                    'errors' => $validator->errors()->get()
                ];
                throw new AppException('Validation Failed');
            }

            $query_customers = $user->company->customers()->withPrimaryPhone()
                ->select([
                    DB::raw('1 AS type'), 'customer.customerID', 'firstName', 'lastName', 'businessName',
                    'ownerAddress AS address', 'ownerAddress2 AS address2', 'ownerCity AS city', 'ownerState AS state',
                    'ownerZip AS zip', 'email', 'phoneNumber'
                ])
                ->where(function ($query) use ($validator, $sub_address) {
                    if (($businessName = $validator->data('businessName', '')) !== '') {
                        $query->where('businessName', $businessName);
                    }
                    $query->orWhere(function ($query) use ($validator) {
                        $query->where('firstName',$validator->data('firstName'))->where('lastName', $validator->data('lastName'));
                    })
                        ->orWhere(function ($query) use ($validator, $sub_address) {
                            $query->where('ownerAddress', 'LIKE', "%{$sub_address}%");
                        if (($address2 = $validator->data('address2', '')) !== '') {
                            $query->where('ownerAddress2', $address2);
                        }
                        $query->where('ownerCity', $validator->data('city'))
                        ->where('ownerState', $validator->data('state'))
                        ->where('ownerZip', $validator->data('zip'));
                    })
                    ->orWhere('phoneNumber', $validator->data('phone'));
                    if (($email = $validator->data('email', '')) !== '') {
                        $query->orWhere(function ($query) use ($email) {
                            $query->where('email', $email);
                        });
                    }
                });

            $json['result'] = $query_customers->get()->toArray();

            // add property type
            $query_properties = $user->company->customers()->withPrimaryPhone()->withProperties()
                ->select([
                             DB::raw('2 AS type'), 'customer.customerID', 'firstName', 'lastName', 'businessName',
                             'property.address AS address', 'property.address2 AS address2', 'property.city AS city', 'property.state AS state',
                             'property.zip AS zip', 'email', 'phoneNumber'
                         ])
                ->where(function ($query) use ($validator, $sub_address) {
                    if (($businessName = $validator->data('businessName', '')) !== '') {
                        $query->where('businessName', $businessName);
                    }
                    $query->orWhere(function ($query) use ($validator) {
                        $query->where('firstName',$validator->data('firstName'))->where('lastName', $validator->data('lastName'));
                    })
                        ->orWhere(function ($query) use ($validator, $sub_address) {
                            $query->where('property.address', 'LIKE', "%{$sub_address}%");
                            if (($address2 = $validator->data('address2', '')) !== '') {
                                $query->where('property.address2', $address2);
                            }
                            $query->where('property.city', $validator->data('city'))
                                ->where('property.state', $validator->data('state'))
                                ->where('property.zip', $validator->data('zip'));
                        })
                        ->orWhere('phoneNumber', $validator->data('phone'));
                    if (($email = $validator->data('email', '')) !== '') {
                        $query->orWhere(function ($query) use ($email) {
                            $query->where('email', $email);
                        });
                    }
                });
            $properties = $query_properties->get()->toArray();
            foreach ($properties as $property) {
                array_push($json['result'], $property);
            }

            $query_leads = $user->company->leads()
                ->select([DB::raw('3 AS type'), DB::raw('HEX(leads.leadUUID) AS id'), 'firstName', 'lastName', 'businessName',
                             'address', 'address2', 'city', 'state', 'zip', 'email', 'phoneNumber'
                         ])
                ->where(function ($query) use ($validator, $sub_address) {
                    if (($businessName = $validator->data('businessName', '')) !== '') {
                        $query->where('businessName', $businessName);
                    }
                    $query->orWhere(function ($query) use ($validator) {
                        $query->where('firstName',$validator->data('firstName'))->where('lastName', $validator->data('lastName'));
                    })
                        ->orWhere(function ($query) use ($validator, $sub_address) {
                            $query->where('address', 'LIKE', "%{$sub_address}%");
                            if (($address2 = $validator->data('address2', '')) !== '') {
                                $query->where('address2', $address2);
                            }
                            $query->where('city', $validator->data('city'))
                                ->where('state', $validator->data('state'))
                                ->where('zip', $validator->data('zip'));
                        })
                        ->orWhere('phoneNumber', $validator->data('phone'));
                    if (($email = $validator->data('email', '')) !== '') {
                        $query->orWhere(function ($query) use ($email) {
                            $query->where('email', $email);
                        });
                    }
                });
            $leads = $query_leads->get()->toArray();
            foreach ($leads as $lead) {
                array_push($json['result'], $lead);
            }

        } catch (AppException $e) {
            $json['status'] = 0;
            $json['error'] = [
                'code' => $e->getCode(),
                'message' => $e->getMessage()
            ];
        }

        return Response::json($json);
    }

    /**
     * Search for customers
     *
     * @param RequestInterface $request
     * @return \Core\Components\Http\Responses\JSONResponse
     */
    public function search(RequestInterface $request)
    {
        $json = ['status' => 1];
        try {
            $source_map = [
                'customer' => SearchResource::SOURCE_CUSTOMER_V1,
                'property-move' => SearchResource::SOURCE_PROPERTY_MOVE_V1
            ];

            $config = FieldConfig::fromArray([
                'term' => [
                    'label' => 'Term',
                    'rules' => 'trim|required|max_length[200]'
                ],
                'source' => [
                    'label' => 'Source',
                    'rules' => 'nullable|optional|in_array[sources]'
                ]
            ]);
            $config->store('sources', array_keys($source_map));
            $validation = Validation::make()->config($config);

            $validator = $validation->run($request->input()->get());
            if (!$validator->valid()) {
                throw new AppException('No term defined');
            }
            if (($source = $validator->data('source')) === null) {
                $source = 'customer';
            }
            $service = new CustomerSearchService(Auth::acl(), $validator->data('term'), $source_map[$source]);
            $json['result'] = $service->run()->toArray();
        } catch (AppException $e) {
            $json['status'] = 0;
            $json['error'] = [
                'code' => $e->getCode(),
                'message' => $e->getMessage()
            ];
        }

        return Response::json($json);
    }

    /**
     * Handle adding customer
     *
     * This is a port of the submitNewCustomer.php legacy ajax page, modified with some improvements. Still needs work,
     * but is the first phase to fully using the api.
     *
     * @param User $user
     * @param RequestInterface $request
     * @return int
     * @throws Exception
     */
    protected function handleAdd(User $user, RequestInterface $request)
    {
        // collect and validate data
        // handle customer data
        $customer = Entity::make([
            'business_name' => $request->get('businessName'),
            'first_name' => $request->get('firstName'),
            'last_name' => $request->get('lastName'),
            'email' => $request->get('email'),
            'address' => $request->get('ownerAddress'),
            'address_2' => $request->get('ownerAddress2'),
            'city' => $request->get('ownerCity'),
            'state' => $request->get('ownerState'),
            'zip' => $request->get('ownerZip'),
            'is_duplicate_match_shown' => (int) $request->get('isDuplicateMatchShown', 0) === 1,
            'is_unsubscribed' => !!$request->get('unsubscribed', 0),
        ]);

        if ($request->get('leadID')) {
            $customer['lead_id'] = (int) $request->get('leadID');
        }

        if ((int) $request->get('noEmailRequired', 0) === 1) {
            $customer->email = null;
        }

        // handle customer phone data
        $phone_data = json_decode($request->get('customerPhoneArray', ''), true);
        $phones = [];
        if (is_array($phone_data)) {
            $type_map = [
                'Cell' => PhoneResource::TYPE_CELL,
                'CellNoText' => PhoneResource::TYPE_CELL_TEXT_OPT_OUT,
                'Home' => PhoneResource::TYPE_HOME,
                'Work' => PhoneResource::TYPE_WORK,
                'Other' => PhoneResource::TYPE_OTHER
            ];
            foreach ($phone_data as $phone) {
                $type = $desc = Arr::get($phone, 'phoneDescription');
                if (!isset($type_map[$type])) {
                    $type = 'Other';
                }
                if ($type === 'CellNoText') {
                    $desc = 'Cell';
                }
                $phones[] = [
                    'type' => $type_map[$type],
                    'number' => Arr::get($phone, 'phoneNumber'),
                    'description' => $desc,
                    'is_primary' => array_key_exists('isPrimary', $phone) ? (int) $phone['isPrimary'] === 1 : false
                ];
            }
        }
        $customer->phones = $phones;

        // handle property data
        $property = Entity::make([
            'address' => $request->get('address'),
            'address_2' => $request->get('address2'),
            'city' => $request->get('city'),
            'state' => $request->get('state'),
            'zip' => $request->get('zip'),
            'county' => $request->get('county'),
            'township' => $request->get('township'),
            'latitude' => $request->get('latitude'),
            'longitude' => $request->get('longitude')
        ]);

        // if customer address is empty, then pull from property
        if (empty($customer->address) || empty($customer->city) || empty($customer->state) || empty($customer->zip)) {
            $customer->address = $property->address;
            $customer->address_2 = $property->address_2;
            $customer->city = $property->city;
            $customer->state = $property->state;
            $customer->zip = $property->zip;
        }

        // handle project data
        $project = Entity::make([
            'description' => $request->get('projectDescription'),
            'salesperson_user_id' => Func::emptyToNull($request->get('projectSalesperson')),
            'referral_marketing_type_id' => Func::emptyToNull($request->get('referralMarketingTypeID')),
            'secondary_marketing_type_id' => Func::emptyToNull($request->get('projectSecondaryMarketingTypeID')),
            'summary' => Func::emptyToNull($request->get('projectSummary')),
            'type' => Func::emptyToNull($request->get('projectType')),
            'priority' => Func::emptyToNull($request->get('projectPriority'))
        ]);
        if (($alt_description = $request->get('projectDescriptionArray', '')) !== '') {
            $project->description = str_replace(',', ', ', $alt_description);
        }
        // if a project salesperson is defined, then we cast it's value to an integer since form data is always a string
        if ($project->salesperson_user_id !== null) {
            $project->salesperson_user_id = (int) $project->salesperson_user_id;
        }
        if ($project->referral_marketing_type_id !== null) {
            $project->referral_marketing_type_id = (int) $project->referral_marketing_type_id;
        }

        if ($project->secondary_marketing_type_id !== null) {
            $project->secondary_marketing_type_id = (int) $project->secondary_marketing_type_id;
        }

        if ($project->priority !== null) {
            $project->priority = (int) $project->priority;
        }

        // handle project contact data
        $project_contacts = [];
        $contact_data = json_decode($request->get('emailArray', ''), true);
        if (is_array($contact_data)) {
            foreach ($contact_data as $contact) {
                $project_contacts[] = [
                    'name' => Arr::get($contact, 'name'),
                    'phone_number' => Arr::get($contact, 'phone'),
                    'email' => Arr::get($contact, 'email')
                ];
            }
        }
        if (count($project_contacts) > 0) {
            $project->contacts = $project_contacts;
        }

        // handle project note
        $project_note = null;
        if (($note_text = trim($request->get('projectNote', ''))) !== '') {
            $project_note = Entity::make([
                'note' => $note_text,
                'tag' => 'PC',
                'is_pinned' => $request->get('projectNotePin') === '1'
            ]);
        }

        try {
            $start_at = Carbon::parse(
                $request->get('scheduledStartDate') . ' ' . $request->get('scheduledStartTimeFormatted'),
                $user->systemTimezone
            );
            $end_at = Carbon::parse(
                $request->get('scheduledEndDate') . ' ' . $request->get('scheduledEndTimeFormatted'),
                $user->systemTimezone
            );
        } catch (Exception $e) {
            throw new Exception('Unable to parse start or end date', 0, $e);
        }

        // handle appointment event
        $event = null;
        if (is_numeric($scheduled_user_id = $request->get('salesperson', ''))) {
            $event = Entity::make([
                'source' => EventResource::SOURCE_CUSTOMER_ADD,
                'type' => EventResource::TYPE_EVALUATION,
                'user_id' => (int) $scheduled_user_id,
                'start_at' => $start_at->toIso8601String(),
                'end_at' => $end_at->toIso8601String(),
                'is_all_day' => false,
                'description' => $request->get('scheduleDescription'),
                'is_pending' => false,
                'send_notifications' => $request->get('notifyCustomerAppointment') === '1'
            ]);
        }

        // store entities
        $acl = Auth::acl();

        // create customer
        $customer_id = CustomerResource::make($acl)->create($customer)->run();

        //@todo Update Lead status to converted and set priority

        // create property
        $property->customer_id = $customer_id;
        $property_id = PropertyResource::make($acl)->create($property)->run();

        // create project
        $project->property_id = $property_id;
        $project->customer_id = $customer_id;
        $project_resource = ProjectResource::make($acl);
        $project_id = $project_resource->create($project)->run();

        // create project note if defined
        if ($project_note !== null) {
            $project_note['project_id'] = $project_id;
            NoteResource::make($acl)->create($project_note)->run();
        }

        if ($event !== null) {
            $event['project_id'] = $project_id;
            EventResource::make($acl)->create($event)->run();
        }

        // if it's a customer coming from a lead we need to find the lead files and copy them to project files
        if ($customer['lead_id']) {
            try {
                $lead_scope = Scope::make()
                    ->fields(['id', 'file_id'])
                    ->with(['file'])
                    ->filter('lead_id', 'eq', $customer['lead_id']);

                $lead_files = FileResource::make($acl)->collection()->scope($lead_scope)->run();
                $file_resource = $project_resource->relationResource('files');

                /** @var FileHandler $file_handler */
                $file_handler = $file_resource->getMediaHandler('file');
                $project_file_resource = ProjectFileResource::make($acl);
                $root_path = Path::get('company', $user->companyID);

                foreach ($lead_files as $file) {
                    $file_path = $root_path . 'lead-files/original/' . $file->file_id;
                    $file_path_copy = $root_path . 'lead-files/original/' . $file->file_id.'-copy';

                    if (!copy($file_path, $file_path_copy)) {
                        throw new Exception('Unable to copy lead file');
                    }
                    $file_id = $file_handler->createFileOnly(Entity::make([
                        'name' => $file->file->name,
                        'tmp_name' => $file_path_copy
                    ]));

                    $project_file_resource->create(Entity::make([
                        'project_id' => $project_id,
                        'name' => $file->file->name,
                        'file_id' => $file_id,
                    ]))->run();
                }
            } catch (Exception $e) {
                throw new Exception('Unable to find and copy lead files', 0, $e);
            }
        }

        // send created notification
        if ($request->get('sendIntroEmail') === '1') {
            CreatedNotificationJob::enqueue($customer_id);
        }

        return $project_id;
    }

    /**
     * Add customer, property, and project
     *
     * @param RequestInterface $request
     * @return \Core\Components\Http\Responses\JSONResponse
     */
    public function add(RequestInterface $request)
    {
        $user = Auth::user();

        if (!$user->primary && !$user->projectManagement && !$user->sales) {
            return Response::json(['error' => 'Access denied'], 403);
        }

        try {
            $project_id = null;
            DB::transaction(function () use ($user, $request, &$project_id) {
                $project_id = $this->handleAdd($user, $request);
            });

            return Response::json(['projectID' => $project_id, 'result' => 'true']);
        } catch (Exception $e) {
            Log::create('customer_add')->error('Unable to add customer', [
                'exception' => $e
            ]);
            return Response::json(['error' => 'Unable to add customer'], 500);
        }
    }

    /**
     * Handle adding customer from an existing property
     *
     * This is a port of the customer-add-new-project.php legacy ajax page, modified with some improvements. Still needs work,
     * but is the first phase to fully using the api.
     *
     * @param User $user
     * @param RequestInterface $request
     * @return \Core\Components\Resource\Requests\CreateRequest
     */
    protected function handleAddFromProperty(User $user, RequestInterface $request)
    {
        $acl = Auth::acl();

        // collect and validate data
        // handle customer data
        $customer = Entity::make([
            'business_name' => $request->get('businessName'),
            'first_name' => $request->get('firstName'),
            'last_name' => $request->get('lastName'),
            'email' => $request->get('email'),
            'address' => $request->get('ownerAddress'),
            'address_2' => $request->get('ownerAddress2'),
            'city' => $request->get('ownerCity'),
            'state' => $request->get('ownerState'),
            'zip' => $request->get('ownerZip'),
            'is_unsubscribed' => !!$request->get('unsubscribed', 0),
        ]);
        if ((int) $request->get('noEmailRequired', 0) === 1) {
            $customer->email = null;
        }

        // handle customer phone data
        $phone_data = json_decode($request->get('userPhoneTable', ''), true);
        $phones = [];
        if (is_array($phone_data)) {
            $type_map = [
                'Cell' => PhoneResource::TYPE_CELL,
                'CellNoText' => PhoneResource::TYPE_CELL_TEXT_OPT_OUT,
                'Home' => PhoneResource::TYPE_HOME,
                'Work' => PhoneResource::TYPE_WORK,
                'Other' => PhoneResource::TYPE_OTHER
            ];
            foreach ($phone_data as $phone) {
                $type = $desc = $phone['phoneDescription'];
                if (!isset($type_map[$type])) {
                    $type = 'Other';
                }
                if ($type === 'CellNoText') {
                    $desc = 'Cell';
                }
                $phones[] = [
                    'type' => $type_map[$type],
                    'number' => $phone['phoneNumber'],
                    'description' => $desc,
                    'is_primary' => array_key_exists('isPrimary', $phone) ? (int) $phone['isPrimary'] === 1 : false
                ];
            }
        }
        $customer->phones = $phones;

        $property_id = (int) $request->get('propertyIDMove', 0);

        // if customer address is empty, then pull from property
        if (empty($customer->address) || empty($customer->city) || empty($customer->state) || empty($customer->zip)) {
            $property = PropertyResource::make($acl)->entity($property_id)->run();

            $customer->address = $property->address;
            $customer->address_2 = $property->address_2;
            $customer->city = $property->city;
            $customer->state = $property->state;
            $customer->zip = $property->zip;
        }

        // handle project data (if needed)
        $project = null;
        if (($description = $request->get('projectDescription', '')) !== '') {
            $project = Entity::make([
                'property_id' => $property_id,
                'description' => $description,
                'salesperson_user_id' => Func::emptyToNull($request->get('projectSalesperson')),
                'referral_marketing_type_id' => Func::emptyToNull($request->get('referralDropdown'))
            ]);
            if (($alt_description = $request->get('projectDescriptionArray', '')) !== '') {
                $project->description = str_replace(',', ', ', $alt_description);
            }
            // if a project salesperson is defined, then we cast it's value to an integer since form data is always a string
            if ($project->salesperson_user_id !== null) {
                $project->salesperson_user_id = (int) $project->salesperson_user_id;
            }
            if ($project->referral_marketing_type_id !== null) {
                $project->referral_marketing_type_id = (int) $project->referral_marketing_type_id;
            }

            // handle project note
            $project_note = null;
            if (($note_text = trim($request->get('projectNote', ''))) !== '') {
                $project_note = Entity::make([
                    'note' => $note_text,
                    'tag' => 'PC',
                    'is_pinned' => $request->get('projectNotePin') === '1'
                ]);
            }
        }

        // create customer
        $customer_id = CustomerResource::make($acl)->create($customer)->run();

        // create project
        if ($project !== null) {
            $project->customer_id = $customer_id;
            $project_id = ProjectResource::make($acl)->create($project)->run();

            // create project note if defined
            if ($project_note !== null) {
                $project_note->project_id = $project_id;
                NoteResource::make($acl)->create($project_note)->run();
            }
        }

        return $customer_id;
    }

    /**
     * Add customer from existing property
     *
     * @param RequestInterface $request
     * @return \Core\Components\Http\Responses\JSONResponse
     */
    public function addFromProperty(RequestInterface $request)
    {
        $user = Auth::user();

        if (!$user->primary && !$user->projectManagement && !$user->sales) {
            return Response::json(['error' => 'Access denied'], 403);
        }

        try {
            $customer_id = null;
            DB::transaction(function () use ($user, $request, &$customer_id) {
                $customer_id = $this->handleAddFromProperty($user, $request);
            });

            return Response::json(['customerID' => $customer_id]);
        } catch (Exception $e) {
            Log::create('customer_add_from_property')->error('Unable to add customer', [
                'exception' => $e
            ]);
            return Response::json(['error' => 'Unable to add customer'], 500);
        }
    }

    /**
     * Update individual customer information
     *
     * @param RequestInterface $request
     * @return \Core\Components\Http\Responses\JSONResponse
     */
    public function update(RequestInterface $request)
    {
        $user = Auth::user();

        if (!$user->primary && !$user->projectManagement && !$user->sales) {
            return Response::json(['error' => 'Access denied'], 403);
        }

        // collect and validate data
        // handle customer data
        $customer = Entity::make([
            'id' => (int) $request->get('customerID', 0),
            'business_name' => $request->get('businessName'),
            'first_name' => $request->get('firstName'),
            'last_name' => $request->get('lastName'),
            'email' => $request->get('email'),
            'address' => $request->get('address'),
            'address_2' => $request->get('address2'),
            'city' => $request->get('city'),
            'state' => $request->get('state'),
            'zip' => $request->get('zip'),
            'is_unsubscribed' => $request->get('unsubscribed') === '1'
        ]);
        if ((int) $request->get('noEmailRequired', 0) === 1) {
            $customer->email = null;
        }

        // handle customer phone data
        $phone_data = $request->get('phoneArray', []);
        $phones = [];
        if (is_array($phone_data)) {
            $type_map = [
                'Cell' => PhoneResource::TYPE_CELL,
                'CellNoText' => PhoneResource::TYPE_CELL_TEXT_OPT_OUT,
                'Home' => PhoneResource::TYPE_HOME,
                'Work' => PhoneResource::TYPE_WORK,
                'Other' => PhoneResource::TYPE_OTHER
            ];
            foreach ($phone_data as $phone) {
                // skip phones which are marked deleted since the newer system will automatically remove missing
                // entries
                if (Arr::get($phone, 'phoneDelete') === 'delete') {
                    continue;
                }
                $type = $desc = Arr::get($phone, 'phoneDescription');
                if (!isset($type_map[$type])) {
                    $type = 'Other';
                }
                if ($type === 'CellNoText') {
                    $desc = 'Cell';
                }
                $_phone = [
                    'type' => $type_map[$type],
                    'number' => Arr::get($phone, 'phoneNumber'),
                    'description' => $desc,
                    'is_primary' => array_key_exists('isPrimary', $phone) ? (int) $phone['isPrimary'] === 1 : false
                ];
                if (($id = Arr::get($phone, 'customerPhoneID', '')) !== '') {
                    $_phone['id'] = $id;
                }
                $phones[] = $_phone;
            }
        }
        $customer->phones = $phones;

        try {
            $acl = Auth::acl();

            CustomerResource::make($acl)->partialUpdate($customer)->run();

            EventResource::make($acl)->pushToCalendarByCustomerID($customer->id);

            return Response::json(['success' => true]);
        } catch (Exception $e) {
            Log::create('customer_update')->error('Unable to update customer', [
                'exception' => $e
            ]);
            return Response::json(['error' => 'Unable to update customer'], 500);
        }
    }
}
