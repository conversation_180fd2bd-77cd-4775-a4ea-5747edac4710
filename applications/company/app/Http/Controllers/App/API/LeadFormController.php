<?php

namespace App\Http\Controllers\App\API;

use App\Classes\Acl;
use App\Classes\Log;
use App\Exceptions\Api\UnprocessableEntityException;
use App\Resources\CompanyResource;
use App\Resources\LeadFormResource;
use App\Resources\LeadResource;
use App\Services\CompanyFeatureService;
use App\Services\GoogleRecaptchaService;
use App\Services\Lead\LeadFormService;
use App\Traits\Resource\Controller\ActionTrait;
use Common\Models\Feature;
use Common\Models\Interfaces\CompanyInterface;
use Common\Models\LeadForm;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\Exceptions\HttpResponseException;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\Responses\JSONResponse;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Exceptions\ValidationException;
use Core\Components\Resource\Http\Requests\ResourceRequest;
use Core\Exceptions\AppException;
use Core\StaticAccessors\Config;
use Exception;
use Monolog\Logger;

class LeadFormController
{
    use ActionTrait {
        retrieve as actionRetrieve;
    }

    protected $resource = LeadFormResource::class;

    protected ?Logger $logger = null;

    public function __construct()
    {
        $this->registerFormat('collection-v1', 'application/vnd.adg.fx.collection-v1+json');
        $this->registerFormat('form-v1', 'application/vnd.adg.fx.form-v1+json');
    }

    /**
     * Get logger
     *
     * @return Logger
     */
    protected function getLog(): Logger
    {
        if ($this->logger === null) {
            $this->logger = Log::create('lead-form', [
                'file' => 'lead-form.log',
            ]);
        }
        return $this->logger;
    }

    /**
     * Retrieve the current active lead form for the company
     *
     * @param ResourceRequest $request
     * @return JSONResponse|null
     */
    public function retrieveCurrent(ResourceRequest $request)
    {
        $this->authorizeFeatureAccess();
        $user = Auth::user();

        $lead_form = LeadForm::where('companyID', $user->companyID)->first();
        if (!$lead_form) {
            return Response::json([], 200);
        }

        $entity = $this->actionRetrieve($lead_form->leadFormID, $request);
        $info = LeadFormService::make()->getCompanyMarketingInfo($user->companyID);

        $current = array_merge($entity->getContent(), [
            'marketing_types' => $info['marketing_types'] ?? [],
            'project_types' => $info['project_types'] ?? [],
        ]);


        return Response::json($current, 200);
    }

    /**
     * Get particular marketing information about the lead form
     *
     * @return JSONResponse
     */
    public function getCompanyMarketingInfo(): JSONResponse
    {
        try {
            $this->authorizeFeatureAccess();
            $user = Auth::user();

            $info = LeadFormService::make()->getCompanyMarketingInfo($user->companyID);
            return Response::json($info);

        } catch (Exception $e) {
            Log::create('project_info')->error('Unable to get company lead form complementary info', [
                'exception' => $e
            ]);
            return Response::json(['error' => 'Unable to get company lead form info'], 500);
        }
    }

    /**
     * Activate the lead form
     *
     * @return JSONResponse
     */
    public function activateForm(ResourceRequest $request): JSONResponse
    {
        try {
            $this->authorizeFeatureAccess();
            $entity = LeadFormService::make()->activate();

            $lead_form = $this->actionRetrieve($entity, $request);
            return Response::json($lead_form->getContent(), 200);
        } catch (\Exception $e) {
            $this->getLog()->error($e->getMessage());
            return Response::json(['error' => 'An error occurred while activating the form.'], 500);
        }
    }

    /**
     * Render the lead form
     *
     * @param $id
     * @return mixed
     * @throws HttpResponseException
     */
    public function render($id)
    {
        $lead_form = LeadForm::where('token', $id)
            ->where('isActive', true)
            ->with('fields')
            ->first();

        if (!$lead_form) {
            throw new HttpResponseException(404);
        }

        $info = LeadFormService::make()->getCompanyMarketingInfo($lead_form->companyID);
        $lead_website_form_captcha_key = Config::get('website_leads_form.recaptcha_key');

        $acl = Acl::make()->setCompanyID($lead_form->companyID);
        $company = CompanyResource::make($acl)->findOrFail($lead_form->companyID);
        $colors = $company->getButtonColors();

        return Response::view('pages.website-leads-form', [
            'lead_form' => $lead_form->toRenderArray(),
            'marketing_types' => $info['marketing_types'],
            'project_types' => $info['project_types'],
            'captcha_key' => $lead_website_form_captcha_key,
            'company_colors' => $colors,
        ])->share('include_layout', false)
          ->share('layout-header', false);
    }

    /**
     * Ingest a lead form
     *
     * @param RequestInterface $request
     * @return JSONResponse
     */
    public function ingest(RequestInterface $request): JSONResponse
    {
        try {
            $input = $request->input()->all();
            if ($request->data('is_api_request')) {
                $lead_form = $request->data('lead_form_by_api');
                $input['origin'] = LeadResource::ORIGIN_API_INTEGRATION;
            }
            else {
                $lead_form = $this->ingestValidation($input);
                $input['origin'] = LeadResource::ORIGIN_WEBSITE_LEADS_FORM;
            }

            $this->authorizeLeadFormFeature($lead_form->companyID);

            $lead_id = LeadFormService::make()->createLead($lead_form, $input);

            return Response::json(['lead_id' => $lead_id], 201);
        } catch (UnprocessableEntityException|ValidationException $e) {
            return $this->logAndRespondWithErrors($e);
        } catch (\Exception $e) {
            $this->getLog()->error($e->getMessage());
            return Response::json(['error' => 'An error occurred while ingesting lead data.'], 500);
        }
    }

    /**
     * Ingest a lead form
     *
     * @param $input
     * @param $captcha_check
     * @return LeadForm
     * @throws ValidationException
     */
    private function ingestValidation($input, $captcha_check = true) : LeadForm
    {
        if ($captcha_check) {
            if (!GoogleRecaptchaService::verifyToken($input['recaptcha_token'])) {
                throw new ValidationException('reCAPTCHA verification failed.');
            }
        }

        if (empty($input['token'])) {
            throw new ValidationException('Token is required.');
        }

        $lead_form = LeadForm::where('token', $input['token'])
            ->where('isActive', true)
            ->first();

        if (!$lead_form) {
            throw new ValidationException('Invalid token or inactive form.');
        }
        return $lead_form;
    }

    /**
     * Ingest lead form files
     *
     * @param RequestInterface $request
     * @return JSONResponse
     */
    public function ingestFiles(RequestInterface $request): JSONResponse
    {
        try {
            $input = $request->input()->all();
            if ($request->data('is_api_request')) {
                $lead_form = $request->data('lead_form_by_api');
            }
            else {
                $lead_form = $this->ingestValidation($input, false);
            }
            $this->authorizeLeadFormFeature($lead_form->companyID);

            $lead_file_id = LeadFormService::make()->createLeadFile($lead_form, $input, $request);

            return Response::json(['lead_file_id' => $lead_file_id], 201);
        } catch (UnprocessableEntityException|ValidationException $e) {
            return $this->logAndRespondWithErrors($e);
        } catch (\Exception $e) {
            $this->getLog()->error($e->getMessage());
            return Response::json(['error' => 'An error occurred while ingesting lead file data.'], 500);
        }
    }

    /**
     * @throws Exception
     */
    public function generateApiKey(): JSONResponse
    {
        $user = Auth::user();
        $data = LeadFormResource::make(Acl::make($user))->generateApiKey();

        return Response::json($data, 201);
    }

    public function deleteApiKey(): JSONResponse
    {
        $user = Auth::user();
        $lead_form = LeadForm::where('companyID', $user->companyID)->first();

        if (!$lead_form) {
            return Response::json(['error' => 'Lead form not found.'], 404);
        }

        // TOOD - We could improve this later on to save all api keys that a user created
        // for a lead integration and it's respective status.

        $lead_form->apiKey = null;
        $lead_form->apiKeyCreatedAt = null;
        $lead_form->save();

        return Response::json(['message' => 'API key deleted successfully.'], 200);
    }

    /**
     * Authorize feature access
     *
     * @return JSONResponse|void
     * @throws HttpResponseException| AppException
     */
    private function authorizeFeatureAccess(): void
    {
        $user = Auth::user();
        if (!$user) {
            throw new HttpResponseException(404, 'User not found.');
        }

        $allowed_statuses = [CompanyInterface::STATUS_TRIAL, CompanyInterface::STATUS_ACTIVE];
        if (!in_array($user->company->status, $allowed_statuses, true)) {
            throw new HttpResponseException(403, 'Company is not active.');
        }

        $this->authorizeLeadFormFeature($user->companyID);
    }

    /**
     * Check if the lead form feature is enabled for a company.
     *
     * @throws AppException
     * @throws HttpResponseException
     */
    private function authorizeLeadFormFeature($companyID): void
    {
        $company_feature_service = CompanyFeatureService::getByCompanyID($companyID);
        $is_feature_enabled = $company_feature_service->has(Feature::WEBSITE_LEADS_FORM, true);

        if (!$is_feature_enabled) {
            throw new HttpResponseException(403, 'Feature not enabled.');
        }
    }

    private function logAndRespondWithErrors($e): JSONResponse
    {
        $this->getLog()->error($e->getMessage(), $e instanceof ValidationException ? $e->getErrors() : []);
        $errors = $e instanceof UnprocessableEntityException ? $e->getResponse()->getContent() : $e->getErrors();
        return Response::json(['message' => $e->getMessage(), 'errors' => $errors], 400);
    }
}
