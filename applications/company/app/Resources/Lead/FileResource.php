<?php

declare(strict_types=1);

namespace App\Resources\Lead;

use App\Interfaces\ResourceCompanyMediaInterface;
use App\ResourceDelegates\Lead\FileDelegate;
use App\Traits\Resource\{BulkActionTrait, CompanyMediaTrait, UserActionTrackingTrait};
use Common\Models\LeadFile;
use Core\Components\Resource\Classes\Resource;

/**
 * Class FileResource
 *
 * @package App\Resources
 */
class FileResource extends Resource implements ResourceCompanyMediaInterface
{
    use BulkActionTrait;
    use CompanyMediaTrait;
    use UserActionTrackingTrait;

    /**
     * @var int Actions allowed on resource
     */
    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_SEARCH | self::ACTION_GROUP_BATCH);

    /**
     * @var string Table name
     */
    protected $table = 'leadFiles';

    /**
     * @var string Model class name
     */
    protected $model = LeadFile::class;

    /**
     * @var bool Determines if UUID is generated automatically
     */
    protected $generate_id = true;

    /**
     * @var bool Determines if an authenticated user is required for resource
     */
    protected $allow_no_user = true;

    /**
     * Boot and assign delegates to resource
     */
    protected static function boot(): void
    {
        static::delegate(FileDelegate::class);
    }
}
