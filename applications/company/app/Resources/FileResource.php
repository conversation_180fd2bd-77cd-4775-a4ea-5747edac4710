<?php

declare(strict_types=1);

namespace App\Resources;

use App\Interfaces\Resource\FileHandlerResourceInterface;
use App\ResourceDelegates\FileDelegate;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\File;
use Core\Components\Resource\Classes\MediaType;
use Core\Components\Resource\Classes\MediaTypeVersion;
use Core\Components\Resource\Classes\Resource;
use Core\Exceptions\AppException;

class FileResource extends Resource implements FileHandlerResourceInterface
{
    use UserActionTrackingTrait;

    public const TYPE_DRAWING = 1;
    public const TYPE_REPAIR_PLAN = 2;
    public const TYPE_USER_IMAGE = 3;
    public const TYPE_COMPANY_LOGO = 4;
    public const TYPE_FORM_UPLOAD = 5;
    public const TYPE_BID = 6;
    public const TYPE_SCOPE_OF_WORK = 7;
    public const TYPE_BID_CUSTOM_DRAWING = 8;
    public const TYPE_MEDIA = 9;
    public const TYPE_INVOICE_STATEMENT = 10;
    public const TYPE_PROPERTY_IMAGE = 11;
    public const TYPE_PROJECT_FILE = 12;
    public const TYPE_CUSTOM_REPORT_RESULT = 13;
    public const TYPE_LEAD_FILE = 14;

    public const STATUS_IN_PROGRESS = 1;
    public const STATUS_FINISHED = 2;
    public const STATUS_FAILED = 3;

    public const SLUG_ORIGINAL = 'original';

    protected static array $type_resource_media_map = [
        self::TYPE_DRAWING => [DrawingResource::class, 'image'],
        self::TYPE_REPAIR_PLAN => [DrawingResource::class, 'repair_plan'],
        self::TYPE_USER_IMAGE => [UserResource::class, 'image'],
        self::TYPE_COMPANY_LOGO => [CompanyResource::class, 'logo'],
        self::TYPE_FORM_UPLOAD => [Form\Item\Entry\Group\FieldFileResource::class, 'file'],
        self::TYPE_BID => [Bid\ItemResource::class, 'file'],
        self::TYPE_SCOPE_OF_WORK => [Bid\ItemResource::class, 'scope_of_work'],
        self::TYPE_BID_CUSTOM_DRAWING => [Bid\Item\CustomDrawingResource::class, 'file'],
        self::TYPE_MEDIA => [MediaResource::class, 'file'],
        self::TYPE_INVOICE_STATEMENT => [Company\InvoiceResource::class, 'statement_file'],
        self::TYPE_PROPERTY_IMAGE => [PropertyResource::class, 'image'],
        self::TYPE_PROJECT_FILE => [Project\FileResource::class, 'file'],
        self::TYPE_CUSTOM_REPORT_RESULT => [Company\CustomReport\ResultResource::class, 'file'],
        self::TYPE_LEAD_FILE => [Lead\FileResource::class, 'file']
    ];

    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~self::ACTION_GROUP_NESTED;

    protected $table = 'files';
    protected $model = File::class;

    protected $generate_id = true;
    protected $allow_no_user = true;

    public static function getTypes(): array
    {
        return [
            static::TYPE_DRAWING, static::TYPE_REPAIR_PLAN, static::TYPE_USER_IMAGE, static::TYPE_COMPANY_LOGO,
            static::TYPE_FORM_UPLOAD, static::TYPE_BID, static::TYPE_SCOPE_OF_WORK, static::TYPE_BID_CUSTOM_DRAWING,
            static::TYPE_MEDIA, static::TYPE_INVOICE_STATEMENT, static::TYPE_PROPERTY_IMAGE, static::TYPE_PROJECT_FILE,
            static::TYPE_CUSTOM_REPORT_RESULT, static::TYPE_LEAD_FILE
        ];
    }

    public static function getStatuses(): array
    {
        return [
            static::STATUS_IN_PROGRESS, static::STATUS_FINISHED, static::STATUS_FAILED
        ];
    }

    protected static function boot(): void
    {
        static::delegate(FileDelegate::class);
    }

    public function getMediaTypeByType(int $type): MediaType
    {
        [$class, $media_name] = self::$type_resource_media_map[$type] ?? throw new AppException('Unable to find resource media config for type: %d', $type);
        $resource = $class::make($this->acl());
        return $resource->getMedia()->get($media_name);
    }

    public function getMediaTypeVersionByType(int $type): MediaTypeVersion
    {
        return $this->getMediaTypeByType($type)->getOriginal();
    }
}
