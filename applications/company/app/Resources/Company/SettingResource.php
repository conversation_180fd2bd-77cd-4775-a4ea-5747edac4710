<?php

namespace App\Resources\Company;

use App\ResourceDelegates\Company\SettingDelegate;
use App\Resources\Bid\Item\OneTimePaymentTermResource;
use App\Traits\Resource\UserActionTrackingTrait;
use Common\Models\CompanySetting;
use Core\Components\Resource\Classes\Resource;

class SettingResource extends Resource
{
    const TYPE_STRING = 1;
    const TYPE_INT = 2;
    const TYPE_FLOAT = 3;
    const TYPE_BOOL = 4;
    const TYPE_ARRAY = 5;

    use UserActionTrackingTrait;

    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL & ~(self::ACTION_SEARCH);

    protected $table = 'companySettings';
    protected $model = CompanySetting::class;

    protected $allow_no_user = true;

    protected static function boot()
    {
        static::delegate(SettingDelegate::class);
    }

    public static function getSettingConfig()
    {
        return [
            'additional_email_recipients' => [
                'type' => static::TYPE_ARRAY,
                'rules' => [
                    'check_email_list' => true
                ]
            ],
            'leads_additional_email_recipients' => [
                'type' => static::TYPE_ARRAY,
                'rules' => [
                    'check_email_list' => true
                ]
            ],
            'payment_term_installments' => [
                'type' => static::TYPE_ARRAY,
                'rules' => [
                    'check_installment' => true
                ]
            ],
            'payment_term_one_time_due_time_frame' => [
                'type' => static::TYPE_INT,
                'rules' => [
                    'in_array' => OneTimePaymentTermResource::getDueTimeFrames()
                ]
            ],
            'appointment_reminder_24_hours' => [
                'type' => static::TYPE_BOOL
            ],
            'bid_file_structure' => [
                'type' => static::TYPE_ARRAY,
                'rules' => [
                    'nullable' => true,
                    'optional' => true,
                    'check_bid_file_structure' => true
                ]
            ],
            'text_messaging_enabled' => [
                'type' => static::TYPE_BOOL
            ],
            'text_message_delivery_failure_email_recipients' => [
                'type' => static::TYPE_ARRAY,
                'rules' => [
                    'check_email_list' => true
                ]
            ],
            'marketing_source_required' => [
                'type' => static::TYPE_BOOL
            ],
            'project_sales_collaboration' => [
                'type' => static::TYPE_BOOL
            ],
            'allow_calendar_sharing' => [
                'type' => static::TYPE_BOOL
            ],
            'invoice_auto_send' => [
                'type' => static::TYPE_BOOL
            ],
            'quickbooks_default_service' => [
                'type' => static::TYPE_INT
            ],
            'quickbooks_use_invoice' => [
                'type' => static::TYPE_BOOL
            ],
            'quickbooks_allow_online_credit_card_payment' => [
                'type' => static::TYPE_BOOL
            ],
            'quickbooks_allow_online_ach_payment' => [
                'type' => static::TYPE_BOOL
            ],
            'email_subject_line_warranty' => [
                'type' => static::TYPE_STRING
            ],
            'bid_follow_up_notifications' => [
                'type' => static::TYPE_BOOL
            ],
            'bid_follow_up_notifications_config' => [
                'type' => static::TYPE_ARRAY,
                'rules' => [
                    'check_bid_follow_ups' => true
                ]
            ],
            'email_footer_template' => [
                'type' => static::TYPE_STRING
            ],
            'wisetack_financing_required_for_all_projects' => [
                'type' => static::TYPE_BOOL
            ],
            'wisetack_project_financing_enabled_by_default' => [
                'type' => static::TYPE_BOOL
            ],
            'wisetack_current_pricing_plan' => [
                'type' => static::TYPE_INT
            ],
            'project_install_collaboration' => [
                'type' => static::TYPE_BOOL
            ],
            'appointment_reminder_scheduling_window' => [
                'type' => static::TYPE_INT
            ],
            'show_unit_with_product_selection' => [
                'type' => static::TYPE_BOOL
            ],
            'project_costing_for_primary_only' => [
                'type' => static::TYPE_BOOL
            ],
            'result_required' => [
                'type' => static::TYPE_BOOL
            ],
            'project_type_required' => [
                'type' => static::TYPE_BOOL
            ],
            'business_hours_config' => [
                'type' => static::TYPE_ARRAY,
                'rules' => [
                    'check_business_hours' => true
                ]
            ],
            'default_project_summary' => [
                'type' => static::TYPE_STRING
            ],
            'include_company_calendar' => [
                'type' => static::TYPE_BOOL
            ],
            'email_greeting' => [
                'type' => static::TYPE_STRING
            ],
            'drawing_watermark' => [
                'type' => static::TYPE_BOOL
            ],
            'bid_footer_content' => [
                'type' => static::TYPE_STRING
            ],
            'text_message_evaluation_template' => [
                'type' => static::TYPE_STRING
            ],
            'text_message_installation_template' => [
                'type' => static::TYPE_STRING
            ],
            'text_message_evaluation_reminder_template' => [
                'type' => static::TYPE_STRING
            ],
            'text_message_installation_reminder_template' => [
                'type' => static::TYPE_STRING
            ],
        ];
    }

    public static function isValidName($name)
    {
        $config = self::getSettingConfig();
        return isset($config[$name]);
    }

    public static function getSettingValidationRules($name, $value)
    {
        $type_map = [
            static::TYPE_STRING => 'string',
            static::TYPE_INT => 'integer',
            static::TYPE_FLOAT => 'float',
            static::TYPE_BOOL => 'boolean',
            static::TYPE_ARRAY => 'array'
        ];

        $config = self::getSettingConfig();
        $setting = $config[$name];
        $rules = ['type' => $type_map[$setting['type']]];
        if (isset($setting['rules'])) {
            $rules = array_merge($rules, $setting['rules']);
        }
        return $rules;
    }

    public static function getQueryValue($name, $value)
    {
        if ($value === null) {
            return $value;
        }
        $config = self::getSettingConfig();
        switch ($config[$name]['type']) {
            case static::TYPE_INT:
            case static::TYPE_FLOAT:
                $value = (string) $value;
                break;
            case static::TYPE_BOOL:
                $value = $value === true ? '1' : '0';
                break;
            case static::TYPE_ARRAY:
                $value = json_encode($value);
                break;
        }
        return $value;
    }

    public static function getOutputValue($name, $value)
    {
        if ($value === null) {
            return $value;
        }
        $config = self::getSettingConfig();
        // depending on value type, convert data
        switch ($config[$name]['type']) {
            case static::TYPE_STRING:
                $value = (string) $value;
                break;
            case static::TYPE_INT:
                $value = (int) $value;
                break;
            case static::TYPE_FLOAT:
                $value = (float) $value;
                break;
            case static::TYPE_BOOL:
                $value = $value === '1';
                break;
            case static::TYPE_ARRAY:
                $value = json_decode($value, true);
                if (!is_array($value)) {
                    $value = [];
                }
                break;
        }
        return $value;
    }

    public function isNameInUse($company_id, $name)
    {
        return $this->newQuery()->where('companyID', $company_id)->where('name', $name)->count() !== 0;
    }
}
