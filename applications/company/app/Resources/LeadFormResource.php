<?php
declare(strict_types=1);
namespace App\Resources;

use App\Classes\Acl;
use App\ResourceDelegates\LeadFormDelegate;
use Common\Models\LeadForm;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Resource;
use Carbon\Carbon;
use Ramsey\Uuid\Uuid;

/**
 * Class LeadFormResource
 *
 * @package App\Resources
 */
class LeadFormResource extends Resource
{
    const LABEL_DEFAULT_FORM_TITLE = 'Contact Us';
    const LABEL_DEFAULT_SAVE_BUTTON = 'Submit';


    protected $table = 'leadForms';
    protected $model = LeadForm::class;
    protected $available_actions = self::ACTION_GROUP_ENABLE_ALL;
    protected $allow_no_user = false;

    /**
     * Boot and assign delegates to resource
     */
    protected static function boot(): void
    {
        static::delegate(LeadFormDelegate::class);
    }

    /**
     * Generate API key for a lead form
     *
     * @throws \Exception
     */
    public function generateApiKey(): array
    {
        $user = $this->acl()->user();
        if (!$user) {
            throw new \Exception('User not authenticated');
        }

        $lead_form = LeadForm::where('companyID', $user->companyID)->first();

        if (!$lead_form) {
            throw new \Exception('Lead form not found or access denied');
        }

        $key = UUID::uuid4();
        $created_at = Carbon::now('UTC')->toDateTimeString();

        $lead_form->apiKey = $key->getBytes();
        $lead_form->apiKeyCreatedAt = $created_at;
        $lead_form->save();

        return [
            'api_key' => $key->toString(),
            'api_key_created_at' => $created_at
        ];
    }

}
