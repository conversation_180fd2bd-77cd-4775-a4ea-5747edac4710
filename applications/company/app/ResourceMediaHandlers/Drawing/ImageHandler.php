<?php

declare(strict_types=1);

namespace App\ResourceMediaHandlers\Drawing;

use App\ResourceMediaHandlers\BaseCompanyFileHandler;
use Core\Classes\File;
use Core\Components\Http\Classes\Http;
use Core\Components\Http\Responses\FileResponse;
use Core\Components\Resource\Classes\BatchRequest;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Exceptions\ActionNotAllowedException;
use Core\Components\Resource\Exceptions\ValidationException;
use App\Resources\FileResource;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Requests\CreateRequest;
use Core\Components\Resource\Requests\PolyCreateRequest;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Components\Validation\Classes\Validation;

/**
 * Class ImageHandler
 *
 * @package App\ResourceMediaHandlers\Drawing
 */
class ImageHandler extends BaseCompanyFileHandler
{
    /**
     * Handle media field data during create request
     *
     * Create file entity by adding a request to the batch of the poly create and assign id to drawing before it's saved.
     *
     * @param Entity $entity
     * @param PolyCreateRequest $request
     */
    public function create(Entity $entity, PolyCreateRequest $request): void
    {
        $this->resource->setMediaCompanyID($this->resource->acl()->user()->companyID);

        $file_request = $this->getFileCreateRequest(
            FileResource::TYPE_DRAWING,
            $entity->get('name'),
            $entity->get('tmp_name')
        );
        $file_request->attach('handle.after', function () use ($file_request, $request) {
            $request->getRequest()->store('image_file_id', $file_request->response());
        });
        $request->getBatchRequest()->add($file_request);
    }

    /**
     * Handle media field data during update request
     *
     * If no image id is available, then we create file entity and assign the id to the drawing. Otherwise, we update
     * the existing file entity and flag the repair plan as invalid to make sure it gets regenerated.
     *
     * @param Entity $entity
     * @param UpdateRequest $request
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     * @throws \Core\Exceptions\AppException
     */
    public function update(Entity $entity, UpdateRequest $request): void
    {
        if (!$request->storage('allow_image_modify', false)) {
            throw new ActionNotAllowedException('Image cannot be modified');
        }

        $model = $request->getModel();

        $this->resource->setMediaCompanyID($this->resource->acl()->user()->companyID);

        $this->resource->setAccessLevel(Field::ACCESS_LEVEL_PRIVATE);

        $image_file_id = $this->resource->getFields()->get('image_file_id')->outputValueFromModel($model);
        if ($image_file_id === null) {
            $batch = BatchRequest::make()->sequential();

            $file_request = $this->getFileCreateRequest(FileResource::TYPE_DRAWING, $entity->get('name'), $entity->get('tmp_name'));
            $batch->add($file_request);

            $drawing_request = $this->resource->partialUpdate(Entity::make([
                'id' => $this->resource->getPrimaryField()->outputValueFromModel($model),
                'is_repair_plan_valid' => false
            ]))
                ->attach('batch.last_request', function (CreateRequest $file_request, UpdateRequest $drawing_request) {
                    $entity = $drawing_request->getEntity();
                    $entity->set('image_file_id', $file_request->response());
                })
                ->findConfig(['check_mutability' => false])
                ->force();
            $batch->add($drawing_request);

            $batch->run();
        } else {
            $this->getFileUpdateRequest($image_file_id, $entity->get('name'), $entity->get('tmp_name'))->run();
            $this->resource->partialUpdate(Entity::make([
                'id' => $this->resource->getPrimaryField()->outputValueFromModel($model),
                'is_repair_plan_valid' => false
            ]))
                ->findConfig(['check_mutability' => false])
                ->force()
                ->run();
        }

        $this->resource->restoreAccessLevel();
    }

    /**
     * Save image file for drawing
     *
     * @deprecated Replaced by media field abilities
     *
     * @param string $id Uuid
     * @param Entity $entity
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     * @throws \Core\Components\Validation\Exceptions\ValidationException
     * @throws \Core\Exceptions\AppException
     */
    public function save(string $id, Entity $entity): void
    {
        $drawing = $this->resource->findOrFail($id);
        $this->resource->setMediaCompanyID($this->resource->acl()->user()->companyID);

        $validator = Validation::make()
            ->config([
                'file' => [
                    'label' => 'File',
                    'rules' => [
                        'upload' => [
                            'required' => true,
                            'mimes' => ['jpeg'],
                            'max_size' => '5MB'
                        ]
                    ]
                ]
            ])
            ->run($entity->toArray());
        if (!$validator->valid()) {
            throw (new ValidationException('Input is not valid'))->setValidator($validator);
        }

        $this->resource->setMediaCompanyID($this->resource->acl()->user()->companyID);

        $image_resource = $this->resource->relationResource('image');

        $time = $this->getElapsedTime();

        if ($drawing->imageFileID === null) {
            $batch = BatchRequest::make()->sequential();

            $file_request = $image_resource->create(Entity::make([
                'type' => FileResource::TYPE_DRAWING,
                'status' => FileResource::STATUS_FINISHED,
                'name' => $validator->data('file.name'),
                'path' => $validator->data('file.tmp_name'),
                'output_directory' => $this->version->getPath(),
                'time' => $time
            ]));
            $batch->add($file_request);

            $drawing_request = $this->resource->setAccessLevel(Field::ACCESS_LEVEL_PRIVATE)->update(Entity::make([
                'id' => $this->resource->getPrimaryField()->outputValueFromModel($drawing),
                'is_repair_plan_valid' => false
            ]))
                ->partial()
                ->attach('batch.last_request', function (CreateRequest $file_request, UpdateRequest $drawing_request) {
                    $entity = $drawing_request->getEntity();
                    $entity->set('image_file_id', $file_request->response());
                });
            $batch->add($drawing_request);

            $batch->run();
        } else {
            $image_resource->update(Entity::make([
                'id' => $this->resource->getFields()->get('image_file_id')->outputValueFromModel($drawing),
                'name' => $validator->data('file.name'),
                'path' => $validator->data('file.tmp_name'),
                'output_directory' => $this->version->getPath(),
                'time' => $time
            ]))
                ->partial()
                ->run();
            $this->resource->setAccessLevel(Field::ACCESS_LEVEL_PRIVATE)->update(Entity::make([
                'id' => $this->resource->getPrimaryField()->outputValueFromModel($drawing),
                'is_repair_plan_valid' => false
            ]))
                ->partial()
                ->run();
        }
    }

    /**
     * Get response for media controller
     *
     * @param mixed $id
     * @param array $config
     * @return FileResponse
     * @throws \Core\Components\Resource\Exceptions\MediaNotFoundException
     */
    public function getResponse(mixed $id, array $config = []): FileResponse
    {
        $drawing = $this->resource->findOrFail($id);

        $path = $this->getPathFromFileField('image_file_id', $drawing);
        $response = Response::file($path);
        if (isset($config['download'])) {
            $extension = Http::getExtensionByMimeType(Http::getMimeType($path));
            $response->download('drawing_image_' . File::sanitizeName($drawing->name, $extension));
        }
        return $response;
    }
}
