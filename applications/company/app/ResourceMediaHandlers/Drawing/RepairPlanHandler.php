<?php

declare(strict_types=1);

namespace App\ResourceMediaHandlers\Drawing;

use App\Classes\Func;
use App\Classes\Pdf;
use App\ResourceMediaHandlers\BaseCompanyFileHandler;
use App\Resources\CompanyResource;
use App\Services\CompanyDrawingNodeTypeService;
use App\Services\CompanySettingService;
use App\Services\TimeService;
use Common\Models\Drawing;
use Core\Components\Http\Responses\FileResponse;
use Core\Components\Http\Responses\ViewResponse;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Exceptions\MediaException;
use App\Resources\Drawing\NodeResource;
use App\Resources\FileResource;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Http\StaticAccessors\View;
use Exception;

/**
 * Class RepairPlanHandler
 *
 * @package App\ResourceMediaHandlers\Drawing
 */
class RepairPlanHandler extends BaseCompanyFileHandler
{
    /**
     * Get drawing model from id if model not already passed
     *
     * @param string|Drawing $drawing
     * @return Drawing
     */
    public function getModel(string|Drawing $drawing): Drawing
    {
        if (!($drawing instanceof Drawing)) {
            $drawing = $this->resource->findOrFail($drawing);
        }
        return $drawing;
    }

    /**
     * Generate repair plan PDF from drawing id or model
     *
     * @param string|Drawing $drawing
     * @param bool $force
     * @return string
     * @throws MediaException
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     * @throws \Core\Exceptions\AppException
     */
    public function generate(string|Drawing $drawing, bool $force = false): string
    {
        $drawing = $this->getModel($drawing);
        $version = $drawing->version;

        if (!$this->resource->hasMediaCompanyID()) {
            $company_id = Drawing::query()->withCreatedByUser()->whereKey($drawing->getKey())->value('companyID');
            $this->resource->setMediaCompanyID($company_id);
        }

        $company_id = $this->resource->getMediaCompanyID();
        $setting_service = new CompanySettingService($company_id);
        $has_drawing_watermark = $setting_service->get('drawing_watermark', false);

        $this->resource->setVersion($version);

        if ($drawing->imageFileID === null) {
            throw new MediaException('Image file id is missing');
        }

        if ($force || $drawing->repairPlanFileID === null || !$drawing->isRepairPlanValid) {
            $this->setStartTime();

            /** @var \App\Resources\UserResource $created_by_user */
            $created_by_user_resource = $this->resource->relationResource('created_by_user');
            $created_by_user_scope = Scope::make()
                ->fields(['first_name', 'last_name', 'phone_number', 'email'])
                ->with([
                    'company' => [
                        'fields' => [
                            'name', 'address', 'address_2', 'city', 'state', 'zip', 'website', 'logo_file_id',
                            'primary_phone_number'
                        ]
                    ]
                ]);
            $created_by_user = $created_by_user_resource->entity($drawing->createdByUserID)
                ->scope($created_by_user_scope)
                ->run();

            if ($drawing->projectID !== null) {
                $project_scope = Scope::make()->field('id')->with([
                    'property' => [
                        'fields' => ['address', 'address_2', 'city', 'state', 'zip'],
                        'with' => [
                            'customer' => [
                                'fields' => ['business_name', 'first_name', 'last_name', 'email', 'primary_phone_number'],
                            ]
                        ]
                    ]
                ]);
                $project = $this->resource->relationResource('project')->entity($drawing->projectID)
                    ->scope($project_scope)
                    ->run();
            }

            try {
                $temp_file = Func::createTempFile(null, false);
                $snappy = new Pdf();
                $snappy->setOptions([
                    'page-size' => 'Letter',
                    'margin-top' => '0',
                    'margin-right' => '0',
                    'margin-bottom' => '0',
                    'margin-left' => '0',
                    'disable-smart-shrinking' => true,
                    'orientation' => 'Landscape'
                ]);

                $logo = null;
                $watermark_logo = null;
                if (($logo_file_id = $created_by_user->get('company.logo_file_id')) !== null) {
                    $company_resource = CompanyResource::make($this->resource->acl());
                    try {
                        $logo = $company_resource->getMediaHandler('logo', 'document_thumbnail')->getPath($this->resource->getMediaCompanyID(), $logo_file_id);
                    } catch (\Throwable $e) {}

                    try {
                        $watermark_logo = $company_resource->getMediaHandler('logo', 'drawing_thumbnail')->getPath($this->resource->getMediaCompanyID(), $logo_file_id);
                    } catch (\Throwable $e) {
                        // Override the setting to prevent template errors
                        $has_drawing_watermark = false;
                    }
                }

                $drawing_node_types = (new CompanyDrawingNodeTypeService($company_id, $version))->all();

                // get nodes
                /** @var NodeResource $node_resource */
                $node_resource = $this->resource->relationResource('nodes');
                $_node_types = $node_resource->getTypesByDrawingID($this->resource->getPrimaryField()->outputValueFromModel($drawing));
                $node_types = [];
                foreach ($drawing_node_types as $type => $data) {
                    if (!in_array($type, $_node_types)) {
                        continue;
                    }
                    $node_types[] = $data;
                }

                $image_file_id = $this->resource->getFields()->get('image_file_id')->outputValueFromModel($drawing);
                $image = $this->resource->getMedia()->get('image')->getOriginal()->getPath($image_file_id);
                $version = $drawing->version;
                if ($version === 2) {
                    $image = file_get_contents($image);
                }

                $timezone = TimeService::getTimezoneForCompany($company_id);
                $vars = [
                    'version' => $version,
                    'node_types' => $node_types,
                    'image' => $image,
                    'name' => $drawing->name,
                    'company' => $created_by_user['company'],
                    'logo' => $logo,
                    'has_drawing_watermark' => $has_drawing_watermark,
                    'watermark_logo' => $watermark_logo,
                    'has_project' => $drawing->projectID !== null,
                    'created_by' => $created_by_user,
                    'finalized_at' => $drawing->finalizedAt !== null ? $drawing->finalizedAt->timezone($timezone)->format('n/j/Y') : null
                ];
                if ($vars['has_project']) {
                    $vars['customer'] = $project['property']['customer'];
                    $vars['property'] = $project['property'];
                }
                $html = View::fetch('pdfs.drawing.repair-plan', $vars)->render();

                $snappy->generateFromHtml($html, $temp_file);
            } catch (Exception $e) {
                if (isset($temp_file) && file_exists($temp_file)) {
                    unlink($temp_file);
                }
                throw $e;
            }

            $drawing_payload = [
                'id' => $this->resource->getPrimaryField()->outputValueFromModel($drawing),
                'is_repair_plan_valid' => $drawing->status === Drawing::STATUS_FINALIZED
            ];
            $repair_plan_file_id = $this->resource->getFields()->get('repair_plan_file_id')->outputValueFromModel($drawing);
            if ($repair_plan_file_id === null) {
                $repair_plan_file_id = $this->getFileCreateRequest(
                    FileResource::TYPE_REPAIR_PLAN, 'repair_plan.pdf', $temp_file
                )->run();
                $drawing_payload['repair_plan_file_id'] = $repair_plan_file_id;
            } else {
                $this->getFileUpdateRequest($repair_plan_file_id, 'repair_plan.pdf', $temp_file)->run();
            }
            $this->resource->setAccessLevel(Field::ACCESS_LEVEL_PRIVATE)->update(Entity::make($drawing_payload))
                ->partial()
                ->findConfig([
                    'check_mutability' => false
                ])
                ->force()
                ->run();
        } else {
            $repair_plan_file_id = $this->resource->getfields()->get('repair_plan_file_id')->outputValueFromModel($drawing);
        }
        return $this->getPathFromFileID($repair_plan_file_id);
    }

    /**
     * Get response for media controller
     *
     * @param mixed $id
     * @param array $config
     * @return ViewResponse|FileResponse
     * @throws MediaException
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     * @throws \Core\Exceptions\AppException
     */
    public function getResponse(mixed $id, array $config = []): ViewResponse|FileResponse
    {
        $drawing = $this->getModel($id);

        if (isset($config['viewer'])) {
            return Response::view('pages/pdf-viewer', [
                'url' => $this->getUrlFromModel($drawing)
            ]);
        }

        $path = $this->generate($drawing);

        return Response::file($path)->contentType('application/pdf')->filename('repair_plan.pdf');
    }

    /**
     * Get path to repair plan PDF from id or model
     *
     * @param string|Drawing $drawing
     * @return string
     * @throws MediaException
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     * @throws \Core\Exceptions\AppException
     */
    public function getPath(string|Drawing $drawing): string
    {
        return $this->generate($drawing);
    }
}
