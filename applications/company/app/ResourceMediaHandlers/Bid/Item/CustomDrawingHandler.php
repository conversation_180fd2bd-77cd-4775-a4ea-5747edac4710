<?php

declare(strict_types=1);

namespace App\ResourceMediaHandlers\Bid\Item;

use App\ResourceMediaHandlers\BaseCompanyFileHandler;
use App\Resources\FileResource;
use App\Traits\ResourceMedia\ImageTrait;
use Common\Models\BidItemCustomDrawing;
use Core\Components\Http\{Classes\Http, Responses\FileResponse, Responses\ViewResponse, StaticAccessors\Response};
use Core\Components\Resource\{Classes\Entity, Requests\PolyCreateRequest};
use Ramsey\Uuid\UuidInterface;

/**
 * Class CustomDrawingHandler
 *
 * @package App\ResourceMediaHandlers\Bid\Item
 */
class CustomDrawingHandler extends BaseCompanyFileHandler
{
    use ImageTrait;

    /**
     * Store uploaded file and create record in database
     *
     * @param Entity $entity
     * @param PolyCreateRequest $request
     */
    public function create(Entity $entity, PolyCreateRequest $request): void
    {
        $company_id = $this->resource->acl()->user()->companyID;

        $this->resource->setMediaCompanyID($company_id);

        $path = $entity->get('tmp_name');

        // image upload file is an image, convert to PDF since this will be merged into another pdf
        $mime_type = Http::getMimeType($path);
        if (in_array($mime_type, ['image/png', 'image/jpeg', 'image/gif'])) {
            $path = $this->convertImageToPdf($path);
        }

        $file_request = $this->getFileCreateRequest(
            FileResource::TYPE_BID_CUSTOM_DRAWING, $entity->get('name'), $path
        );
        $file_request->attach('handle.after', function () use ($file_request, $request) {
            // add file id to main entity of parent request
            $request->getMainEntity()->set('file_id', $file_request->response());
        });
        $request->getBatchRequest()->add($file_request);
    }

    /**
     * Get bid item custom drawing model from id if model not already passed
     *
     * @param string|BidItemCustomDrawing $drawing
     * @return BidItemCustomDrawing
     */
    public function getModel(string|BidItemCustomDrawing $drawing): BidItemCustomDrawing
    {
        if (!($drawing instanceof BidItemCustomDrawing)) {
            $drawing = $this->resource->findOrFail($drawing);
        }
        return $drawing;
    }

    /**
     * Set media company ID on resource using bid item custom drawing model
     *
     * If media company id is not defined, the company ID will be queried based on bid item custom drawing id. It is
     * not efficient, but is necessary to properly get paths to files without an authenticated user.
     *
     * @param BidItemCustomDrawing $drawing
     */
    protected function setMediaCompanyID(BidItemCustomDrawing $drawing): void
    {
        $company_id = BidItemCustomDrawing::query()->withCustomer()->whereKey($drawing->getKey())->value('companyID');
        $this->resource->setMediaCompanyID($company_id);
    }

    /**
     * Get path for drawing using id or model
     *
     * If file id is passed, it will be used in the path. Otherwise, file id will be pulled from the model directly.
     *
     * @param string|BidItemCustomDrawing $drawing
     * @param string|UuidInterface|null $file_id
     * @return string
     */
    public function getPath(string|BidItemCustomDrawing $drawing, string|UuidInterface $file_id = null): string
    {
        if ($file_id === null) {
            $drawing = $this->getModel($drawing);
            $file_id = $this->resource->getFields()->get('file_id')->outputValueFromModel($drawing);
        }
        if (!$this->resource->hasMediaCompanyID()) {
            $drawing = $this->getModel($drawing);
            $this->setMediaCompanyID($this->getModel($drawing));
        }

        return $this->getPathFromFileID((string) $file_id);
    }

    /**
     * Get response to access file
     *
     * If 'viewer' config item is passed, the PDF will be wrapped in our pdf-viewer module
     *
     * @param mixed $id
     * @param array $config
     * @return ViewResponse|FileResponse
     */
    public function getResponse(mixed $id, array $config = []): ViewResponse|FileResponse
    {
        $user = $this->resource->acl()->user();
        if ($user !== null) {
            $this->resource->setMediaCompanyID($user->companyID);
        }

        $drawing = $this->getModel($id);

        if (!$this->resource->hasMediaCompanyID()) {
            $this->setMediaCompanyID($drawing);
        }

        if (isset($config['viewer'])) {
            return Response::view('pages/pdf-viewer', [
                'url' => $this->getUrlFromModel($drawing)->build()
            ]);
        }

        return Response::file($this->getPath($drawing));
    }
}
