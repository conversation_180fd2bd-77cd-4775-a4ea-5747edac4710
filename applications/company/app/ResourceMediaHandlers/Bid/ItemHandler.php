<?php

declare(strict_types=1);

namespace App\ResourceMediaHandlers\Bid;

use App\Classes\Acl;
use App\NotificationJobs\User\BidViewNotificationJob;
use App\ResourceMediaHandlers\BaseCompanyFileHandler;
use Core\Components\Http\Responses\FileResponse;
use Core\Components\Http\Responses\ViewResponse;
use App\FileBuilders\Bid\{FileBuilder, LegacyFileBuilder};
use App\Resources\FileResource;
use Carbon\Carbon;
use Common\Models\BidItem;
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Resource\Classes\{BatchRequest, Entity, Field};
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Exceptions\MediaGeneratingException;
use Core\Exceptions\AppException;
use mikehaertl\pdftk\Pdf as PdftkPdf;
use Throwable;

/**
 * Class ItemHandler
 *
 * Handles generation and storage of bid PDF
 *
 * @package App\ResourceMediaHandlers\Bid
 */
class ItemHandler extends BaseCompanyFileHandler
{
    /**
     * Get model from id if model not already passed
     *
     * @param string|BidItem $bid
     * @return BidItem
     */
    public function getModel(string|BidItem $bid): BidItem
    {
        if (!($bid instanceof BidItem)) {
            $bid = $this->resource->findOrFail($bid);
        }
        return $bid;
    }

    /**
     * Get company id from bid model
     *
     * Needed to scope to company when working with this handler via system.
     *
     * @param BidItem $bid
     * @return int
     */
    protected function getCompanyID(BidItem $bid): int
    {
        return BidItem::query()->withCustomer()->whereKey($bid->getKey())->value('companyID');
    }

    /**
     * Determines if file should be generated
     *
     * @param BidItem $bid
     * @param bool $force
     * @return bool
     */
    protected function shouldGenerate(BidItem $bid, bool $force): bool
    {
        return $force || $bid->fileID === null || !$bid->isFileValid;
    }

    /**
     * Generate bid PDF
     *
     * @param string|BidItem $bid
     * @param bool $force
     * @return string
     * @throws AppException
     * @throws MediaGeneratingException
     * @throws Throwable
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     */
    public function generate(string|BidItem $bid, bool $force = false): string
    {
        $bid = $this->getModel($bid);

        /** @var Acl $acl */
        $acl = $this->resource->acl();
        if (!$acl->hasCompanyID()) {
            $acl->setCompanyID($this->getCompanyID($bid));
        }

        if ($bid->isFileGenerating) {
            throw new MediaGeneratingException('Bid PDF is being generated');
        }

        if ($this->shouldGenerate($bid, $force)) {
            $this->setStartTime();

            DB::transaction(function () use (&$bid, $force) {
                /** @var BidItem $bid */
                $bid = BidItem::query()->whereKey($bid->getKey())->lockForUpdate()->first();
                if (!$this->shouldGenerate($bid, $force)) {
                    throw new AppException('Cannot generate bid after obtaining lock');
                }
                if ($bid->isFileGenerating) {
                    throw new MediaGeneratingException('Bid PDF is being generated after obtaining lock');
                }
                BidItem::disableHistory(function () use ($bid) {
                    $bid->isFileGenerating = true;
                    $bid->save();
                });
            });

            try {
                $temp_file = match ($bid->type) {
                    BidItem::TYPE_GUIDED => (new FileBuilder($bid))->build(),
                    BidItem::TYPE_LEGACY => (new LegacyFileBuilder($bid))->build(),
                    default => throw new AppException('Unsupported bid type')
                };

                $batch = BatchRequest::make()->sequential();

                $file_id = $this->resource->getFields()->get('file_id')->outputValueFromModel($bid);
                if ($file_id === null) {
                    $file_request = $this->getFileCreateRequest(FileResource::TYPE_BID, 'bid.pdf', $temp_file);
                } else {
                    $file_request = $this->getFileUpdateRequest($file_id, 'bid.pdf', $temp_file);
                }
                $batch->add($file_request);

                $bid_entity = Entity::make([
                    'id' => $this->resource->getPrimaryField()->outputValueFromModel($bid),
                    'is_file_valid' => !in_array($bid->status, [BidItem::STATUS_INCOMPLETE, BidItem::STATUS_SUBMITTED]),
                    'is_file_generating' => false
                ]);
                $bid_request = $this->resource->setAccessLevel(Field::ACCESS_LEVEL_PRIVATE)->partialUpdate($bid_entity)
                    ->findConfig([
                        'check_mutability' => false
                    ])
                    ->force(true)
                    ->attach('batch.last_request', function () use ($bid, $bid_entity, $file_request) {
                        if ($bid->fileID !== null) {
                            return;
                        }
                        $bid_entity->set('file_id', $file_request->response());
                    });

                $batch->add($bid_request);
                $batch->run();

                $file_id = $file_request->response();
            } catch (Throwable $e) {
                // if unable to make PDF file or save updates, then we disable the generating flag so things are not stuck and action
                // can be retried
                BidItem::disableHistory(function () use ($bid) {
                    $bid->isFileGenerating = false;
                    $bid->save();
                });
                throw $e;
            }
        } else {
            $file_id = $this->resource->getfields()->get('file_id')->outputValueFromModel($bid);
        }
        return $this->getPathFromFileID($file_id);
    }

    /**
     * Finalize bid by filling in form data using XFDF system
     *
     * @param string|BidItem $bid
     * @param array $data
     * @throws AppException
     * @throws MediaGeneratingException
     * @throws Throwable
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     * @throws \Core\Components\Resource\Exceptions\ResourceException
     */
    public function finalize(string|BidItem $bid, array $data): void
    {
        $bid = $this->getModel($bid);

        /** @var Acl $acl */
        $acl = $this->resource->acl();
        if (!$acl->hasCompanyID()) {
            $acl->setCompanyID($this->getCompanyID($bid));
        }

        $this->setStartTime();

        $pdf = new PdftkPdf($this->getPath($bid));
        $pdf->fillForm($data);
        $pdf->flatten();
        if (!$pdf->execute()) {
            throw new AppException('Unable to fill form and flatten PDF - Reason: %s', $pdf->getError());
        }
        $temp_file = $pdf->getTmpFile();
        // turn off automatic clean up of temp file since it will be moved to it's final location by the
        // file resource
        $temp_file->delete = false;
        $temp_file = $temp_file->getFileName();

        // refresh model since the ->getPath method might of updated the bid record
        $bid = $bid->fresh();

        $file_id = $this->resource->getFields()->get('file_id')->outputValueFromModel($bid);
        $this->getFileUpdateRequest($file_id, 'accepted_bid.pdf', $temp_file)->run();
    }

    /**
     * Get path to bid item PDF
     *
     * @param string|BidItem $bid
     * @return string
     * @throws MediaGeneratingException
     * @throws Throwable
     */
    public function getPath(string|BidItem $bid): string
    {
        return $this->generate($bid);
    }

    /**
     * Get response for media controller
     *
     * @param mixed $id
     * @param array $config
     * @return ViewResponse|FileResponse
     * @throws AppException
     * @throws MediaGeneratingException
     * @throws Throwable
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     */
    public function getResponse(mixed $id, array $config = []): ViewResponse|FileResponse
    {
        $bid = $this->getModel($id);

        /** @var Acl $acl */
        $acl = $this->resource->acl();

        if (isset($config['viewer'])) {
            $id = $this->resource->getPrimaryField()->outputValueFromModel($bid);
            return Response::view('pages/pdf-viewer', [
                'url' => $this->version->getType()->getOriginal()->getUrl($id)->build(),
                'loaded_callback' => $config['loaded_callback'] ?? ''
            ]);
        }

        if ($acl->user() === null) {
            $table = $bid->type === BidItem::TYPE_LEGACY ? 'evaluationBid' : 'customBid';
            $evaluation = DB::table($table)
                ->join('evaluation', 'evaluation.evaluationID', '=', "{$table}.evaluationID")
                ->join('project', 'project.projectID', '=', "evaluation.projectID")
                ->where('evaluation.bidItemID', $bid->getKey())
                ->first(
                    ['evaluation.evaluationID', "{$table}.bidLastViewed", 'evaluation.evaluationFinalizedByID', 'project.projectSalesperson']
                );

            if ($evaluation->bidLastViewed === null) {
                BidViewNotificationJob::enqueue((int) $evaluation->evaluationID);
            }

            DB::table($table)->where('evaluationID', $evaluation->evaluationID)->update([
                'bidLastViewed' => Carbon::now('UTC')
            ]);
        }

        $path = $this->generate($bid, isset($config['regenerate']));
        $name = 'bid' . ($bid->referenceID !== null ? "_{$bid->referenceID}" : '') . '.pdf';
        return Response::file($path)->contentType('application/pdf')->filename($name);
    }
}
