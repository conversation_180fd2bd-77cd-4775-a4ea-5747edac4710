<?php

declare(strict_types=1);

namespace App\ResourceMediaHandlers\Bid;

use App\Classes\{Acl, Func, Pdf};
use App\ResourceMediaHandlers\BaseCompanyFileHandler;
use App\ResourceMediaHandlers\Bid\Item\CustomDrawingHandler;
use App\ResourceMediaHandlers\Drawing\RepairPlanHandler;
use App\Resources\{
    ContentPartialResource,
    ContentTemplateResource,
    DrawingResource,
    FileResource
};
use App\Resources\Bid\Item\{
    ContentResource,
    CustomDrawingResource,
    InstallmentPaymentTerm\InstallmentResource,
    LineItemResource,
    OneTimePaymentTermResource,
    PaymentTermResource
};
use App\Resources\Form\Item\Entry\Group\FieldFileResource;
use App\Resources\Form\Item\Group\LayoutResource;
use App\Services\{ContentTemplateService, TimeService};
use App\Services\Form\Classes\Entry;
use App\Services\Form\Types\Company\BidType;
use Brick\Math\{BigDecimal, RoundingMode};
use Carbon\Carbon;
use Common\Models\BidItem;
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Http\{Responses\FileResponse, StaticAccessors\Response, StaticAccessors\View};
use Core\Components\Resource\Classes\{
    BatchRequest,
    Entity,
    Field,
    Scope
};
use Core\Components\Resource\Exceptions\{MediaNotFoundException, MediaGeneratingException};
use Core\Exceptions\AppException;
use Core\StaticAccessors\App;
use mikehaertl\pdftk\Pdf as PdftkPdf;
use Throwable;

/**
 * Class ScopeOfWorkHandler
 *
 * @package App\ResourceMediaHandlers\Bid
 */
class ScopeOfWorkHandler extends BaseCompanyFileHandler
{
    /**
     * Get model from id if model not already passed
     *
     * @param string|BidItem $bid
     * @return BidItem
     */
    public function getModel(string|BidItem $bid): BidItem
    {
        if (!($bid instanceof BidItem)) {
            $bid = $this->resource->findOrFail($bid);
        }
        return $bid;
    }

    /**
     * Set company id from ACL or model
     *
     * @param Acl $acl
     * @param BidItem $bid
     */
    protected function setCompanyID(Acl $acl, BidItem $bid): void
    {
        if ($acl->hasCompanyID()) {
            return;
        }
        $company_id = BidItem::query()->withCustomer()->whereKey($bid->getKey())->value('companyID');
        $acl->setCompanyID($company_id);
    }

    /**
     * Get context needed to generate PDF
     *
     * @param Acl $acl
     * @param BidItem $bid
     * @param array $available_templates
     * @return array
     * @throws AppException
     * @throws \App\Exceptions\TimeException
     * @throws \App\Services\Form\Exceptions\FormException
     * @throws \App\Services\Form\Exceptions\ImportException
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     */
    protected function getContext(Acl $acl, BidItem $bid, array $available_templates): array
    {
        $context = [];

        $project_resource = $this->resource->relationResource('project');
        $project_scope = Scope::make()
            ->fields(['description', 'reference_id', 'marketing_source'])
            ->with([
                'contacts' => [
                    'fields' => ['name', 'phone_number', 'email']
                ],
                'salesperson_user' => [
                    'fields' => ['first_name', 'last_name', 'email'],
                    'with' => [
                        'phones' => [
                            'fields' => ['number', 'description', 'is_primary']
                        ]
                    ]
                ],
                'property' => [
                    'fields' => [
                        'id', 'address', 'address_2', 'city', 'state', 'zip', 'county', 'township', 'latitude',
                        'longitude', 'image_file_id'
                    ],
                    'with' => [
                        'customer' => [
                            'fields' => [
                                'id', 'business_name', 'first_name', 'last_name', 'email', 'address', 'address_2', 'city',
                                'state', 'zip'
                            ],
                            'with' => [
                                'company' => [
                                    'fields' => [
                                        'id', 'name', 'address', 'address_2', 'city', 'state', 'zip', 'website',
                                        'logo_file_id', 'latitude', 'longitude'
                                    ],
                                    'with' => [
                                        'phones' => [
                                            'fields' => ['number', 'description', 'is_primary']
                                        ]
                                    ]
                                ],
                                'phones' => [
                                    'fields' => ['number', 'description', 'is_primary']
                                ]
                            ]
                        ]
                    ]
                ]
            ]);
        $data = $project_resource->entity($bid->projectID)->scope($project_scope)->run()->toArray();

        // handle customer info
        $customer = $data['property']['customer'];
        $customer['primary_phone'] = null;
        if (count($customer['phones']) > 0) {
            $phones = array_filter($customer['phones'], function ($phone) {
                return $phone['is_primary'];
            });
            if (count($phones) > 0) {
                $customer['primary_phone'] = array_shift($phones);
            }
        }
        $company = $customer['company'];
        unset($customer['company']);
        $context['customer'] = $customer;
        unset($customer);

        // handle property info
        $property = $data['property'];
        $property['full_address'] = $property['address'] . (isset($property['address_2']) ? ' ' . $property['address_2'] : '') . " {$property['city']}, {$property['state']} {$property['zip']}";

        $property_resource = $project_resource->relationResource('property');
        if ($property['image_file_id'] !== null) {
            $property_image = $property_resource->getMedia()->get('image');
            $property['image'] = [
                'original' => $property_image->getOriginal()->getUrl($property['id'])->port(null)->csm()->build(),
                'medium' => $property_image->getVariant('size_medium')->getUrl($property['id'])->port(null)->csm()->build()
            ];
        }
        $street_view_image = $property_resource->getMedia()->get('street_view_image');
        $property['street_view_image'] = [
            'original' => $street_view_image->getOriginal()->getUrl($property['id'])->port(null)->csm()->build()
        ];

        $context['property'] = $property;
        unset($property);

        $salesperson_user = $data['salesperson_user'];
        if ($salesperson_user !== null) {
            $salesperson_user['primary_phone'] = null;
            if (count($salesperson_user['phones']) > 0) {
                $phones = array_filter($salesperson_user['phones'], function ($phone) {
                    return $phone['is_primary'];
                });
                if (count($phones) > 0) {
                    $salesperson_user['primary_phone'] = array_shift($phones);
                }
            }
        }
        $context['salesperson_user'] = $salesperson_user;
        unset($salesperson_user);

        unset($data['property'], $data['salesperson_user']);

        // handle project
        // all the other data comes from the project data, so we have to pull all that out before saving the project info
        $context['project'] = $data;

        $context['company'] = $company;
        unset($company);

        $primary_field = $this->resource->getPrimaryField();

        // bid specific data
        $section_scope = Scope::make()
            ->fields(['id', 'name'])
            ->query(function ($query) {
                return $query->ordered();
            })
            ->filter('item_id', 'eq', $primary_field->outputValueFromModel($bid))
            ->with([
                'forms' => [
                    'fields' => ['company_form_item_id', 'form_item_entry_id'],
                    'query' => fn($query) => $query->ordered(),
                    'with' => [
                        'company_form_item' => [
                            'fields' => ['name']
                        ]
                    ]
                ]
            ]);
        $sections = $this->resource->relationResource('sections')->collection()
            ->scope($section_scope)
            ->run()
            ->toArray();

        $bid_info = [
            'reference_id' => $bid->referenceID,
            'total' => $bid->total,
            'sections' => $sections
        ];

        // handle sections data
//        if (isset($available_templates['sections'])) {
            $_sections = [];
            foreach ($sections as $section) {
                $_section = [
                    'name' => $section['name'],
                    'forms' => []
                ];
                foreach ($section['forms'] as $form) {
                    $bid_type = BidType::getByID($acl, $form['company_form_item_id']);
                    if ($bid_type->getIsHiddenFromJobDocument()) {
                        continue;
                    }
                    $entry = Entry::getByID($acl, $form['form_item_entry_id']);
                    $template = $bid_type->render($acl, LayoutResource::TYPE_OUTPUT_JOB_DOCUMENT, $entry);
                    if ($template['content'] === '') {
                        continue;
                    }
                    // @todo handle styles
                    $_section['forms'][] = [
                        'content' => $template['content']
                    ];
                }
                $_sections[] = $_section;
            }
            // filter out any sections with no forms
            $_sections = array_filter($_sections, function ($section) {
                return count($section['forms']) > 0;
            });
            $context['sections'] = $_sections;
            unset($_sections);
//        }

        // if materials list template is in use, grab line item data
//        if (isset($available_templates['materials_list'])) {
            // get all line items for bid
            $line_item_scope = Scope::make()
                ->fields(['type', 'name', 'quantity', 'amount', 'subtotal', 'total'])
                ->filter('type', 'in', [LineItemResource::TYPE_GENERAL, LineItemResource::TYPE_PRODUCT])
                ->filter('bid_item_id', 'eq', $primary_field->outputValueFromModel($bid))
                ->query(function ($query) {
                    return $query->ordered();
                })
                ->with([
                    'item' => [
                        'poly_scopes' => [
                            LineItemResource::TYPE_PRODUCT => [
                                'fields' => ['id', 'is_intangible', 'pricing_disclaimer'],
                                'with' => [
                                    'materials'
                                ]
                            ]
                        ]
                    ]
                ]);
            $line_items = $this->resource->relationResource('line_items')->collection()
                ->scope($line_item_scope)
                ->run()
                ->toArray();

            // group line items based on type
            $grouped_line_items = [
                'products' => [],
                'component_materials' => [],
                'other' => []
            ];
            $component_materials = [];
            foreach ($line_items as $line_item) {
                switch ($line_item['type']) {
                    case LineItemResource::TYPE_PRODUCT:
                        // since this list is used for a pick-list, it doesn't make sense to include intangible items
                        if ($line_item['item']['is_intangible']) {
                            $grouped_line_items['other'][] = $line_item;
                            break;
                        }
                        $grouped_line_items['products'][] = $line_item;

                        foreach ($line_item['item']['materials'] as $material) {
                            if (!isset($component_materials[$material['material_id']])) {
                                $component_materials[$material['material_id']] = [
                                    'name' => $material['name'],
                                    'quantity' => $material['total_quantity'],
                                    'is_material' => true
                                ];
                                continue;
                            }
                            $component_materials[$material['material_id']]['quantity'] = $component_materials[$material['material_id']]['quantity'] + $material['total_quantity'];
                        }
                        break;
                    default:
                        $grouped_line_items['other'][] = $line_item;
                        break;
                }
            }
            $grouped_line_items['component_materials'] = $component_materials;
            $bid_info['line_items'] = $grouped_line_items;
//        }

        // get images for each form
//        if (isset($available_templates['images'])) {
            $file_resource = FieldFileResource::make($acl);

            // set company id on resource so media handler gets proper path
            $file_resource->setMediaCompanyID($context['company']['id']);

            $file_handler = $file_resource->getMedia()->get('file')->getOriginal();
            $images = [];
            foreach ($sections as $section) {
                $image_group = [
                    'name' => $section['name'],
                    'forms' => []
                ];
                foreach ($section['forms'] as $form) {
                    $_form = [
                        'name' => $form['company_form_item']['name'],
                        'images' => []
                    ];
                    $scope = Scope::make()
                        ->fields(['id', 'file_id']);
                    $form_images = $file_resource
                        ->getAllImagesByEntryID($form['form_item_entry_id'], $scope)
                        ->toArray();
                    foreach ($form_images as $image) {
                        $_form['images'][] = [
                            'full' => $file_handler->getPath($image['file_id'])
                        ];
                    }
                    if (count($_form['images']) > 0) {
                        $image_group['forms'][] = $_form;
                    }
                }
                if (count($image_group['forms']) > 0) {
                    $images[] = $image_group;
                }
            }
            if (count($images) > 0) {
                $context['images'] = $images;
            }
            unset($images);
//        }

        /** @var TimeService $time_service */
        $time_service = App::get(TimeService::class);
        $bid_info['created_at'] = $time_service->get($bid->createdAt, true)->format('c');

        // if terms & conditions template is in use, grab bid content
//        if (isset($available_templates['terms_conditions'])) {
            $content_types = [
                ContentResource::TYPE_DISCLAIMER => 'disclaimers',
                ContentResource::TYPE_WAIVER => 'waivers',
                ContentResource::TYPE_WARRANTY => 'warranties',
                ContentResource::TYPE_CONTRACT => 'contracts'
            ];
            $content_scope = Scope::make()
                ->fields(['type', 'name', 'content'])
                ->filter('type', 'in', array_keys($content_types))
                ->filter('item_id', 'eq', $primary_field->outputValueFromModel($bid))
                ->query(function ($query) {
                    return $query->ordered();
                });
            $content = $this->resource->relationResource('content')->collection()
                ->scope($content_scope)
                ->run();

            $terms_conditions = [];
            if (count($content) > 0) {
                $content = $content->groupBy('type');
                $customer = $context['customer'];
                $property = $context['property'];
                $fx_tag_info = [
                    'date' => $time_service->get(Carbon::now('UTC'))->format('n/j/Y'),
                    'firstName' => $customer['first_name'],
                    'lastName' => $customer['last_name'],
                    'businessName' => $customer['business_name'],
                    'address' => $property['address'] . ($property['address_2'] !== null ? ' ' . $property['address_2'] : ''),
                    'address1' => $property['address'],
                    'address2' => $property['address_2'],
                    'city' => $property['city'],
                    'state' => $property['state'],
                    'zip' => $property['zip'],
                    'phone' => $customer['primary_phone']['number'],
                    'email' => $customer['email'],
                    'bidNumber' => $bid_info['reference_id'],
                    'description' => $context['project']['description']
                ];
                $fx_tags = array_map(function ($tag) {
                    return "{{$tag}}";
                }, array_keys($fx_tag_info));
                $fx_tag_values = array_values($fx_tag_info);
                foreach ($content_types as $type => $key) {
                    if (!isset($content[$type])) {
                        continue;
                    }
                    foreach ($content[$type] as $content_type) {
                        $content_type->content = str_replace($fx_tags, $fx_tag_values, $content_type->content);
                    }
                    $terms_conditions[$key] = $content[$type]->toArray();
                }
            }
            $bid_info['terms_conditions'] = $terms_conditions;
//        }

        $percentage_payment_terms = [];
        $payment_terms_scope = Scope::make()
            ->fields(['type', 'order'])
            ->filter('bid_item_id', 'eq', $primary_field->outputValueFromModel($bid))
            ->with([
                'item' => [
                    'poly_scopes' => [
                        PaymentTermResource::TYPE_ONE_TIME => [
                            'fields' => ['due_time_frame']
                        ],
                        PaymentTermResource::TYPE_INSTALLMENT => [
                            'no_fields' => true,
                            'with' => [
                                'installments' => [
                                    'fields' => ['id', 'name', 'due_time_frame', 'amount_type', 'amount', 'order'],
                                    'query' => function ($query) {
                                        return $query->ordered();
                                    }
                                ]
                            ]
                        ]
                    ]
                ]
            ]);
        $payment_terms = $this->resource->relationResource('payment_terms')->collection()
            ->scope($payment_terms_scope)
            ->run();
        if ($payment_terms->count() > 0) {
            $payment_term_type_map = [
                PaymentTermResource::TYPE_ONE_TIME => 'ONE_TIME',
                PaymentTermResource::TYPE_INSTALLMENT => 'INSTALLMENT'
            ];
            $one_time_due_time_frame_map = [
                OneTimePaymentTermResource::DUE_TIME_FRAME_AT_BID_ACCEPTANCE => 'AT_BID_ACCEPTANCE',
                OneTimePaymentTermResource::DUE_TIME_FRAME_AFTER_BID_ACCEPTANCE => 'AFTER_BID_ACCEPTANCE',
                OneTimePaymentTermResource::DUE_TIME_FRAME_BEFORE_PROJECT_START => 'BEFORE_PROJECT_START',
                OneTimePaymentTermResource::DUE_TIME_FRAME_AFTER_PROJECT_START => 'AFTER_PROJECT_START',
                OneTimePaymentTermResource::DUE_TIME_FRAME_BEFORE_PROJECT_COMPLETION => 'BEFORE_PROJECT_COMPLETION',
                OneTimePaymentTermResource::DUE_TIME_FRAME_AT_PROJECT_COMPLETION => 'AT_PROJECT_COMPLETION',
                OneTimePaymentTermResource::DUE_TIME_FRAME_AFTER_PROJECT_COMPLETION => 'AFTER_PROJECT_COMPLETION',
                OneTimePaymentTermResource::DUE_TIME_FRAME_AT_CLOSING => 'AT_CLOSING'
            ];
            $installment_due_time_frame_map = [
                InstallmentResource::DUE_TIME_FRAME_AT_BID_ACCEPTANCE => 'AT_BID_ACCEPTANCE',
                InstallmentResource::DUE_TIME_FRAME_AFTER_BID_ACCEPTANCE => 'AFTER_BID_ACCEPTANCE',
                InstallmentResource::DUE_TIME_FRAME_BEFORE_PROJECT_START => 'BEFORE_PROJECT_START',
                InstallmentResource::DUE_TIME_FRAME_AFTER_PROJECT_START => 'AFTER_PROJECT_START',
                InstallmentResource::DUE_TIME_FRAME_BEFORE_PROJECT_COMPLETION => 'BEFORE_PROJECT_COMPLETION',
                InstallmentResource::DUE_TIME_FRAME_AT_PROJECT_COMPLETION => 'AT_PROJECT_COMPLETION',
                InstallmentResource::DUE_TIME_FRAME_AFTER_PROJECT_COMPLETION => 'AFTER_PROJECT_COMPLETION',
                InstallmentResource::DUE_TIME_FRAME_AT_CLOSING => 'AT_CLOSING'
            ];
            $installment_amount_type_map = [
                InstallmentResource::AMOUNT_TYPE_TOTAL => 'TOTAL',
                InstallmentResource::AMOUNT_TYPE_PERCENTAGE => 'PERCENTAGE'
            ];
            $bid_total = $bid->total;
            foreach ($payment_terms as $payment_term) {
                switch ($payment_term->type) {
                    case PaymentTermResource::TYPE_ONE_TIME:
                        $payment_term->item->due_time_frame = $one_time_due_time_frame_map[$payment_term->get('item.due_time_frame')];
                        $payment_term->item->amount = $bid_total;
                        $percentage_payment_terms[] = [
                            'name' => 'One-Time Payment',
                            'due_time_frame' => $payment_term->get('item.due_time_frame'),
                            'amount' => $payment_term->item->amount,
                            'order' => 1
                        ];
                        break;
                    case PaymentTermResource::TYPE_INSTALLMENT:
                        $installments = $payment_term->get('item.installments', []);
                        // loop through all installments and get total of all non-percentage based installments
                        $total_sum = BigDecimal::of('0');
                        $last_percentage_installment = null;
                        foreach ($installments as $installment) {
                            if ($installment->amount_type === InstallmentResource::AMOUNT_TYPE_TOTAL) {
                                $total_sum = $total_sum->plus($installment->amount);
                            } elseif ($installment->amount_type === InstallmentResource::AMOUNT_TYPE_PERCENTAGE) {
                                $last_percentage_installment = $installment->id;
                            }
                        }
                        // get difference of non-percentage installments and bid total to get sum for percentage
                        // installment calculations
                        $percentage_base_total = BigDecimal::of($bid_total)
                            ->minus($total_sum);
                        if ($percentage_base_total->isNegativeOrZero()) {
                            $percentage_base_total = BigDecimal::of('0');
                        }
                        $percentage_total = BigDecimal::of('0');
                        foreach ($installments as $installment) {
                            $amount = BigDecimal::of($installment->amount);
                            // if amount type is percentage, then we need to multiply the amount by the base
                            // percentage total. if this is the last percentage, then we make any adjustments to the
                            // amount due to rounding errors
                            if ($installment->amount_type === InstallmentResource::AMOUNT_TYPE_PERCENTAGE) {
                                $amount = $percentage_base_total->multipliedBy($amount)->toScale(2, RoundingMode::HALF_DOWN);
                                $percentage_total = $percentage_total->plus($amount);
                                if ($installment->id === $last_percentage_installment) {
                                    $diff = $percentage_base_total->minus($percentage_total);
                                    if (!$diff->isZero()) {
                                        $amount = $amount->plus($diff);
                                    }
                                }
                            }
                            $installment->amount_type = $installment_amount_type_map[$installment->amount_type];
                            $installment->due_time_frame = $installment_due_time_frame_map[$installment->due_time_frame];
                            $installment->amount = (string) $amount->toScale(2, RoundingMode::HALF_DOWN);
                            $percentage_payment_terms[] = $installment->toArray();
                        }
                        break;
                }
                $payment_term->type = $payment_term_type_map[$payment_term->type];
            }
        }
        $bid_info['payment_terms'] = $percentage_payment_terms;
        $context['bid'] = $bid_info;

        return $context;
    }

    /**
     * Determines if file should be generated
     *
     * @param BidItem $bid
     * @param bool $force
     * @return bool
     */
    protected function shouldGenerate(BidItem $bid, bool $force): bool
    {
        return $force || $bid->scopeOfWorkFileID === null || !$bid->isScopeOfWorkFileValid;
    }

    /**
     * Create and store temporary PDF and return path
     *
     * @param Acl $acl
     * @param BidItem $bid
     * @return string
     * @throws AppException
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     */
    protected function makePdf(Acl $acl, BidItem $bid): string
    {
        $styles = '';
        $content = [];

        $content_template = ContentTemplateResource::make($acl);

        $templates = [
            ContentTemplateResource::TYPE_SCOPE_OF_WORK_PROJECT_INFO => 'project_info',
            ContentTemplateResource::TYPE_SCOPE_OF_WORK_MATERIALS_LIST => 'materials_list',
            ContentTemplateResource::TYPE_SCOPE_OF_WORK_SECTIONS => 'sections',
            ContentTemplateResource::TYPE_SCOPE_OF_WORK_IMAGES => 'images',
            ContentTemplateResource::TYPE_SCOPE_OF_WORK_TERMS_CONDITIONS => 'terms_conditions'
        ];
        $available_templates = [];
        foreach ($content_template->getDefaultsByType(array_keys($templates)) as $type => $template_id) {
            $available_templates[$templates[$type]] = $template_id;
        }

        $context = $this->getContext($acl, $bid, $available_templates);

        $content_partial = ContentPartialResource::make($acl);
        foreach ($available_templates as $name => $template_id) {
            if ($name === 'images' && !isset($context['images'])) {
                continue;
            }
            $data = ContentTemplateService::render($content_template, $content_partial, $template_id, $context);
            $styles .= implode("\n", $data['styles']);
            $content[$name] = $data['content'];
        }

        $temp_file = Func::createTempFile(null, false);

        $html = View::fetch('pdfs.scope_of_work', [
            'styles' => $styles,
            'content' => $content
        ])->render();

        $snappy = new Pdf();
        $snappy->setOptions([
            'page-size' => 'Letter',
            'margin-top' => '0.5in',
            'margin-right' => '0.75in',
            'margin-bottom' => '0.5in',
            'margin-left' => '0.75in',
            'disable-smart-shrinking' => true,
            'orientation' => 'Portrait',
            'title' => 'Job Document'
        ]);
        $snappy->generateFromHtml($html, $temp_file);

        $bid_item_id = $this->resource->getPrimaryField()->outputValueFromModel($bid);

        $drawing_files = [];

        // get repair plan pro drawings
        $drawing_scope = Scope::make()
            ->field('drawing_id')
            ->filter('item_id', 'eq', $bid_item_id);
        /** @var DrawingResource $drawing_resource */
        $drawing_resource = $this->resource->relationResource('drawings');
        $drawings = $drawing_resource->collection()
            ->scope($drawing_scope)
            ->run();
        if ($drawings->count() > 0) {
            /** @var RepairPlanHandler $drawing_handler */
            $drawing_handler = DrawingResource::make($acl)->getMediaHandler('repair_plan');
            foreach ($drawings as $drawing) {
                $drawing_files[] = $drawing_handler->getPath($drawing['drawing_id']);
            }
        }

        // get custom drawings
        $custom_drawing_scope = Scope::make()
            ->fields(['id', 'file_id'])
            ->filter('item_id', 'eq', $bid_item_id)
            ->with([
                'file' => [
                    'fields' => ['name', 'data']
                ]
            ])
            ->query(function ($query) {
                return $query->ordered();
            });
        /** @var CustomDrawingResource $custom_drawing_resource */
        $custom_drawing_resource = $this->resource->relationResource('custom_drawings');
        $drawings = $custom_drawing_resource->collection()
            ->scope($custom_drawing_scope)
            ->run();
        if ($drawings->count() > 0) {
            /** @var CustomDrawingHandler $custom_drawing_handler */
            $custom_drawing_handler = $custom_drawing_resource->getMediaHandler('file');
            foreach ($drawings as $drawing) {
                if ($drawing->get('file.data.protected', false)) {
                    continue;
                }
                $drawing_files[] = $custom_drawing_handler->getPath($drawing['id'], $drawing['file_id']);
            }
        }

        if (count($drawing_files) > 0) {
            $pdf = new PdftkPdf($temp_file);
            foreach ($drawing_files as $drawing_file) {
                $pdf->addFile($drawing_file);
            }
            if (!$pdf->execute()) {
                throw new AppException('Unable to merge PDFs with pdftk - Reason: %s', $pdf->getError());
            }
            // clean up old temp file
            if (!unlink($temp_file)) {
                throw new AppException('Unable to delete old temp file: %s', $temp_file);
            }
            $temp_file = $pdf->getTmpFile();
            // turn off automatic clean up of temp file since it will be moved to it's final location by the
            // file resource
            $temp_file->delete = false;
            $temp_file = $temp_file->getFileName();
        }

        return $temp_file;
    }

    /**
     * Generate PDF from model data
     *
     * @param string|BidItem $bid
     * @param bool $force
     * @return string
     * @throws AppException
     * @throws MediaGeneratingException
     * @throws MediaNotFoundException
     * @throws Throwable
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     */
    public function generate(string|BidItem $bid, bool $force = false): string
    {
        $bid = $this->getModel($bid);

        if ($bid->type === BidItem::TYPE_LEGACY) {
            throw new MediaNotFoundException('Job Document is not available for legacy bids');
        }

        /** @var Acl $acl */
        $acl = $this->resource->acl();

        $this->setCompanyID($acl, $bid);

        if ($bid->isScopeOfWorkFileGenerating) {
            throw new MediaGeneratingException('Job Document PDF is being generated');
        }

        if ($this->shouldGenerate($bid, $force)) {
            $this->setStartTime();

            DB::transaction(function () use (&$bid, $force) {
                $bid = BidItem::whereKey($bid->getKey())->lockForUpdate()->first();
                if (!$this->shouldGenerate($bid, $force)) {
                    throw new AppException('Cannot generate job document after obtaining lock');
                }
                if ($bid->isScopeOfWorkFileGenerating) {
                    throw new MediaGeneratingException('Job Document PDF is being generated after obtaining lock');
                }
                BidItem::disableHistory(function () use ($bid) {
                    $bid->isScopeOfWorkFileGenerating = true;
                    $bid->save();
                });
            });

            try {
                $temp_file = $this->makePdf($acl, $bid);

                /** @var FileResource $file_resource */
                $file_resource = $this->resource->relationResource('file');

                $time = $this->getElapsedTime();

                $batch = BatchRequest::make()->sequential();

                $file_id = $this->resource->getFields()->get('scope_of_work_file_id')->outputValueFromModel($bid);
                if ($file_id === null) {
                    $file_request = $this->getFileCreateRequest(
                        FileResource::TYPE_SCOPE_OF_WORK, 'job_document.pdf', $temp_file
                    );
                } else {
                    $file_request = $this->getFileUpdateRequest($file_id, 'job_document.pdf', $temp_file);
                }
                $batch->add($file_request);

                $bid_entity = Entity::make([
                    'id' => $this->resource->getPrimaryField()->outputValueFromModel($bid),
                    'is_scope_of_work_file_valid' => !in_array($bid->status, [BidItem::STATUS_INCOMPLETE, BidItem::STATUS_SUBMITTED]),
                    'is_scope_of_work_file_generating' => false
                ]);
                $bid_request = $this->resource->setAccessLevel(Field::ACCESS_LEVEL_PRIVATE)->partialUpdate($bid_entity)
                    ->findConfig([
                        'check_mutability' => false
                    ])
                    ->force(true)
                    ->attach('batch.last_request', function () use ($bid, $bid_entity, $file_request) {
                        if ($bid->scopeOfWorkFileID !== null) {
                            return;
                        }
                        $bid_entity->set('scope_of_work_file_id', $file_request->response());
                    });

                $batch->add($bid_request);
                $batch->run();

                $file_id = $file_request->response();
            } catch (Throwable $e) {
                BidItem::disableHistory(function () use ($bid) {
                    $bid->isScopeOfWorkFileGenerating = false;
                    $bid->save();
                });
                throw $e;
            }
        } else {
            $file_id = $this->resource->getfields()->get('scope_of_work_file_id')->outputValueFromModel($bid);
        }
        return $this->getPathFromFileID($file_id);
    }

    /**
     * Get path to file from bid item model
     *
     * @param string|BidItem $bid
     * @return string
     * @throws AppException
     * @throws MediaGeneratingException
     * @throws MediaNotFoundException
     * @throws Throwable
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     */
    public function getPath(string|BidItem $bid): string
    {
        if (!$this->resource->hasMediaCompanyID() && $this->resource->acl()->user() === null) {
            throw new AppException('User is required to get path');
        }
        return $this->generate($bid);
    }

    /**
     * Get response for media controller
     *
     * @param mixed $id
     * @param array $config
     * @return FileResponse
     * @throws AppException
     * @throws MediaGeneratingException
     * @throws MediaNotFoundException
     * @throws Throwable
     * @throws \Core\Components\Resource\Exceptions\InvalidUuidException
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     * @throws \Core\Components\Resource\Exceptions\RelationNotFoundException
     */
    public function getResponse(mixed $id, array $config = []): FileResponse
    {
        /** @var Acl $acl */
        $acl = $this->resource->acl();
        if (($user = $acl->user()) !== null) {
            $acl->setCompanyID($user->companyID);
        }

        $path = $this->generate($id, isset($config['regenerate']));
        return Response::file($path)->contentType('application/pdf')->filename('job_document.pdf');
    }
}
