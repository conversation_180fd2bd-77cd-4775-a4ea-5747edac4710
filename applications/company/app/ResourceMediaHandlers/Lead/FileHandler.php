<?php

declare(strict_types=1);

namespace App\ResourceMediaHandlers\Lead;

use App\ResourceMediaHandlers\BaseCompanyFileHandler;
use App\Resources\FileResource;
use Core\Classes\File;
use Core\Components\Http\Responses\FileResponse;
use Core\Components\Http\StaticAccessors\Response;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Requests\PolyCreateRequest;
use Core\Components\Resource\Requests\UpdateRequest;

/**
 * Class FileHandler
 *
 * @package App\ResourceMediaHandlers\Media
 */
class FileHandler extends BaseCompanyFileHandler
{
    /**
     * Create new file entity from upload data
     *
     * @param Entity $entity
     * @param PolyCreateRequest $request
     */
    public function create(Entity $entity, PolyCreateRequest $request): void
    {
        $file_request = $this->getFileCreateRequest(
            FileResource::TYPE_LEAD_FILE,
            $entity->get('name'),
            $entity->get('tmp_name')
        );
        $file_request->attach('handle.after', function () use ($file_request, $request) {
            $request->getMainEntity()->set('file_id', $file_request->response());
        });
        $request->getBatchRequest()->add($file_request);
    }

    /**
     * Create new file entity from passed file data and returns the file id to save with the lead file resource
     *
     * Used for lead form service when passing lead files through the website form builder or api
     *
     * @param Entity $entity
     * @return string file_id
     */
    public function createFileOnly(Entity $entity): string
    {
        $file_request = $this->getFileCreateRequest(
            FileResource::TYPE_LEAD_FILE,
            $entity->get('name'),
            $entity->get('tmp_name')
        );
        return $file_request->run();
    }

    /**
     * Update or create file entity from upload data
     *
     * @param Entity $entity
     * @param UpdateRequest $request
     */
    public function update(Entity $entity, UpdateRequest $request): void
    {
        $file_id = $this->resource->getFields()->get('file_id')->outputValueFromModel($request->getModel());
        $this->getFileUpdateRequest($file_id, $entity->get('name'), $entity->get('tmp_name'))->run();
    }

    /**
     * Get response for media controller
     *
     * @param mixed $id
     * @param array $config
     * @return FileResponse
     * @throws \Core\Components\Resource\Exceptions\MediaNotFoundException
     */
    public function getResponse(mixed $id, array $config = []): FileResponse
    {
        $media = $this->resource->findOrFail($id);

        return Response::file($this->getPathFromFileField('file_id', $media))
            ->contentType($media->file->contentType)
            ->filename(File::sanitizeName($media->name, $media->file->extension));
    }
}
