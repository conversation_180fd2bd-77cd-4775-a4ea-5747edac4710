<?php

declare(strict_types=1);

namespace App\ResourceMediaHandlers;

use App\Resources\FileResource;
use Core\Components\Http\Classes\URLBuilder;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Exceptions\MediaNotFoundException;
use Core\Components\Resource\MediaHandlers\OriginalHandler;
use Core\Components\Resource\Requests\CreateRequest;
use Core\Components\Resource\Requests\DeleteRequest;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Components\Resource\Traits\Media\TimerTrait;
use Illuminate\Database\Eloquent\Model;

/**
 * Class BaseCompanyFileHandler
 *
 * @package App\ResourceMediaHandlers
 */
abstract class BaseCompanyFileHandler extends OriginalHandler
{
    use TimerTrait;

    /**
     * @var FileResource|null Cache of file resource instance
     */
    protected ?FileResource $file_resource = null;

    /**
     * Get and cache file resource instance with current ACL
     *
     * @return FileResource
     */
    protected function getFileResource(): FileResource
    {
        if ($this->file_resource === null) {
            $this->file_resource = FileResource::make($this->getResource()->acl());
        }
        return $this->file_resource;
    }

    /**
     * Get new file create request using type, name, and path
     *
     * @param int $type
     * @param string $name
     * @param string $path
     * @param string|null $description
     * @return CreateRequest
     */
    protected function getFileCreateRequest(int $type, string $name, string $path, ?string $description = null): CreateRequest
    {
        return $this->getFileResource()->create(Entity::make([
            'type' => $type,
            'status' => FileResource::STATUS_FINISHED,
            'name' => $name,
            'description' => $description,
            'path' => $path,
            'time' => $this->getElapsedTime()
        ]))
            ->store('media_type_version', $this->getMediaTypeVersion());
    }

    /**
     * Get new file update request using existing id, name, and path
     *
     * @param string $id
     * @param string $name
     * @param string $path
     * @return UpdateRequest
     */
    protected function getFileUpdateRequest(string $id, string $name, string $path): UpdateRequest
    {
        return $this->getFileResource()->partialUpdate(Entity::make([
            'id' => $id,
            'name' => $name,
            'path' => $path,
            'time' => $this->getElapsedTime()
        ]))
            ->store('media_type_version', $this->getMediaTypeVersion());
    }

    /**
     * Get new file delete request using existing id
     *
     * @param string $id
     * @param bool $data
     * @return DeleteRequest
     */
    protected function getFileDeleteRequest(string $id, bool $data = false): DeleteRequest
    {
        return $this->getFileResource()->delete(Entity::make([
            'id' => $id
        ]))
            ->store('media_type_version', $this->getMediaTypeVersion())
            ->store('delete_data', $data);
    }

    /**
     * Delete file record by field name and model
     *
     * If data is true, the source data for the file will also be deleted
     *
     * @param string $field
     * @param Model $model
     * @param bool $data
     */
    public function deleteByFileField(string $field, Model $model, bool $data = false): void
    {
        $file_id = $this->getResource()->getFields()->get($field)->outputValueFromModel($model);
        $this->getFileDeleteRequest($file_id, $data)->run();
    }

    /**
     * Get path to file from file id
     *
     * @param string $id
     * @return string
     */
    public function getPathFromFileID(string $id): string
    {
        return $this->getMediaTypeVersion()->getPath($id);
    }

    /**
     * Get absolute path from a file field and model
     *
     * @param string $field
     * @param Model $model
     * @return string
     * @throws MediaNotFoundException
     */
    public function getPathFromFileField(string $field, Model $model): string
    {
        $file_id = $this->getResource()->getFields()->get($field)->outputValueFromModel($model);
        if ($file_id === null) {
            throw new MediaNotFoundException('No file associated entity field: %s', $field);
        }
        return $this->getPathFromFileID($file_id);
    }

    /**
     * Get url from entity id
     *
     * Should be the id of the entity which stores the file, not the file itself.
     *
     * @param string|int $id
     * @return URLBuilder
     */
    public function getUrlFromID(string|int $id): URLBuilder
    {
        return $this->getMediaTypeVersion()->getUrl($id);
    }

    /**
     * Get public URL builder instance which points to file
     *
     * @param Model $model
     * @return URLBuilder
     */
    public function getUrlFromModel(Model $model): URLBuilder
    {
        $id = $this->getResource()->getPrimaryField()->outputValueFromModel($model);
        return $this->getUrlFromID($id);
    }
}
