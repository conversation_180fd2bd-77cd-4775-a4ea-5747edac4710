<?php

namespace App\ResourceDelegates\LeadForm;

use App\Resources\LeadForm\FieldResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\Scope;

class FieldDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    /**
     * Build fields for LeadFormField.
     */
    public function buildFields(FieldList $list)
    {
        $list->field('id')
            ->primary()
            ->column('leadFormFieldID')
            ->onAction(FieldResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            })
            ->noSave();

        $list->field('lead_form_id')
            ->typeUuid()
            ->column('leadFormID')
            ->validation('Lead Form ID', 'required|uuid')
            ->label('Lead Form ID');

        $list->field('reference')
            ->column('reference')
            ->validation('Reference', 'required|type[string]|max_length[50]');

        $list->field('field_type')
            ->column('fieldType')
            ->validation('Field Type', 'required|numeric|max_length[50]');

        $list->field('label')
            ->column('label')
            ->validation('Label', 'required|type[string]|max_length[50]');

        $list->field('instruction')
            ->column('instruction')
            ->validation('Instruction', 'nullable|optional|type[string]|max_length[255]');

        $list->field('is_enabled')
            ->column('isEnabled')
            ->validation('Is Enabled', 'required|type[bool]');

        $list->field('is_required')
            ->column('isRequired')
            ->validation('Is Required', 'required|type[bool]');

        // Additional timestamp fields
        $this->timestampFields($list);

        return $list;
    }


    /**
     * Handle actions after LeadFormField is updated.
     */
    public function anyUpdateModelDataAfter($model_data, $request)
    {
        return $model_data;
    }

    /**
     * Determine if a specific action is allowed for the LeadFormField resource.
     */
    public function actionAllowed($action, FieldResource $resource)
    {
        if (in_array($action, [FieldResource::ACTION_GET_COLLECTION, FieldResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        return false;
    }

    /**
     * Configure the scope for LeadFormField queries.
     */
    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'collection-v1':
                $scope->fields([
                    'id', 'lead_form_id', 'field_type', 'label', 'is_enabled', 'is_required', 'instruction', 'created_at', 'updated_at'
                ]);
                break;
            case 'form-v1':
                $scope->fields([
                    'reference', 'lead_form_id', 'field_type', 'label', 'is_enabled', 'is_required', 'instruction'
                ], true);
                break;
        }
    }
}
