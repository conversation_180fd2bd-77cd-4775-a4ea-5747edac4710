<?php

namespace App\ResourceDelegates;

use App\NotificationJobs\User\TaskAssignmentNotificationJob;
use App\Resources\CustomerResource;
use App\Resources\LeadResource;
use App\Resources\ProjectResource;
use App\Resources\TaskResource;
use App\Resources\PropertyResource;
use App\Resources\UserResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Carbon\Carbon;
use Common\Models\Customer;
use Common\Models\Project;
use Common\Models\Property;
use Common\Models\Task;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Relations\PolyRelation;
use Core\Components\Resource\Requests\CollectionRequest;
use Core\Components\Resource\Requests\CreateRequest;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\Validator;
use Core\Exceptions\AppException;
use Ramsey\Uuid\UuidInterface;

class TaskDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->polymorphic('association')
            ->typeField('association_type')->idField('association_id')
            ->types(function (PolyRelation $relation) {
                $relation->type(TaskResource::ASSOCIATION_TYPE_CUSTOMER)->resource(CustomerResource::class);
                $relation->type(TaskResource::ASSOCIATION_TYPE_PROPERTY)->resource(PropertyResource::class);
                $relation->type(TaskResource::ASSOCIATION_TYPE_PROJECT)->resource(ProjectResource::class);
                $relation->type(TaskResource::ASSOCIATION_TYPE_LEAD)->resource(LeadResource::class);
            });
        $list->oneOrMany('cancelled_by_user')->modelRelation('cancelledByUser')->resource(UserResource::class);
        $list->oneOrMany('completed_by_user')->modelRelation('completedByUser')->resource(UserResource::class);
        $list->oneOrMany('assigned_to')->modelRelation('assignedTo')->resource(UserResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->typeUuid()
            ->column('taskID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction(
                TaskResource::ACTION_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid|pk_available')
                    ->save();
            })
            ->enableAction(TaskResource::ACTION_FILTER)
            ->label('Id');

        $list->field('company_id')
            ->column('companyID', true)
            ->validation('Company Id', 'required|type[int]|check_company_id');

        $list->field('status')
            ->label('Status')
            ->requireColumn()
            ->validationRules('required|type[int]|in_array[statuses]|check_status')
            ->onAction([TaskResource::ACTION_CREATE, TaskResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->disable();
            })
            ->onAction(TaskResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $status_names = TaskResource::getStatusNames();
        $list->field('status_name')
            ->onDemand()
            ->label('Status')
            ->value(function (Task $task) use ($status_names) {
                return $status_names[$task->status];
            });

        $list->field('title')
            ->label('Title')
            ->validationRules('trim|required|max_length[1000]');

        $list->field('notes')
            ->label('notes')
            ->validationRules('trim|nullable|optional|max_length[50000]');

        $list->field('completion_notes')
            ->column('completionNotes')
            ->label('completion_notes')
            ->validationRules('trim|nullable|optional|max_length[50000]');

        $list->field('type')
            ->requireColumn()
            ->validation('Type', 'nullable|optional|type[int]|in_array[types]')
            ->onAction(TaskResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $type_names = TaskResource::getTypeNames();
        $list->field('type_name')
            ->onDemand()
            ->label('Type')
            ->value(function (Task $task) use ($type_names) {
                return $type_names[$task->type];
            });

        $list->field('priority')
            ->requireColumn()
            ->validation('Priority', 'nullable|optional|type[int]|in_array[priorities]')
            ->onAction(TaskResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $priority_names = TaskResource::getPriorityNames();
        $list->field('priority_name')
            ->onDemand()
            ->label('Priority')
            ->value(function (Task $task) use ($priority_names) {
                return $priority_names[$task->priority];
            });

        $type_map = TaskResource::getAssociationTypeMap();
        $list->field('association_type')
            ->column('associationType')
            ->requireColumn()
            ->validation('Association Type', 'nullable|optional|type[int]|in_array[association_types]')
            ->saveMutator(function ($value) use ($type_map) {
                if($value === null) {
                    return null;
                }
                return array_search($value, $type_map);
            })
            ->outputMutator(function ($value) use ($type_map) {
                if($value === null) {
                    return null;
                }
                return $type_map[$value];
            })
            ->onAction(TaskResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $association_type_names = TaskResource::getAssociationTypeNames();
        $list->field('association_type_name')
            ->onDemand()
            ->label('Association Type')
            ->value(function (Task $task) use ($association_type_names) {
                if ($task->associationType === null) {
                    return null;
                }
                return $association_type_names[$task->associationType];
            });

        $list->field('association_id')
            ->column('associationID')
            ->validation('Association Id', 'nullable|required_if[association_type_not_null]|type[int]|check_association_id')
            ->onAction(TaskResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('association_uuid')
            ->column('associationUUID')
            ->typeUuid()
            ->validation('Association UUID', 'nullable|required_if[association_type_not_null]|uuid');

        $list->polyField('association')->typeField('association_type')->keyField('association_id');

        $list->field('assigned_to_user_id')
            ->column('assignedToUserID')
            ->validation('Assigned To User Id', 'nullable|optional|type[int]|check_assigned_to_user_id')
            ->onAction(TaskResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('assigned_to_user_name')
            ->onDemand()
            ->label('Assigned To User Name')
            ->query(function ($query) {
                return $query->leftJoin('user as assigned_to', 'assigned_to.userID', '=', 'tasks.assignedToUserID');
            })
            ->rawColumn('IF(tasks.assignedToUserID, CONCAT(assigned_to.userFirstName, \' \', assigned_to.userLastName), null)', 'assigned_to_user_name')
            ->onAction(TaskResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('due_date')
            ->typeDateTime()
            ->column('dueDate')
            ->label('Due Date')
            ->validationRules('nullable|optional|iso8601_date:datetime|to_carbon|to_utc')
            ->onAction(TaskResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required');
            });

        $list->field('reminder_type')
            ->column('reminderType')
            ->validation('Reminder Type', 'nullable|optional|type[int]|in_array[reminder_types]');

        $list->field('completed_at')
            ->typeDateTime()
            ->column('completedAt')
            ->label('Completed At')
            ->immutable()
            ->onAction(TaskResource::ACTION_SORT, function (Field $field) {
                return $field->enableAction(TaskResource::ACTION_SORT)
                    ->mutable();
            })
            ->onAction(TaskResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required')
                    ->mutable();
            });

        $list->field('completed_by_user_id')
            ->column('completedByUserID', true)
            ->onAction(TaskResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required')
                    ->mutable();
            })
            ->immutable();

        $list->field('completed_by_user_name')
            ->onDemand()
            ->label('Completed By User Name')
            ->query(function ($query) {
                return $query->leftJoin('user as completed_by', 'completed_by.userID', '=', 'tasks.completedByUserID');
            })
            ->rawColumn('IF(tasks.completedByUserID, CONCAT(completed_by.userFirstName, \' \', completed_by.userLastName), null)', 'completed_by_user_name')
            ->onAction(TaskResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $this->timestampFields($list, true, true, true);

        $list->modify([
            'title', 'status', 'type', 'priority', 'due_date', 'association_type'
        ], function (Field $field) {
            return $field->enableAction(TaskResource::ACTION_SORT);
        });

        return $list;
    }

    public function configureFields(FieldList $list, TaskResource $resource)
    {
        if ($resource->acl()->user() !== null) {
            $list->get('company_id')->disable();
        }

        return $list;
    }

    public function validationRules(Rules $rules, TaskResource $resource)
    {
        $rules->register('check_company_id', function ($id) use ($resource) {
            if ($resource->relationResource('company')->entityExists($id)) {
                return true;
            }
            return 'check_company_id';
        }, [
            'check_company_id' => 'Unable to find company'
        ]);

        $rules->register('check_association_id', function ($id, $params, Validator $validator) use ($resource) {
            if ($validator->errors()->has('association_type')) {
                return Rules::STOP;
            }
            $association = $validator->data('association_type');
            $association_resource = $resource->polyRelationResource('association', $association);

            if (!$association_resource->entityExists($id)) {
                return 'check_association_id';
            }
            return true;
        }, [
            'check_association_id' => 'Unable to find associated item'
        ]);

        $rules->register('check_assigned_to_user_id', function ($id) use ($resource) {
            if ($resource->relationResource('assigned_to')->entityExists($id)) {
                return true;
            }
            return 'check_assigned_to_user_id';
        }, [
            'check_assigned_to_user_id' => 'Unable to find assigned to user'
        ]);

        $rules->register('check_status', function ($status, $params, Validator $validator) {
            $model = $validator->getConfig()->storage('_model');
            $prev_status = $model !== null ? $model->status : null;
            if ($prev_status !== null && $prev_status === $status) {
                return true;
            }

            // sets the allowed transitions between statuses, could use a finite state machine here I think
            $allowed_statuses_config = [
                null => [TaskResource::STATUS_ACTIVE],
                TaskResource::STATUS_ACTIVE => [TaskResource::STATUS_COMPLETED],
                TaskResource::STATUS_COMPLETED => [TaskResource::STATUS_ACTIVE]
            ];

            if (!isset($allowed_statuses_config[$prev_status])) {
                throw new AppException('Unable to find allowed status config for status: %d', $prev_status);
            }
            $allowed_statuses = $allowed_statuses_config[$prev_status];
            if (in_array($status, $allowed_statuses, true)) {
                return true;
            }
            if ($prev_status === null) {
                return 'status_invalid';
            }
            if (count($allowed_statuses) === 0) {
                return 'status_final';
            }
            return ['status_invalid_transition', [
                'statuses' => implode(', ', $allowed_statuses)
            ]];
        }, [
            'status_invalid' => 'Status is not a valid value',
            'status_invalid_transition' => 'Only can transition to the following statuses: {statuses}',
            'status_final' => 'Status can no longer be changed'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('statuses', TaskResource::getStatuses());
        $config->store('types', TaskResource::getTypes());
        $config->store('priorities', TaskResource::getPriorities());
        $config->store('reminder_types', TaskResource::getReminderTypes());
        $config->store('association_types', TaskResource::getAssociationTypes());
        $config->store('is_not_completed', function ($field, Validator $validator) {
            return $validator->data('status') !== TaskResource::STATUS_COMPLETED;
        });
        $config->store('association_type_not_null', function ($item_id, Validator $validator) {
            return $validator->data('association_type') !== null;
        });

        return $config;
    }

    public function anyCreateModelDataAfter($model_data, CreateRequest $request)
    {
        $user = $request->resource()->acl()->user();
        if ($user !== null) {
            $model_data['companyID'] = $user->companyID;
        }
        $model_data['status'] = Task::STATUS_ACTIVE;

        if ($model_data['assignedToUserID'] !== null && $model_data['assignedToUserID'] !== $user->userID) {
            $request->store('send_assigned_notification', true);
        }

        return $model_data;
    }

    public function anyUpdateModelDataAfter($model_data, UpdateRequest $request)
    {
        $resource = $request->resource();
        $model = $request->getModel();
        $user = $request->resource()->acl()->user();
        if (isset($model_data['status']) && $model_data['status'] !== $model->status) {
            switch ($model_data['status']) {
                case Task::STATUS_ACTIVE:
                    $model_data['completedAt'] = null;
                    $model_data['completedByUserID'] = null;
                    break;
                case Task::STATUS_COMPLETED:
                    $model_data['completedAt'] = Carbon::now('UTC');
                    $model_data['completedByUserID'] = $resource->acl()->user()->getKey();
                    break;
            }
        }
        if (!isset($model_data['type'])) {
            $model_data['type'] = $model->type;
        }
        if (!isset($model_data['priority'])) {
            $model_data['priority'] = $model->priority;
        }

        if (
            isset($model_data['assignedToUserID']) &&
            $model_data['assignedToUserID'] !== $model->assignedToUserID &&
            $model_data['assignedToUserID'] !== $user->userID
        ) {
            $request->store('send_assigned_notification', true);
        }

        return $model_data;
    }

    public function createSaveAfter(CreateRequest $request)
    {
        if ($request->storage('send_assigned_notification', false)) {
            $model = $request->getModel();
            TaskAssignmentNotificationJob::enqueue($model->getUuidKey());
        }
    }

    public function anyUpdateSaveAfter(UpdateRequest $request): void
    {
        if ($request->storage('send_assigned_notification', false)) {
            $model = $request->getModel();
            TaskAssignmentNotificationJob::enqueue($model->getUuidKey());
        }
    }

    public function queryScopeGlobal($query, TaskResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        // @todo need to test this
        if (!$user->primary && !$user->projectManagement) {
            return $query->ofUser($user);
        }
        return $query->ofCompany($user->companyID);
    }

    public function queryScopeSearch($query, $term)
    {
        return $query->search($term);
    }

    /**
     * Determines what actions are allowed based on the ACL of the passed resource
     *
     * Any collection and entity requests are allowed, but all other actions are only allowed based on the user's roles
     *
     * @param int $action
     * @param TaskResource $resource
     * @return bool
     */
    public function actionAllowed($action, TaskResource $resource)
    {
        if (in_array($action, [TaskResource::ACTION_GET_COLLECTION, TaskResource::ACTION_GET_ENTITY])) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        //@todo check if user can delete a task
        // if user is project management or sales, then we only allow them to do non-delete actions
        if (($user->projectManagement || $user->sales || $user->installation || $user->marketing || $user->metrics) && ($action & TaskResource::ACTION_GROUP_DELETE) === 0) {
            return true;
        }
        return false;
    }

    public function requestCollectionScope(Scope $scope, CollectionRequest $request)
    {
        if ($scope->isSearching() && in_array($scope->getFormat(), ['collection-v1', 'export-v1'])) {
            $resource = $request->resource();
            $term = $scope->getSearch();

            $project_association_query = $resource->newScopedQuery()
                ->join('project', 'project.projectID', '=', 'tasks.associationID');
            (new Project())->scopeSearch($project_association_query, $term);
            $request->unionQuery($project_association_query);

            $property_association_query = $resource->newScopedQuery()
                ->join('property', 'property.propertyID', '=', 'tasks.associationID');
            (new Property())->scopeSearch($property_association_query, $term);
            $request->unionQuery($property_association_query);

            $customer_association_query = $resource->newScopedQuery()
                ->join('customer', 'customer.customerID', '=', 'tasks.associationID');
            (new Customer())->scopeSearch($customer_association_query, $term);
            $request->unionQuery($customer_association_query);

            $request->enableChunking()->chunkLimit(100);
        }
    }

    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'collection-v1':
                $scope->fields([
                    'id', 'status', 'title', 'notes', 'completion_notes', 'type', 'priority', 'association_type',
                    'association_id', 'assigned_to_user_id', 'assigned_to_user_name', 'due_date', 'reminder_type',
                    'completed_at', 'completed_by_user_name', 'created_at', 'created_by_user_name', 'created_by_user_id',
                    'updated_at', 'updated_by_user_name'
                ]);
                $scope->with([
                    'association' => [
                        'poly_scopes' => [
                            TaskResource::ASSOCIATION_TYPE_CUSTOMER => [
                                'fields' => ['id', 'first_name', 'last_name', 'business_name']
                            ],
                            TaskResource::ASSOCIATION_TYPE_PROPERTY => [
                                'fields' => ['id', 'address', 'city', 'state', 'zip']
                            ],
                            TaskResource::ASSOCIATION_TYPE_PROJECT => [
                                'fields' => ['id', 'description']
                            ],
                            TaskResource::ASSOCIATION_TYPE_LEAD => [
                                'fields' => ['lead_uuid', 'first_name', 'last_name']
                            ]
                        ]
                    ]
                ]);
                break;
            case 'task-v1':
                $scope->fields([
                    'id', 'status', 'title', 'notes', 'completion_notes', 'type', 'priority', 'association_type',
                    'association_id', 'association_uuid', 'assigned_to_user_id', 'assigned_to_user_name', 'due_date',
                    'reminder_type', 'completed_at', 'completed_by_user_name', 'created_at', 'created_by_user_name'
                ]);
                $scope->with(['association']);
                break;
            case 'export-v1':
                $scope->fields([
                    'title', 'priority_name', 'type_name', 'assigned_to_user_name', 'association_type_name',
                    'status_name', 'due_date', 'completed_at', 'completed_by_user_name', 'created_at',
                    'created_by_user_name'
                ], true);
                break;
        }
    }
}
