<?php

namespace App\ResourceDelegates;

use App\Resources\Bid\ContentResource;
use App\Resources\CompanyResource;
use App\Resources\EmailTemplateResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Carbon\Carbon;
use Common\Models\EmailTemplate;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Request;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Exceptions\ActionNotAllowedException;
use Core\Components\Resource\Relations\PolyRelation;
use Core\Components\Resource\Requests\CreateRequest;
use Core\Components\Resource\Requests\UpdateRequest;
use Core\Components\Validation\Classes\FieldConfig;
use Core\Components\Validation\Classes\Rules;
use Core\Components\Validation\Classes\Validator;
use Ramsey\Uuid\UuidInterface;

class EmailTemplateDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->polymorphic('owner')->typeField('owner_type')->idField('owner_id')
            ->types(function (PolyRelation $relation) {
                $relation->type(EmailTemplateResource::OWNER_TYPE_COMPANY)->resource(CompanyResource::class);
            });
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id')
            ->primary()
            ->typeUuid()
            ->column('emailTemplateID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([EmailTemplateResource::ACTION_CREATE, EmailTemplateResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid|check_id')
                    ->save();
            });

        $owner_type_map = EmailTemplateResource::getOwnerTypeMap();
        $list->field('owner_type')
            ->column('ownerType', true)
            ->validation('Owner Type', 'required|type[int]|in_array[owner_types]')
            ->saveMutator(function ($value) use ($owner_type_map) {
                return array_search($value, $owner_type_map);
            })
            ->outputMutator(function ($value) use ($owner_type_map) {
                return $owner_type_map[$value];
            });

        $list->field('owner_id')
            ->column('ownerID', true)
            ->validation('Owner Id', 'required|type[int]|check_owner_id')
            ->onAction(EmailTemplateResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|type[int]');
            });

        $list->field('source')
            ->column('source', true)
            ->validation('Source', 'required|type[int]|in_array[sources]');

        $list->field('type')
            ->column('type', true)
            ->validation('Type', 'required|type[int]|in_array[types]')
            ->onAction(EmailTemplateResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('type_name')
            ->onDemand()
            ->query(function ($query) {
                return $query->join('emailTemplateTypes', 'emailTemplateTypes.emailTemplateTypeID', '=', 'emailTemplates.type');
            })
            ->rawColumn('emailTemplateTypes.name', 'type_name')
            ->onAction(EmailTemplateResource::ACTION_SORT, function (Field $field) {
                return $field->onDemand(false);
            });

        $list->field('name')
            ->validation('Name', 'required|max_length[100]');

        $list->field('subject')
            ->validation('Subject', 'required|max_length[100]');

        $list->field('content')
            ->validation('Content', 'wysiwyg_trim|clean_html|required');

        $list->field('is_send_from_salesperson')
            ->column('isSendFromSalesperson')
            ->validation('Is Sent From Salesperson', 'nullable|optional|type[bool]');

        $list->field('status')
            ->validation('Status', 'required|type[int]|in_array[statuses]')
            ->disableAction([EmailTemplateResource::ACTION_CREATE, EmailTemplateResource::ACTION_NESTED_CREATE])
            ->onAction(EmailTemplateResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('archived_at')
            ->typeDateTime()
            ->column('archivedAt')
            ->immutable();

        $list->field('archived_by_user_id')
            ->column('archivedByUserID')
            ->immutable();

        $this->timestampFields($list, true, true, true);

        $list->modify(['name', 'type'], function (Field $field) {
            return $field->enableAction(EmailTemplateResource::ACTION_SORT);
        });

        return $list;
    }

    public function configureFields(FieldList $list, EmailTemplateResource $resource)
    {
        if ($resource->acl()->user() !== null) {
            $list->get('owner_type')->disable();
            $list->get('owner_id')->disable();
        }

        return $list;
    }

    public function validationRules(Rules $rules, EmailTemplateResource $resource)
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_owner_id', function ($id, $params, Validator $validator) use ($resource) {
            if ($validator->errors()->has('owner_type')) {
                return Rules::STOP;
            }
            $type = $validator->data('owner_type');
            $owner_type_resource = $resource->polyRelationResource('owner', $type);

            if (!$owner_type_resource->entityExists($id)) {
                return 'check_owner_id';
            }
            return true;
        }, [
            'check_owner_id' => 'Unable to find owner'
        ]);

        return $rules;
    }

    public function validationFieldConfig(FieldConfig $config)
    {
        $config->store('owner_types', EmailTemplateResource::getOwnerTypes());
        $config->store('sources', EmailTemplateResource::getSources());
        $config->store('types', EmailTemplateResource::getTypes());
        $config->store('statuses', EmailTemplateResource::getStatuses());
        return $config;
    }

    public function anyCreateModelDataAfter($model_data, CreateRequest $request)
    {
        $user = $request->resource()->acl()->user();
        if ($user !== null) {
            $model_data['ownerType'] = EmailTemplate::OWNER_COMPANY;
            $model_data['ownerID'] = $user->companyID;
        }
        $model_data['status'] = EmailTemplateResource::STATUS_ACTIVE;
        return $model_data;
    }

    public function anyUpdateModelDataAfter($model_data, UpdateRequest $request)
    {
        $resource = $request->resource();
        $model = $request->getModel();
        if (isset($model_data['status']) && $model_data['status'] !== $model->status) {
            switch ($model_data['status']) {
                case EmailTemplateResource::STATUS_ACTIVE:
                    $model_data['archivedAt'] = null;
                    $model_data['archivedByUserID'] = null;
                    break;
                case EmailTemplateResource::STATUS_ARCHIVED:
                    $model_data['archivedAt'] = Carbon::now('UTC');
                    $model_data['archivedByUserID'] = $resource->acl()->user()->getKey();
                    break;
            }
        }
        return $model_data;
    }

    public function queryScopeGlobal($query, EmailTemplateResource $resource)
    {
        $user = $resource->acl()->user();
        if ($user === null) {
            return $query;
        }
        return $query->ofCompany($user->companyID);
    }

    public function queryScopeSearch($query, $term)
    {
        return $query->searchWithRank($term);
    }

    public function anyUpdateValidateAfter(Entity $entity, UpdateRequest $request)
    {
        /** @var EmailTemplateResource $resource */
        $resource = $request->resource();
        $model = $request->getModel();
        if (isset($entity['status'])) {
            $status = $resource->getFields()->get('status')->saveValue($entity['status']);
            if ($status !== $model->status) {
                $source = $resource->getFields()->get('source')->saveValue($entity['status']);
                if ($source === EmailTemplateResource::SOURCE_SYSTEM) {
                    throw new ActionNotAllowedException('Cannot archive this email template');
                }
            }
        }
        return $entity;
    }

    public function actionAllowed($action, EmailTemplateResource $resource)
    {
        if (($action & EmailTemplateResource::ACTION_GROUP_READ_ONLY_FULL) > 0) {
            return true;
        }
        $user = $resource->acl()->user();
        if ($user === null || $user->primary) {
            return true;
        }
        return false;
    }

    public function scopeBuildBefore(Scope $scope)
    {
        switch ($scope->getFormat()) {
            case 'collection-v1':
                $scope->fields([
                    'id', 'name', 'type', 'source', 'content', 'subject', 'is_send_from_salesperson', 'updated_at', 'created_by_user_name'
                ]);
                $scope->filter('status', 'eq', EmailTemplateResource::STATUS_ACTIVE);
                $scope->query(function ($query) {
                    return $query->ordered();
                });
                break;
        }
    }

}
