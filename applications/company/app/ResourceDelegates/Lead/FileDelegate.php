<?php

declare(strict_types=1);

namespace App\ResourceDelegates\Lead;

use App\ResourceMediaHandlers\Lead\FileHandler;
use App\Resources\FileResource;
use App\Resources\Lead\FileResource AS LeadFileResource;
use App\Resources\LeadResource;
use App\Traits\Resource\MutableTrait;
use App\Traits\ResourceDelegate\TimestampFieldsTrait;
use Common\Models\File;
use Core\Components\Resource\Classes\Field;
use Core\Components\Resource\Classes\FieldList;
use Core\Components\Resource\Classes\MediaList;
use Core\Components\Resource\Classes\MediaType;
use Core\Components\Resource\Classes\RelationList;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Requests\DeleteRequest;
use Core\Components\Validation\Classes\Rules;
use Ramsey\Uuid\UuidInterface;

class FileDelegate
{
    use MutableTrait;
    use TimestampFieldsTrait;

    public function buildRelations(RelationList $list)
    {
        $list->oneOrMany('lead')->resource(LeadResource::class);
        $list->oneOrMany('file')->resource(FileResource::class);

        return $list;
    }

    public function buildFields(FieldList $list)
    {
        $list->field('id', true)
            ->typeUuid()
            ->column('leadFileID')
            ->validation('Id', 'required|uuid')
            ->noSave()
            ->onAction([LeadFileResource::ACTION_CREATE, LeadFileResource::ACTION_NESTED_CREATE], function (Field $field) {
                return $field->validationRules('required|uuid')
                    ->save();
            });

        $list->field('lead_id')
            ->column('leadID', true)
            ->validation('Lead Id', 'required|cast[int]|type[int]|check_lead_id')
            ->onAction(LeadFileResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|cast[int]|type[int]');
            })
            ->onAction(LeadFileResource::ACTION_FILTER, function (Field $field) {
                return $field->validationRules('required|numeric');
            });

        $list->field('name')
            ->validation('Name', 'trim|required|max_length[250]');

        $list->field('file_id')
            ->typeUuid()
            ->column('fileID', true)
            ->validation('File Id', 'uuid|check_file_id')
            ->onAction(LeadFileResource::ACTION_NESTED_CREATE, function (Field $field) {
                return $field->validationRules('required|uuid');
            });

        $list->mediaField('file')
            ->validation('File', [
                'mimes' => [
                    // microsoft
                    'ppt', 'pptx', 'doc', 'docx', 'xls', 'xlsx', 'msg',

                    // open document
                    'pps', 'ppsx',

                    // apple
                    'key', 'numbers', 'pages',

                    // images
                    'jpeg', 'png', 'gif', 'tiff', 'heic',

                    // video
                    'mp4', 'm4v', 'qt', 'asf', 'avi', 'ogv', 'webm', '3gp',

                    // audio
                    'mpga', 'oga', 'wav',

                    // document
                    'pdf', 'csv', 'txt', 'dwg', 'rtf', 'html', 'zip', 'eml'
                ],
                'max_size' => '60MB'
            ]);

        $this->timestampFields($list, true, true, true);

        return $list;
    }

    public function buildMedia(MediaList $list)
    {
        $list->type('file')
            ->directoryName('lead-files', true)
            ->versions(function (MediaType $type) {
                $type->original()->handler(FileHandler::class);
            });
        return $list;
    }

    public function validationRules(Rules $rules, LeadFileResource $resource)
    {
        $rules->register('check_id', function (UuidInterface $id) use ($resource) {
            return $resource->primaryFieldInUse($id->toString()) ? 'check_id' : true;
        }, [
            'check_id' => '{label} is already in use'
        ]);

        $rules->register('check_lead_id', function ($id) use ($resource) {
            if ($resource->relationResource('lead')->entityExists($id)) {
                return true;
            }
            return 'check_lead_id';
        }, [
            'check_lead_id' => 'Unable to find lead'
        ]);

        $rules->register('check_file_id', function (UuidInterface $id) use ($resource) {
            $file = $resource->relationResource('file')->find($id->toString());
            if ($file === null) {
                return 'file_not_found';
            }
            if ($file->type !== File::TYPE_LEAD_FILE) {
                return 'file_invalid_type';
            }
            return true;
        }, [
            'file_not_found' => '{label} does not exist',
            'file_invalid_type' => '{label} must be a lead file'
        ]);

        return $rules;
    }

    public function deleteSaveAfter(DeleteRequest $request): void
    {
        /** @var LeadFileResource $resource */
        $resource = $request->resource();

        /** @var FileHandler $file_handler */
        $file_handler = $resource->getMediaHandler('file');
        $file_handler->deleteByFileField('file_id', $request->getModel());
    }

    public function queryScopeGlobal($query, LeadFileResource $resource)
    {
        $company_id = $resource->acl()->companyID(true);
        if ($company_id !== null) {
            $query->ofCompany($company_id);
        }
        return $query;
    }

    public function scopeBuildBefore(Scope $scope): void
    {
        switch ($scope->getFormat()) {
            case 'collection-v1':
                $scope->fields(['id', 'name', 'created_at'], true);
                $scope->with([
                    'file_media_urls',
                    'file' => [
                        'fields' => ['name', 'extension', 'size', 'content_type']
                    ]
                ]);

                $scope->disablePagination();
                break;
        }
    }
}
