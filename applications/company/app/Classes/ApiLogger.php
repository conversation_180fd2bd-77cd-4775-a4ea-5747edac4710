<?php

declare(strict_types=1);

namespace App\Classes;

use App\Exceptions\ApiException;
use Carbon\Carbon;
use Closure;
use Common\Classes\DB\Model;
use Common\Models\ApiRequest;
use Core\Classes\{Arr, Config};
use Common\Models\LeadFormRequest;
use Core\Components\Auth\Classes\Auth;
use Core\Components\Http\Interfaces\{RequestInterface, ResponseInterface};
use Core\StaticAccessors\App;
use Throwable;
use Ramsey\Uuid\{Uuid, UuidInterface};

/**
 * Class ApiLogger
 *
 * @package App\Classes
 */
class ApiLogger
{
    public const PART_REQUEST_BODY = 1;
    public const PART_RESPONSE_BODY = 2;

    /**
     * @var bool Determines if API logging is enabled
     */
    protected bool $enabled = true;

    /**
     * @var Auth Auth instance used to get user info
     */
    protected Auth $auth;

    /**
     * @var RequestInterface|null Request interface to log from
     */
    protected ?RequestInterface $request = null;

    /**
     * @var UuidInterface|null ID of log entry
     */
    protected ?UuidInterface $id = null;

    /**
     * @var float|null Start time of request
     */
    protected ?float $start_time = null;

    /**
     * @var Model Cached model for log entry
     */
    protected ?Model $model = null;


    /** @var class-string<Model> */
    protected string $model_class = ApiRequest::class;

    /**
     * @var array Ignore configuration for request and response bodies
     */
    protected array $ignore = [
        self::PART_REQUEST_BODY => [],
        self::PART_RESPONSE_BODY => []
    ];

    /**
     * ApiLogger constructor
     *
     * @param Config $config
     * @param Auth $auth
     */
    public function __construct(Config $config, Auth $auth, string $model_class = ApiRequest::class)
    {
        $this->enabled = $config->get('api.logging_enabled');
        $this->auth = $auth;
        $this->model_class = $model_class;
    }

    protected function getPrimaryKeyField(): string
    {
        if (is_a($this->model_class, LeadFormRequest::class, true)) {
            return 'leadFormRequestID';
        }
        return 'apiRequestID';
    }

    /**
     * Determines if API logging is allowed
     *
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    /**
     * Ignore request or response body based on specified conditions
     *
     * Used to hide API requests with sensitive data like credit card info, etc. If config is closure, the request and
     * response to passed as params to allow user to ignore data via their own logic. If array is passed, the
     * only_methods or except_methods config items can be used to ignore logging data.
     *
     * @param int $part
     * @param string $path
     * @param Closure|array|null $config
     * @return $this
     */
    public function ignore(int $part, string $path, $config = null): self
    {
        $this->ignore[$part][$path] = $config;
        return $this;
    }

    /**
     * Encode array as JSON string
     *
     * @param array $data
     * @return false|string
     */
    protected function encodeJson(array $data)
    {
        $options = JSON_UNESCAPED_SLASHES;
        if (App::debugEnabled()) {
            $options |= JSON_PRETTY_PRINT;
        }
        return json_encode($data, $options);
    }

    /**
     * Determine if request or response is ignored based on defined config
     *
     * @param int $part
     * @param RequestInterface $request
     * @param ResponseInterface|null $response
     * @return bool
     */
    protected function isIgnored(int $part, RequestInterface $request, ResponseInterface $response = null): bool
    {
        if (count($this->ignore[$part]) === 0) {
            return false;
        }
        $uri = $request->uri();
        foreach ($this->ignore[$part] as $path => $config) {
            if (!$uri->matches($path)) {
                continue;
            }
            if ($config === null) {
                return true;
            }
            if (is_object($config) && $config instanceof Closure) {
                $return = $config($request, $response);
                if (is_bool($return) && $return) {
                    return true;
                }
            }
            if (is_array($config)) {
                if (isset($config['only_methods']) && in_array($request->method(), $config['only_methods'])) {
                    return true;
                } elseif (isset($config['except_methods']) && !in_array($request->method(), $config['except_methods'])) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Get id of log entry
     *
     * If doesn't exist, a UUID is created and cached.
     *
     * @return UuidInterface
     */
    public function getID(): UuidInterface
    {
        if ($this->id === null) {
            $this->id = Uuid::uuid4();
        }
        return $this->id;
    }

    /**
     * Create/update log entry
     *
     * @param array $data
     */
    protected function saveLog(array $data): void
    {
        try {
            $cls     = $this->model_class;
            $pkField = $this->getPrimaryKeyField();

            if ($this->model === null) {
                $newID       = Uuid::uuid4()->getBytes();
                $data[$pkField] = $newID;

                $this->model = new $cls($data);
            } else {
                $this->model->fill($data);
            }

            $this->model->quietSave();
        } catch (Throwable $e) {
            ApiException::getLogger()->warning('Unable to save API log', [
                'exception' => $e,
            ]);
        }
    }

    /**
     * Set request to log
     *
     * @param RequestInterface $request
     */
    public function request(RequestInterface $request): void
    {
        if (!$this->isEnabled()) {
            return;
        }
        $this->start_time = microtime(true);

        $this->request = $request;

        $input = $request->input();

        $headers = array_filter(Arr::except($input->header(), ['Authorization']), function ($value) {
            return trim($value) !== '';
        });

        // @todo add ignore check for headers

        $request_data = '**IGNORED**';
        if (!$this->isIgnored(self::PART_REQUEST_BODY, $request)) {
            $request_data = $this->encodeJson([
                'query_string' => Arr::except($input->get(), ['_uri_']),
                'body' => [
                    'data' => Arr::except($input->post(), ['password']),
                    'files' => $input->file()
                ]
            ]);
        }

        $ip_type_map = [
            RequestInterface::IP_TYPE_V4 => ApiRequest::IP_ADDRESS_TYPE_V4,
            RequestInterface::IP_TYPE_V6 => ApiRequest::IP_ADDRESS_TYPE_V6
        ];

        [$ip_type, $ip] = $request->ip(true);
        $ip = inet_pton($ip);

        $this->saveLog([
            'ipAddressType' => $ip_type_map[$ip_type],
            'ipAddress' => $ip,
            'method' => $request->method(),
            'path' => $request->uri()->path(),
            'requestHeaders' => $this->encodeJson($headers),
            'requestData' => $request_data,
            'createdAt' => Carbon::now('UTC')
        ]);
    }

    /**
     * Log API response
     *
     * Will update the existing row. Request is logged as a separate query to help with situations where a fatal error
     * in the application would prevent any data from logging if we did it at the end of the process only.
     *
     * @param ResponseInterface $response
     */
    public function response(ResponseInterface $response): void
    {
        if (!$this->isEnabled() || $this->model === null) {
            return;
        }

        $time = $this->start_time === null ? 0 : (int) round(((microtime(true) - $this->start_time) * 1000000) / 1000);

        // @todo add ignore check for headers

        $response_data = '**IGNORED**';
        if (!$this->isIgnored(self::PART_RESPONSE_BODY, $this->request, $response)) {
            $response_data = $response->getContent();
            if (is_array($response_data)) {
                $response_data = $this->encodeJson($response_data);
            }
        }

        $update = [
            'responseHeaders' => $this->encodeJson($response->getHeaders()),
            'responseData' => $response_data,
            'statusCode' => $response->getStatusCode(),
            'time' => $time
        ];
        if (($api_token = $this->auth->userApiToken()) !== null) {
            $update['userApiTokenID'] = $api_token->getKey();
        }

        $this->saveLog($update);
    }
}
