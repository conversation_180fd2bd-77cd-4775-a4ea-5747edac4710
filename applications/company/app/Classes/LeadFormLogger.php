<?php
declare(strict_types=1);

namespace App\Classes;

use Common\Models\LeadFormRequest;
use Core\Components\Http\Interfaces\RequestInterface;
use Core\Components\Http\Interfaces\ResponseInterface;
use Ramsey\Uuid\Uuid;
class LeadFormLogger extends ApiLogger
{
    public function __construct($config, $auth)
    {
        parent::__construct($config, $auth, LeadFormRequest::class);
    }

    /**
     * Override request() so we can inject leadFormID and apiKey.
     *
     * @param RequestInterface $request
     */
    public function request(RequestInterface $request): void
    {
        if (!$this->isEnabled()) {
            return;
        }

        $leadForm     = $request->data('lead_form_by_api');
        $api_key_bytes  = $request->data('api_key_bytes');

        if (!$leadForm || !is_string($api_key_bytes)) {
            return;
        }

        parent::request($request);

        $this->model->leadFormID = $leadForm->leadFormID;
        $this->model->apiKey     = $api_key_bytes;
        $this->model->quietSave();
    }

    /**
     * Override response() so that the “response” update also retains
     * leadFormID and apiKey (they will already be on the model).
     *
     * @param ResponseInterface $response
     */
    public function response(ResponseInterface $response): void
    {
        parent::response($response);
    }
}