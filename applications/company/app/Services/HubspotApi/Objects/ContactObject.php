<?php

declare(strict_types=1);

namespace App\Services\HubspotApi\Objects;

use App\Services\HubspotApi\Classes\BaseObject;
use App\Services\HubspotApi\Entities\ContactEntity;
use HubSpot\Client\Crm\Contacts\Model\{
    Error,
    Filter,
    FilterGroup,
    PublicObjectSearchRequest,
    SimplePublicObject,
    SimplePublicObjectInput
};
use HubSpot\Client\Crm\Associations\V4\Model\AssociationSpec;
use HubSpot\Crm\ObjectType;
use HubSpot\Discovery\Crm\Contacts\Discovery;

/**
 * Class ContactObject
 *
 * @package App\Services\HubspotApi\Objects
 */
class ContactObject extends BaseObject
{
    /**
     * Get client instance
     *
     * @return Discovery
     */
    protected function getClient(): Discovery
    {
        return $this->service->getClient()->crm()->contacts();
    }

    /**
     * Determine if API response is an error
     *
     * @param mixed $response
     * @return bool
     */
    protected function isErrorResponse(mixed $response): bool
    {
        return is_object($response) && $response instanceof Error;
    }

    /**
     * Search for contacts by email
     *
     * @param string $email
     * @return array
     * @throws \App\Services\HubspotApi\Exceptions\ApiException
     */
    public function search(string $email): array
    {
        $filter = (new Filter())->setOperator('EQ')
            ->setPropertyName('email')
            ->setValue($email);
        $group = (new FilterGroup())->setFilters([$filter]);

        $request = new PublicObjectSearchRequest([
            'filter_groups' => [$group]
        ]);
        /** @var CollectionResponseWithTotalSimplePublicObjectForwardPaging $result */
        $response = $this->callApi(fn(Discovery $client) => $client->searchApi()->doSearch($request));
        return array_map(fn(SimplePublicObject $contact): ContactEntity => ContactEntity::fromSimplePublicObject($contact), $response->getResults());
    }

    /**
     * Find individual contact by id
     *
     * @param string $id
     * @return ContactEntity|null
     * @throws \App\Services\HubspotApi\Exceptions\ApiException
     */
    public function find(string $id): ?ContactEntity
    {
        /** @var SimplePublicObject $object */
        $object = $this->callApi(fn(Discovery $client) => $client->basicApi()->getById($id));
        return ContactEntity::fromSimplePublicObject($object);
    }

    /**
     * Get associated company of contact if exists
     *
     * @param string $id
     * @return string|null
     * @throws \App\Services\HubspotApi\Exceptions\ApiException
     */
    public function getAssociatedCompanyID(string $id): ?string
    {
        $response = $this->callApi(fn() => $this->service->getClient()->crm()->associations()->v4()->basicApi()->getPage(
            'contacts', $id, 'companies'
        ));
        
        if (!$response || !$response->getResults()) {
            return null;
        }
        
        $results = $response->getResults();
        return isset($results[0]) ? $results[0]->getToObjectId() : null;
    }

    /**
     * Create contact
     *
     * @param array $properties
     * @return ContactEntity
     * @throws \App\Services\HubspotApi\Exceptions\ApiException
     */
    public function create(array $properties): ContactEntity
    {
        $input = new SimplePublicObjectInput([
            'properties' => $properties
        ]);
        $object = $this->callApi(fn(Discovery $client) => $client->basicApi()->create($input));
        return ContactEntity::fromSimplePublicObject($object);
    }

    /**
     * Update contact
     *
     * @param string $id
     * @param array $properties
     * @throws \App\Services\HubspotApi\Exceptions\ApiException
     */
    public function update(string $id, array $properties)
    {
        $input = new SimplePublicObjectInput([
            'properties' => $properties
        ]);
        $this->callApi(fn(Discovery $client) => $client->basicApi()->update($id, $input));
    }

    /**
     * Associate company with contact
     *
     * @param string $id
     * @param string $company_id
     * @throws \App\Services\HubspotApi\Exceptions\ApiException
     */
    public function associateCompany(string $id, string $company_id): void
    {
        $associationSpec = new AssociationSpec([
            'association_category' => 'HUBSPOT_DEFINED',
            'association_type_id' => 1 // contact_to_company
        ]);


        $this->callApi(fn() => $this->service->getClient()->crm()->associations()->v4()->basicApi()->create(
            'contacts',
            $id,
            'companies',
            $company_id,
            [$associationSpec]
        ));
    }
}
