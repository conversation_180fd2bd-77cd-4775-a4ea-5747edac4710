<?php

declare(strict_types=1);

namespace App\Services\HubspotApi\Objects;

use App\Services\HubspotApi\Classes\BaseObject;
use App\Services\HubspotApi\Entities\DealEntity;
use HubSpot\Client\Crm\Deals\Model\{
    Error,
    SimplePublicObjectInput
};
use HubSpot\Client\Crm\Associations\V4\Model\AssociationSpec;
use HubSpot\Crm\ObjectType;
use HubSpot\Discovery\Crm\Deals\Discovery;

/**
 * Class DealObject
 *
 * @package App\Services\HubspotApi\Objects
 */
class DealObject extends BaseObject
{
    /**
     * Get client instance
     *
     * @return Discovery
     */
    protected function getClient(): Discovery
    {
        return $this->service->getClient()->crm()->deals();
    }

    /**
     * Determine if API response is an error
     *
     * @param mixed $response
     * @return bool
     */
    protected function isErrorResponse(mixed $response): bool
    {
        return is_object($response) && $response instanceof Error;
    }

    /**
     * Get all associated contact ids for deal
     *
     * @param string $id
     * @return array
     * @throws \App\Services\HubspotApi\Exceptions\ApiException
     */
    public function getAssociatedContactIds(string $id): array
    {
        $response = $this->callApi(fn() => $this->service->getClient()->crm()->associations()->v4()->basicApi()->getPage(
            'deals',
            $id,
            'contacts'
        ));
        
        if (!$response || !$response->getResults()) {
            return [];
        }
        
        return array_map(fn($association) => $association->getToObjectId(), $response->getResults());
    }

    /**
     * Create deal
     *
     * @param array $properties
     * @return DealEntity
     * @throws \App\Services\HubspotApi\Exceptions\ApiException
     */
    public function create(array $properties): DealEntity
    {
        $input = new SimplePublicObjectInput([
            'properties' => $properties
        ]);
        $object = $this->callApi(fn(Discovery $client) => $client->basicApi()->create($input));
        return DealEntity::fromSimplePublicObject($object);
    }

    /**
     * Update deal
     *
     * @param string $id
     * @param array $properties
     * @throws \App\Services\HubspotApi\Exceptions\ApiException
     */
    public function update(string $id, array $properties): void
    {
        $input = new SimplePublicObjectInput([
            'properties' => $properties
        ]);
        $this->callApi(fn(Discovery $client) => $client->basicApi()->update($id, $input));
    }

    /**
     * Associate company with deal
     *
     * @param string $id
     * @param string $company_id
     * @throws \App\Services\HubspotApi\Exceptions\ApiException
     */
    public function associateCompany(string $id, string $company_id): void
    {
        $associationSpec = new AssociationSpec([
            'association_category' => 'HUBSPOT_DEFINED',
            'association_type_id' => 5 // deal_to_company
        ]);
        
        
        $this->callApi(fn() => $this->service->getClient()->crm()->associations()->v4()->basicApi()->create(
            'deals',
            $id,
            'companies',
            $company_id,
            [$associationSpec]
        ));
    }

    /**
     * Associate contact with deal
     *
     * @param string $id
     * @param string $contact_id
     * @throws \App\Services\HubspotApi\Exceptions\ApiException
     */
    public function associateContact(string $id, string $contact_id): void
    {
        $associationSpec = new AssociationSpec([
            'association_category' => 'HUBSPOT_DEFINED',
            'association_type_id' => 3 // deal_to_contact
        ]);
        
        
        $this->callApi(fn() => $this->service->getClient()->crm()->associations()->v4()->basicApi()->create(
            'deals',
            $id,
            'contacts',
            $contact_id,
            [$associationSpec]
        ));
    }

    /**
     * Disassociate contact with deal
     *
     * @param string $id
     * @param string $contact_id
     * @throws \App\Services\HubspotApi\Exceptions\ApiException
     */
    public function disassociateContact(string $id, string $contact_id): void
    {
        $associationSpec = new AssociationSpec([
            'association_category' => 'HUBSPOT_DEFINED',
            'association_type_id' => 3 // deal_to_contact
        ]);
        
        
        $this->callApi(fn() => $this->service->getClient()->crm()->associations()->v4()->basicApi()->archive(
            'deals',
            $id,
            'contacts',
            $contact_id,
            [$associationSpec]
        ));
    }
}
