<?php

declare(strict_types=1);

namespace app\Services\Import\Resources;

use App\Classes\Func;
use App\Services\Import\Classes\{BaseResource, Load\Processor};
use Common\Models\{ProductCategory, ProductCategoryItem, ProductItem, ProductItemPrice, Unit};
use Ramsey\Uuid\Uuid;

/**
 * Class ProductResource
 *
 * @package App\Services\Import\Resources
 */
class ProductResource extends BaseResource
{
    /**
     * @var string Display name for resource
     */
    protected string $name = 'Products';

    /**
     * @var string App table name
     */
    protected string $table = 'productItems';

    /**
     * @var string Import table name
     */
    protected string $import_table = 'products';

    /**
     * @var string Name of CSV file within zip archive
     */
    protected string $csv_file_name = 'products.csv';

    /**
     * @var int Batch size to process at once
     */
    protected int $chunk_size = 200;

    /**
     * @var null|array
     */
    protected ?array $categories = null;

    /**
     * @var array
     */
    protected array $category_aliases = [];

    /**
     * @var null|array
     */
    protected ?array $units = null;

    /**
     * @inheritdoc
     */
    public function getColumns(): array
    {
        return [
            'id_user_defined' => [
                'label' => 'Id',
                'rules' => 'trim|required|max_length[100]'
            ],
            'name' => [
                'label' => 'Name',
                'rules' => 'trim|required|max_length[100]'
            ],
            'description' => [
                'label' => 'Description',
                'rules' => 'trim|nullable|optional|max_length[1000]'
            ],
            'price' => [
                'label' => 'Price',
                'rules' => 'trim|required|numeric|currency|abs'
            ],
            'unit' => [
                'label' => 'Unit',
                'rules' => 'trim|required|max_length[20]'
            ],
            'pricing_disclaimer' => [
                'label' => 'Pricing Disclaimer',
                'rules' => 'trim|nullable|optional|max_length[500]'
            ],
            'category' => [
                'label' => 'Category',
                'rules' => 'trim|nullable|optional|max_length[5000]'
            ]
        ];
    }

    /**
     * Select products with standard set of fields
     *
     * @param int $company_id
     * @return object
     */
    protected function select(int $company_id): object
    {
        return ProductItem::query()
            ->select('productItems.*')
            ->selectRaw('GROUP_CONCAT(HEX(productCategories.productCategoryID) SEPARATOR \',\') AS category_ids')
            ->leftJoin('productCategoriesItems', function ($join) {
                $join->on('productCategoriesItems.productItemID', '=', 'productItems.productItemID')
                    ->whereNull('productCategoriesItems.deletedAt');
            })
            ->leftJoin('productCategories', 'productCategoriesItems.productCategoryID', '=', 'productCategories.productCategoryID')
            ->ofCompany($company_id)
            ->where('productItems.status', ProductItem::STATUS_ACTIVE)
            ->groupBy('productItems.productItemID');
    }

    /**
     * @inheritdoc
     */
    public function load(array $ids, Processor $processor): void
    {
        $products = $this->select($processor->getCompany()['id'])
            ->whereIn('productItems.productItemID', $ids)
            ->with(['nonTieredPrice'])
            ->get()
            ->keyBy('productItemID')
            ->all();
        $this->setEagerLoadedData($products);
    }

    /**
     * @inheritdoc
     */
    public function search(object $row, Processor $processor): ?object
    {
        return $this->select($processor->getCompany()['id'])
            ->where('productItems.name', $row->name)
            ->first();
    }

    /**
     * Get unique category alias based on name
     *
     * @param string $name
     * @return string
     */
    protected function getCategoryAlias(string $name): string
    {
        $base_alias = Func::createAlias($name);
        $i = 0;
        do {
            $alias = $base_alias . ($i > 0 ? "-{$i}" : '');
            $i++;
        } while (isset($this->category_aliases[$alias]));
        $this->category_aliases[$alias] = true;
        return $alias;
    }

    /**
     * Get or create category and return id
     *
     * @param int $company_id
     * @param string $name
     * @return string
     * @throws \Exception
     */
    protected function getCategoryID(int $company_id, string $name): string
    {
        if ($this->categories === null) {
            $this->categories = [];
            ProductCategory::query()
                ->select('productCategoryID as id', 'name', 'alias', 'status')
                ->ofCompany($company_id)
                ->each(function ($category) {
                    // only track category if active so it won't be created again, we need to include archived items
                    // since aliases need to be unique per company (even with archived)
                    if ($category->status === ProductCategory::STATUS_ACTIVE) {
                        $this->categories[$category->name] = $category->id;
                    }
                    $this->category_aliases[$category->alias] = true;
                });
        }
        if (isset($this->categories[$name])) {
            return $this->categories[$name];
        }
        $id = Uuid::uuid4()->getBytes();
        ProductCategory::create([
            'productCategoryID' => $id,
            'ownerType' => ProductCategory::OWNER_COMPANY,
            'ownerID' => $company_id,
            'alias' => $this->getCategoryAlias($name),
            'name' => $name,
            'status' => ProductCategory::STATUS_ACTIVE
        ]);
        $this->categories[$name] = $id;
        return $id;
    }

    /**
     * Get or create unit and return id
     *
     * @param int $company_id
     * @param string $name
     * @return string
     * @throws \Exception
     */
    protected function getUnitID(int $company_id, string $name): string
    {
        if ($this->units === null) {
            $this->units = [];
            Unit::query()
                ->select('unitID as id', 'name')
                ->ofCompany($company_id)
                ->where('status', Unit::STATUS_ACTIVE)
                ->each(function ($unit) {
                    $this->units[$unit->name] = $unit->id;
                });
        }
        if (isset($this->units[$name])) {
            return $this->units[$name];
        }
        $id = Uuid::uuid4()->getBytes();
        Unit::create([
            'unitID' => $id,
            'ownerType' => Unit::OWNER_COMPANY,
            'ownerID' => $company_id,
            'name' => $name,
            'abbreviation' => $name,
            'status' => Unit::STATUS_ACTIVE
        ]);
        $this->units[$name] = $id;
        return $id;
    }

    /**
     * Get model data from row
     *
     * @param object $row
     * @param Processor $processor
     * @return array
     * @throws \Exception
     */
    public function data(object $row, Processor $processor): array
    {
        $company_id = $processor->getCompany()['id'];
        $data = [
            'product' => [
                'ownerType' => ProductItem::OWNER_COMPANY,
                'ownerID' => $company_id,
                'name' => $row->name,
                'description' => $row->description,
                'pricingDisclaimer' => $row->pricing_disclaimer,
                'unitID' => $this->getUnitID($company_id, $row->unit),
                'status' => ProductItem::STATUS_ACTIVE
            ],
            'price' => [
                'minCount' => 0,
                'maxCount' => null,
                'price' => $row->price,
                'status' => ProductItemPrice::STATUS_ACTIVE
            ]
        ];
        // Need to create multiple categories if there are multiple items in the array
        if ($row->category !== null) {
            $categories = explode(',', $row->category);
            foreach ($categories as $category) {
                $data['category'][] = $this->getCategoryID($company_id, trim($category));
            }
        }
        return $data;
    }

    /**
     * @inheritdoc
     */
    public function create(object $row, Processor $processor): array
    {
        $data = $this->data($row, $processor);

        $data['product']['productItemID'] = Uuid::uuid4()->getBytes();
        $product = (new ProductItem())->fill($data['product']);
        $product->save();

        $data['price']['productItemPriceID'] = Uuid::uuid4()->getBytes();
        $price = (new ProductItemPrice())->fill($data['price']);
        $price->item()->associate($product);
        $price->save();

        // loop through categories passed into the data set and associate with the product
        $category_ids = null;
        if (isset($data['category'])) {
            $category_ids = $data['category'];
            foreach ($category_ids as $category_id) {
                $category_item = (new ProductCategoryItem())->forceFill([
                    'productCategoryItemID' => Uuid::uuid4()->getBytes(),
                    'productCategoryID' => $category_id
                ]);
                $category_item->item()->associate($product);
                $category_item->save();
            }
        }

        return [
            'id_fx' => $product->getKey(),
            'category_id_fx' => is_array($category_ids) ? ($category_ids[0] ?? null) : null,
            'price_id_fx' => $price->getKey(),
            'unit_id_fx' => $data['product']['unitID']
        ];
    }

    /**
     * @inheritdoc
     */
    public function update(object $row, object $existing_row, Processor $processor): array
    {
        $data = $this->data($row, $processor);

        // pluck out category ids so it doesn't get saved with the model and cause issues creating history records
        $existing_category_ids = $existing_row->category_ids;
        unset($existing_row->category_ids);

        $existing_row->fill($data['product'])->save();
        // if we find a single price for the product (they are not using tiered pricing), then we can update. otherwise,
        // we leave the price alone since we cannot know which one to update
        $price_id = null;
        if ($existing_row->nonTieredPrice !== null & $existing_row->pricingType !== ProductItem::PRICING_TYPE_COMPONENT) {
            $existing_row->nonTieredPrice->fill($data['price'])->save();
            $price_id = $existing_row->nonTieredPrice->getKey();
        }

        // loop through categories passed into the data set and associate with the product if they aren't already
        $category_ids = null;
        if (isset($data['category'])) {
            $category_ids = $data['category'];

            if ($existing_category_ids !== null) {
                $existing_category_ids = explode(',', $existing_category_ids);
                $existing_category_ids = array_map(fn(string $id): string => Uuid::fromString($id)->getBytes(), $existing_category_ids);

                foreach ($category_ids as $category_id) {
                    $assign_category = true;
                    $assign_category = !in_array($category_id, $existing_category_ids);

                    if ($assign_category) {
                        $category_item = (new ProductCategoryItem())->forceFill([
                            'productCategoryItemID' => Uuid::uuid4()->getBytes(),
                            'productCategoryID' => $category_id
                        ]);
                        $category_item->item()->associate($existing_row);
                        $category_item->save();
                    }
                }
            }
        }

        return [
            'id_fx' => $existing_row->getKey(),
            'category_id_fx' => is_array($category_ids) ? ($category_ids[0] ?? null) : null,
            'price_id_fx' => $price_id,
            'unit_id_fx' => $data['product']['unitID']
        ];
    }
}
