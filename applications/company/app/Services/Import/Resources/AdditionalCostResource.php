<?php

declare(strict_types=1);

namespace App\Services\Import\Resources;

use App\Services\Import\Classes\{BaseResource, Load\Processor};
use Common\Models\{AdditionalCost, Unit};
use Ramsey\Uuid\Uuid;
use App\Resources\AdditionalCostResource AS ApiAdditionalCostResource;
use App\Classes\Acl;

/**
 * Class AdditionalCostResource
 *
 * @package App\Services\Import\Resources
 */
class AdditionalCostResource extends BaseResource
{
    /**
     * @var string Display name for resource
     */
    protected string $name = 'Additional Costs';

    /**
     * @var string App table name
     */
    protected string $table = 'additionalCosts';

    /**
     * @var string Import table name
     */
    protected string $import_table = 'additional_costs';

    /**
     * @var string Name of CSV file within zip archive
     */
    protected string $csv_file_name = 'additional_costs.csv';

    /**
     * @var int Batch size to process at once
     */
    protected int $chunk_size = 200;

    /**
     * @var null|array
     */
    protected ?array $categories = null;

    /**
     * @var null|array
     */
    protected ?array $units = null;

    /**
     * @inheritdoc
     */
    public function getColumns(): array
    {
        return [
            'id_user_defined' => [
                'label' => 'Id',
                'rules' => 'trim|required|max_length[100]'
            ],
            'name' => [
                'label' => 'Name',
                'rules' => 'trim|required|max_length[100]'
            ],
            'cost' => [
                'label' => 'Cost',
                'rules' => 'trim|required|numeric|currency|abs'
            ],
            'markup' => [
                'label' => 'Markup',
                'rules' => 'trim|nullable|optional|numeric|currency|abs'
            ],
            'unit' => [
                'label' => 'Unit',
                'rules' => 'trim|required|max_length[20]'
            ]
        ];
    }

    /**
     * Select additional costs with standard set of fields
     *
     * @param int $company_id
     * @return object
     */
    protected function select(int $company_id): object
    {
        return AdditionalCost::query()
            ->select('additionalCosts.*')
            ->ofCompany($company_id)
            ->where('additionalCosts.status', AdditionalCost::STATUS_ACTIVE);
    }

    /**
     * @inheritdoc
     */
    public function load(array $ids, Processor $processor): void
    {
        $additional_costs = $this->select($processor->getCompany()['id'])
            ->whereIn('additionalCosts.additionalCostID', $ids)
            ->get()
            ->keyBy('additionalCostID')
            ->all();
        $this->setEagerLoadedData($additional_costs);
    }

    /**
     * @inheritdoc
     */
    public function search(object $row, Processor $processor): ?object
    {
        return $this->select($processor->getCompany()['id'])
            ->where('additionalCosts.name', $row->name)
            ->first();
    }

    /**
     * Get or create unit and return id
     *
     * @param int $company_id
     * @param string $name
     * @return string
     * @throws \Exception
     */
    protected function getUnitID(int $company_id, string $name): string
    {
        if ($this->units === null) {
            $this->units = [];
            Unit::query()
                ->select('unitID as id', 'name')
                ->ofCompany($company_id)
                ->where('status', Unit::STATUS_ACTIVE)
                ->each(function ($unit) {
                    $this->units[$unit->name] = $unit->id;
                });
        }
        if (isset($this->units[$name])) {
            return $this->units[$name];
        }
        $id = Uuid::uuid4()->getBytes();
        Unit::create([
            'unitID' => $id,
            'ownerType' => Unit::OWNER_COMPANY,
            'ownerID' => $company_id,
            'name' => $name,
            'abbreviation' => $name,
            'status' => Unit::STATUS_ACTIVE
        ]);
        $this->units[$name] = $id;
        return $id;
    }

    /**
     * Get model data from row
     *
     * @param object $row
     * @param Processor $processor
     * @return array
     * @throws \Exception
     */
    public function data(object $row, Processor $processor): array
    {
        $company_id = $processor->getCompany()['id'];
        $unit_price = $row->cost;
        if ($row->markup !== null) {
            $unit_price = $unit_price + ($unit_price * ($row->markup/100));
        }
        $data = [
            'additional_cost' => [
                'ownerType' => AdditionalCost::OWNER_COMPANY,
                'ownerID' => $company_id,
                'name' => $row->name,
                'cost' => $row->cost,
                'markup' => $row->markup,
                'unitPrice' => $unit_price,
                'unitID' => $this->getUnitID($company_id, $row->unit),
                'status' => AdditionalCost::STATUS_ACTIVE
            ]
        ];
        return $data;
    }

    /**
     * @inheritdoc
     */
    public function create(object $row, Processor $processor): array
    {
        $data = $this->data($row, $processor);

        $data['additional_cost']['additionalCostID'] = Uuid::uuid4()->getBytes();
        $additional_cost = (new AdditionalCost())->fill($data['additional_cost']);
        $additional_cost->save();

        return [
            'id_fx' => $additional_cost->getKey(),
            'unit_id_fx' => $data['additional_cost']['unitID']
        ];
    }

    /**
     * @inheritdoc
     */
    public function update(object $row, object $existing_row, Processor $processor): array
    {
        $data = $this->data($row, $processor);

        $existing_row->fill($data['additional_cost'])->save();

        ApiAdditionalCostResource::make(Acl::make())->updateProductsWithAdditionalCostID($existing_row->additionalCostID);

        return [
            'id_fx' => $existing_row->getKey(),
            'unit_id_fx' => $data['additional_cost']['unitID']
        ];
    }
}
