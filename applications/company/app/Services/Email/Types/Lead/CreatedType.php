<?php

namespace App\Services\Email\Types\Lead;

use App\Classes\Template;
use App\Services\CompanySettingService;
use App\Services\Email\Exceptions\TypeException;
use App\Services\Email\Traits\Type\NotificationTrait;
use App\Services\Email\Types\LeadType;
use Common\Models\EmailTemplate;
use Common\Models\Lead;
use Ramsey\Uuid\Uuid;

/**
 * Class CreatedType
 *
 * Email sent when lead is created (user has control if this email is sent out)
 *
 * @package App\Services\Email\Types\Lead
 */
class CreatedType extends LeadType
{
    use NotificationTrait;

    /**
     * Build email
     *
     * @param array $payload
     * @return \App\Services\Email\Classes\Message
     * @throws TypeException
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     * @throws \Core\Exceptions\AppException
     */
    public function build(array $payload)
    {
        $this->findNotification($payload);

        $lead = Lead::where('leadUUID', $this->getNotificationItemID())->first();
        if ($lead === null) {
            throw new TypeException('Unable to find lead');
        }

        $this->setup($lead);

        $message = $this->getMessage();
        $message->itemID(Uuid::fromBytes($lead->leadUUID));
        $message->from($this->getCompanyFromAddress());
        $message->replyTo($this->getCompanyReplyAddress());

        $setting_service = new CompanySettingService($this->company->companyID);
        $email_greeting = $setting_service->get('email_greeting', null);

        $email_template_data = EmailTemplate::where('ownerID', $this->company->companyID)
            ->where('type', EmailTemplate::TYPE_NEW_LEAD)
            ->first();

        $subject = Template::replace($email_template_data->subject, [
            'company_name' => $this->company->name,
        ]);
        $message->subject($subject);
        $content = $email_template_data->content;

        $this->template->content = Template::fetch('emails.html.lead.created', [
            'greeting' => $email_greeting ?: 'Hello',
            'first_name' => $lead->firstName,
            'content' => $content,
            'company_logo' => $this->getCompanyLogoUrl()
        ]);
        $message->html($this->template->render());
        $message->textFromHtml($content);

        $this->saveNotificationDistribution($message);

        return $message;
    }
}
