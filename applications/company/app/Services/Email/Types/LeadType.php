<?php

namespace App\Services\Email\Types;

use App\Classes\Acl;
use App\Classes\Template;
use App\Resources\CompanyResource;
use App\Services\CompanySettingService;
use App\Services\Email\Addresses\CompanyFromAddress;
use App\Services\Email\Addresses\CompanyReplyAddress;
use App\Services\Email\Addresses\LeadAddress;
use App\Services\Email\Classes\Type;
use App\Services\Email\Traits\Type\CompanyTrait;
use App\Services\Email\Traits\Type\DomainTrait;
use Common\Models\Lead;
use Core\Exceptions\AppException;

/**
 * Class LeadType
 *
 * @package App\Services\Email\Types
 */
abstract class LeadType extends Type
{
    use CompanyTrait;
    use DomainTrait;

    /**
     * @var null|Lead
     */
    protected $lead = null;

    /**
     * @var Template
     */
    protected $template;

    /**
     * Load domain based on company id
     *
     * @return \Common\Models\Domain
     * @throws AppException
     * @throws \App\Services\Email\Exceptions\TypeException
     */
    protected function loadDomain()
    {
        return $this->getDomainService()->findByCompanyID($this->getCompany()->getKey());
    }

    /**
     * Set lead
     *
     * @param Lead $lead
     */
    protected function setLead(Lead $lead)
    {
        $this->lead = $lead;
    }

    /**
     * Get lead
     *
     * @return Lead
     * @throws AppException
     */
    protected function getLead()
    {
        if ($this->lead === null) {
            throw new AppException('No lead defined');
        }
        return $this->lead;
    }

    /**
     * Get company from address
     *
     * @return CompanyFromAddress
     * @throws \App\Services\Email\Exceptions\TypeException
     */
    protected function getCompanyFromAddress()
    {
        return (new CompanyFromAddress)->fromModel($this->getCompany());
    }

    /**
     * Get company reply address
     *
     * @return CompanyReplyAddress
     * @throws \App\Services\Email\Exceptions\TypeException
     */
    protected function getCompanyReplyAddress()
    {
        return (new CompanyReplyAddress)->fromModel($this->getCompany());
    }

    /**
     * Get company logo (if available)
     *
     * @param string $variant
     * @return string|null
     * @throws AppException
     * @throws \Core\Components\Resource\Exceptions\MediaTypeNotFoundException
     */
    protected function getCompanyLogoUrl($variant = 'email_thumbnail')
    {
        $company = $this->getCompany();
        $logo_url = null;
        if ($company->logoFileID !== null) {
            $company_id = $company->getKey();
            $company_resource = CompanyResource::make(Acl::make());
            $company_resource->setMediaCompanyID($company_id);
            $logo_url = $company_resource->getMedia()->get('logo')->getVariant($variant)->getUrl($company_id)->csm()->build();
        }
        return $logo_url;
    }

    /**
     * Setup lead email
     *
     * @param Lead $lead
     * @throws AppException
     */
    protected function setup(Lead $lead)
    {
        $this->setCompany($lead->company);
        $this->setLead($lead);

        $message = $this->getMessage();
        $message->to(LeadAddress::make()->fromModel($lead));

        $setting_service = new CompanySettingService($this->company->companyID);
        $footer_template = $setting_service->get('email_footer_template', null);

        $this->template = new Template('emails.html.layouts.lead');

        $address = $this->company->address2 ? $this->company->address . ', ' . $this->company->address2 : $this->company->address;
        $address = $address . ', ' . $this->company->city . ', ' . $this->company->state . ', ' . $this->company->zip;
        $name_address = '<span class="highlight">' . $this->company->name . '</span> | ' . $address;

        if ($footer_template !== null) {
            $name_address = Template::replace($footer_template, [
                'company_name' => $this->company->name,
                'address' => $this->company->address,
                'address2' => $this->company->address2,
                'city' => $this->company->city,
                'state' => $this->company->state,
                'zip' => $this->company->zip
            ]);
        }

        $vars = [
            'company' => [
                'id' => $this->company->companyID,
                'color' => $this->company->color,
                'name_address' => $name_address,
                'phones' => [],
                'website' => $this->company->website,
            ],
            'unsubscribe_url' => "mailto:{$this->company->emailReply}?subject=Unsubscribe"
        ];
        if ($this->company->phones !== null) {
            foreach ($this->company->phones as $phone) {
                $vars['company']['phones'][] = [
                    'number' => $phone->phoneNumber,
                    'description' => $phone->phoneDescription
                ];
            }
        }
        $this->template->setVars($vars);
    }
}
