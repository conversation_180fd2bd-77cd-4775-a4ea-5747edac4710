<?php

namespace App\Services\Email\Types\User;

use App\Classes\Acl;
use App\Classes\Template;
use App\Resources\LeadResource;
use App\Services\CompanySettingService;
use App\Services\Email\Addresses\ExternalAddress;
use App\Services\Email\Exceptions\TypeException;
use App\Services\Email\Traits\Type\NotificationTrait;
use App\Services\Email\Types\UserType;
use Common\Models\Company;
use Common\Models\Lead;
use Common\Models\User;
use Core\Components\Resource\Classes\Scope;
use Core\Components\Resource\Exceptions\EntityNotFoundException;
use Ramsey\Uuid\Uuid;

/**
 * Class LeadAssignedType
 *
 * @package App\Services\Email\Types\User
 */
class LeadAssignedType extends UserType
{
    use NotificationTrait;

    /**
     * Build email
     *
     * @param array $payload
     * @return \App\Services\Email\Classes\Message
     * @throws TypeException
     * @throws \Core\Exceptions\AppException
     */
    public function build(array $payload)
    {
        $this->findNotification($payload);

        $lead_item_lookup = Lead::query()->where('leadUUID', $this->getNotificationItemID())
            ->first(['leadID']);
        if ($lead_item_lookup === null) {
            throw new TypeException('Unable to find lead item id: %s', $this->getNotificationItemID());
        }

        try {
            $lead_scope = Scope::make()
                ->fields([
                    'id', 'lead_uuid', 'company_id', 'first_name', 'last_name', 'business_name', 'priority',
                    'priority_name', 'marketing_source', 'assigned_to_user_id', 'project_type_name',
                    'updated_by_user_name', 'phone', 'email', 'address', 'status_name', 'origin', 'origin_name', 'notes'
                ]);
            $lead_item = LeadResource::make(Acl::make())
                ->entity($lead_item_lookup->leadID)
                ->scope($lead_scope)
                ->run();
        } catch (EntityNotFoundException $e) {
            throw new TypeException('Unable to find lead item: %s', $lead_item_lookup->leadID);
        }

        $company = Company::find($lead_item->get('company_id'));
        if ($company === null) {
            throw new TypeException('Unable to find company');
        }

        $user = User::find($lead_item->get('assigned_to_user_id'));
        if ($user === null) {
            throw new TypeException('Unable to find user');
        }

        $this->setup($company, [$user]);

        $message = $this->getMessage();
        $message->itemID(Uuid::fromString($lead_item['lead_uuid']));

        $message->subject("A Lead Has Been Assigned To You:  {$lead_item['first_name']} {$lead_item['last_name']}");

        $company_settings = new CompanySettingService($company->companyID);
        $raw = $company_settings->get('leads_additional_email_recipients', '[]');

        // Decode JSON if it’s a string, or cast to array otherwise
        $emails = is_string($raw) ? (json_decode($raw, true) ?: []) : (array) $raw;

        foreach ($emails as $email) {
            // Trim quotes or whitespace
            $email = trim($email, "\"' \t\n\r\0\x0B");

            // Skip invalid or empty emails
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                error_log("Skipped invalid email: {$email}");
                continue;
            }

            $address = new ExternalAddress();
            $address->setAddress($email);
            $message->bcc($address);
        }

        if ($lead_item['priority']) {
            $priority_names = LeadResource::getPriorityNames();
            $lead_item['priority'] = $priority_names[$lead_item['priority']];
        }

        $business_display = '';
        if ($lead_item['business_name'] !== null) {
            $business_display = ' (' . $lead_item['business_name'] . ')';
        }

        $domain = $this->getDomain();

        $template_vars = [
            'lead_name' => $lead_item['first_name'] . ' ' . $lead_item['last_name'] . $business_display,
            'assigned_by_user' => $lead_item['updated_by_user_name'],
            'project_type' => $lead_item['project_type_name'],
            'priority' => $lead_item['priority'],
            'marketing_source' => $lead_item['marketing_source'],
            'brand_color' => "#{$domain['brand']['mailColor']}",
            'link' => $this->newUrlBuilder()->path("leads/details/{$lead_item['lead_uuid']}")->build(),
            'unsubscribe_link' => $this->newUrlBuilder()->path("user/profile")->build(),
            'phone'          => $lead_item['phone']         ?? '',
            'email'          => $lead_item['email']         ?? '',
            'address'        => $lead_item['address']       ?? '',
            'status'         => $lead_item['status_name']   ?? '',
            'origin'         => $lead_item['origin_name']   ?? '',
            'business_name'  => $lead_item['business_name'] ?? '',
            'unsubscribed'   => $lead_item['unsubscribed']  ? 'Yes' : 'No',
            'notes'          => $lead_item['notes']         ?? '',
        ];

        $this->template->content = Template::fetch('emails.html.user.lead-assigned', $template_vars);
        $message->html($this->template->render(), Template::fetch('emails.text.user.lead-assigned', $template_vars));

        $this->saveNotificationDistribution($message);
        return $message;
    }
}
