<?php

namespace App\Services\Email\Interfaces;

use Common\Models\EmailMessageAddress;
use Ramsey\Uuid\UuidInterface;

/**
 * Interface AddressInterface
 *
 * @package App\Services\Email\Interfaces
 */
interface AddressInterface
{
    const ADDRESS_TYPE_FROM = 1;
    const ADDRESS_TYPE_REPLY_TO = 2;
    const ADDRESS_TYPE_TO = 3;
    const ADDRESS_TYPE_CC = 4;
    const ADDRESS_TYPE_BCC = 5;

    const TYPE_EXTERNAL = 0;
    const TYPE_COMPANY_FROM = 26;
    const TYPE_COMPANY_REPLY = 27;
    const TYPE_USER = 28;
    const TYPE_CUSTOMER = 29;
    const TYPE_PROJECT_CONTACT = 30;
    const TYPE_BRAND_FROM = 31;
    const TYPE_BRAND_REPLY = 32;
    const TYPE_REGISTRATION = 33;
    const TYPE_LEAD = 49;

    /**
     * Get domain service instance
     *
     * @return \App\Services\DomainService
     */
    public function getDomainService();

    /**
     * Fill in instance from email message address model
     *
     * @param EmailMessageAddress $address
     * @return $this
     */
    public function hydrate(EmailMessageAddress $address);

    /**
     * Create clone of instance without id info
     *
     * @return AddressInterface
     */
    public function replicate();

    /**
     * Set id
     *
     * @param UuidInterface $id
     * @return $this
     */
    public function setID(UuidInterface $id);

    /**
     * Get id
     *
     * @return \Ramsey\Uuid\UuidInterface
     */
    public function getID();

    /**
     * Set address
     *
     * @param string $address
     * @return $this
     */
    public function setAddress($address);

    /**
     * Get address
     *
     * @return string
     */
    public function getAddress();

    /**
     * Set name
     *
     * @param string $name
     * @return $this
     */
    public function setName($name);

    /**
     * Get name
     *
     * @return string|null
     */
    public function getName();

    /**
     * Set type
     *
     * @param int $type
     * @return $this
     */
    public function setType($type);

    /**
     * Get type
     *
     * @return int
     */
    public function getType();

    /**
     * Set item id
     *
     * @param UuidInterface|null $id
     * @return $this
     */
    public function setItemID(UuidInterface $id = null);

    /**
     * Get item id
     *
     * @return \Ramsey\Uuid\UuidInterface
     */
    public function getItemID();
}
