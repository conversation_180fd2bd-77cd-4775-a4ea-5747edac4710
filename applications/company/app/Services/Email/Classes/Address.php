<?php

namespace App\Services\Email\Classes;

use App\Services\DomainService;
use App\Services\Email\Addresses;
use App\Services\Email\Exceptions\AddressException;
use App\Services\Email\Interfaces\AddressInterface;
use Common\Models\EmailMessageAddress;
use Core\StaticAccessors\App;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidInterface;

/**
 * Class Address
 *
 * Base class for all addresses
 *
 * @package App\Services\Email\Classes
 */
abstract class Address implements AddressInterface
{
    /**
     * @var array Map of type ids to their respective classes
     */
    protected static $type_map = [
        AddressInterface::TYPE_EXTERNAL => Addresses\ExternalAddress::class,
        AddressInterface::TYPE_BRAND_FROM => Addresses\BrandFromAddress::class,
        AddressInterface::TYPE_BRAND_REPLY => Addresses\BrandReplyAddress::class,
        AddressInterface::TYPE_COMPANY_FROM => Addresses\CompanyFromAddress::class,
        AddressInterface::TYPE_COMPANY_REPLY => Addresses\CompanyReplyAddress::class,
        AddressInterface::TYPE_USER => Addresses\UserAddress::class,
        AddressInterface::TYPE_CUSTOMER => Addresses\CustomerAddress::class,
        AddressInterface::TYPE_PROJECT_CONTACT => Addresses\ProjectContactAddress::class,
        AddressInterface::TYPE_REGISTRATION => Addresses\RegistrationAddress::class,
        AddressInterface::TYPE_LEAD => Addresses\LeadAddress::class
    ];

    /**
     * @var null|DomainService
     */
    protected static $domain_service = null;

    /**
     * @var null|UuidInterface
     */
    protected $id = null;

    /**
     * @var string
     */
    protected $address;

    /**
     * @var null|string
     */
    protected $name;

    /**
     * @var int
     */
    protected $type;

    /**
     * @var null|UuidInterface
     */
    protected $item_id;

    /**
     * Create new instance, used for chaining
     *
     * @return Address
     */
    public static function make()
    {
        return new static;
    }

    /**
     * Get proper address instance type based on type defined in model
     *
     * If class is found based on type, the model data is passed into hydrate the instance with it's data
     *
     * @param EmailMessageAddress $address
     * @return AddressInterface
     * @throws AddressException
     */
    public static function makeFromModel(EmailMessageAddress $address)
    {
        if (!isset(static::$type_map[$address->type])) {
            throw new AddressException('Unable to find address class from type: %s', $address->type);
        }
        return (new static::$type_map[$address->type]())->hydrate($address);
    }

    /**
     * Handle cloning of address
     *
     * ID is reset so it won't be shared and item id uuid instance is cloned
     */
    public function __clone()
    {
        $this->id = null;
        if ($this->item_id !== null) {
            $this->item_id = clone $this->item_id;
        }
    }

    /**
     * Create clone of instance without id info
     *
     * @return AddressInterface
     */
    public function replicate()
    {
        return clone $this;
    }

    /**
     * Get domain service instance
     *
     * @return DomainService
     */
    public function getDomainService()
    {
        if (static::$domain_service === null) {
            static::$domain_service = App::get(DomainService::class);
        }
        return static::$domain_service;
    }

    /**
     * Fill in instance from email message address model
     *
     * @param EmailMessageAddress $address
     * @return $this
     */
    public function hydrate(EmailMessageAddress $address)
    {
        $this->setID($address->getUuidKey());
        $this->setType($address->type);
        $this->setItemID($address->getUuid('itemID'));
        $this->setAddress($address->address);
        $this->setName($address->name);

        return $this;
    }

    /**
     * Set id
     *
     * @param UuidInterface $id
     * @return $this
     */
    public function setID(UuidInterface $id)
    {
        $this->id = $id;
        return $this;
    }

    /**
     * Get id
     *
     * If no id is defined, one will be created and returned
     *
     * @return UuidInterface
     */
    public function getID()
    {
        if ($this->id === null) {
            $this->id = Uuid::uuid4();
        }
        return $this->id;
    }

    /**
     * Set address
     *
     * @param string $address
     * @return $this
     */
    public function setAddress($address)
    {
        $this->address = $address;
        return $this;
    }

    /**
     * Get address
     *
     * @return string
     */
    public function getAddress()
    {
        return $this->address;
    }

    /**
     * Set name
     *
     * @param string $name
     * @return $this
     */
    public function setName($name)
    {
        $this->name = $name;
        return $this;
    }

    /**
     * Get name
     *
     * @return string|null
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Set type
     *
     * @param int $type
     * @return $this
     */
    public function setType($type)
    {
        $this->type = $type;
        return $this;
    }

    /**
     * Get type
     *
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * Set item id
     *
     * @param UuidInterface|null $id
     * @return $this
     */
    public function setItemID(UuidInterface $id = null)
    {
        $this->item_id = $id;
        return $this;
    }

    /**
     * Get item id
     *
     * @return UuidInterface|null
     */
    public function getItemID()
    {
        return $this->item_id;
    }
}
