<?php

namespace App\Services\Email\Addresses;

use App\Services\Email\Classes\Address;
use Common\Models\Lead;
use Ramsey\Uuid\Uuid;

/**
 * Class LeadAddress
 *
 * @package App\Services\Email\Addresses
 */
class LeadAddress extends Address
{
    /**
     * Fill in instance from lead model
     *
     * @param Lead $lead
     * @return $this
     *
     * @todo maybe use customer business name
     */
    public function fromModel(Lead $lead)
    {
        $this->setAddress($lead->email);
        $this->setName($lead->firstName . ' ' . $lead->lastName);
        $this->setType(static::TYPE_LEAD);
        $this->setItemID(Uuid::fromBytes($lead->leadUUID));

        return $this;
    }
}
