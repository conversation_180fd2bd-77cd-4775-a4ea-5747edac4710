<?php

declare(strict_types=1);

namespace App\NotificationJobs\Lead;

use App\Attributes\JobAttribute;
use App\Services\Email\Types\Lead\CreatedType;
use App\Traits\Job\NotificationTrait;
use Common\Models\Lead;
use Common\Models\Notification;
use Core\Components\Queue\Classes\Job;
use Core\Components\Queue\Exceptions\JobFailedException;
use Ramsey\Uuid\Uuid;

/**
 * Class CreatedNotificationJob
 *
 * @package App\NotificationJobs\Lead
 */
#[JobAttribute(type: 62)]
class CreatedNotificationJob extends Job
{
    use NotificationTrait;

    /**
     * CreatedNotificationJob constructor
     *
     * @param int $lead_id
     */
    public function __construct(protected int $lead_id)
    {}

    /**
     * Handle lead created notification
     *
     * Send email to lead
     *
     * @return void
     * @throws JobFailedException
     * @throws \Core\Exceptions\AppException
     */
    public function handle(): void
    {
        if (($lead = Lead::find($this->lead_id)) === null) {
            throw new JobFailedException('Unable to find lead: %d', $this->lead_id);
        }

        // send email if possible
        if ($lead->canEmail()) {
            $notification = $this->createNotification(Notification::TYPE_LEAD_CREATED, Uuid::fromBytes($lead->leadUUID));
            CreatedType::send([
                'notification_id' => $notification->getUuidKey()->toString()
            ]);
        }

        // send other types of notifications (sms, push, etc.) here
    }
}
