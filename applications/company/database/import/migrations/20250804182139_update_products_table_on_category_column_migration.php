<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use Illuminate\Database\Schema\Blueprint;

final class UpdateProductsTableOnCategoryColumnMigration extends Migration
{
    /**
     * Up Method.
     *
     * Change the category column from varchar(200) to TEXT
     */
    public function up(): void
    {
        $this->updateTable('products')
            ->columns(function (Blueprint $table) {
                $table->text('category')->nullable()->change();
            })
            ->useHistory(false)
            ->alter();
    }

    /**
     * Down Method.
     *
     * Revert the category column back to varchar(200)
     */
    public function down(): void
    {
        $this->updateTable('products')
            ->columns(function (Blueprint $table) {
                $table->string('category', 200)->nullable()->change();
            })
            ->useHistory(false)
            ->alter();
    }
}
