<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;
use App\Classes\DB\Schema;

class AddLeadFormRequestsMigration extends Migration
{
    public function up()
    {
        $this->newTable('leadFormRequests')
            ->primaryKey('leadFormRequestID')
            ->columns(function (Blueprint $table) {
                $table->enum('ipAddressType', ['IPv4', 'IPv6']);
                $table->enum('method', ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS']);
                $table->string('path', 500);
                $table->text('requestHeaders')->nullable();
                $table->longText('requestData')->nullable();
                $table->text('responseHeaders')->nullable();
                $table->longText('responseData')->nullable();
                $table->unsignedSmallInteger('statusCode');
                $table->unsignedBigInteger('time');
                $table->dateTime('createdAt', 6);
            })
            ->column('leadFormID', Table::COLUMN_TYPE_UUID, [
                'after' => 'leadFormRequestID',
                'nullable' => true
            ])
            ->column('ipAddress', Table::COLUMN_TYPE_BINARY, [
                'length' => 16,
                'after' => 'ipAddressType'
            ])
            ->indexes(function (Blueprint $table) {
                $table->index('statusCode', 'status_code_index');
                $table->index('createdAt', 'created_at_index');
            })
            ->noTimestamps()
            ->useHistory(false)
            ->create();

        Schema::uuidColumn('leadFormRequests', 'apiKey', ['after' => 'leadFormRequestID', 'nullable' => true, 'unique' => true]);

        $this->updateTable('leadFormRequests')
            ->indexes(function (Blueprint $table) {
                $table->index('apiKey', 'api_key_id_index');
            });
    }

    public function down()
    {
        $this->dropTables([
            'leadFormRequests'
        ]);
    }
}

