<?php
declare(strict_types=1);

use App\Classes\DB\Migration;
use App\Classes\DB\Schema\Table;
use Illuminate\Database\Schema\Blueprint;
use App\Classes\DB\Schema;

final class AddNewSettingsAndFieldsToLeadsForm extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        $this->updateTable('leadForms')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('isSendNotifications')->after('defaultAssignedToUserID')->default(0);
                $table->string('googleTagID')->after('isSendNotifications')->nullable();
                $table->dateTime('apiKeyCreatedAt', 6)->after('apiKey')->nullable();

                Schema::uuidColumn('leadForms', 'apiKey', ['after' => 'token', 'nullable' => true, 'unique' => true]);
            })
            ->useHistory(false)
            ->alter();

        $this->updateTable('leadFormFields')
            ->columns(function (Blueprint $table) {
                $table->string('instruction')->after('label')->nullable(); // Work like a second label/subtitle
            })
            ->useHistory(false)
            ->alter();

        $this->updateTable('leads')
            ->columns(function (Blueprint $table) {
                $table->unsignedTinyInteger('isUnsubscribed')->after('origin')->default(0);
                $table->unsignedTinyInteger('isSentNotifications')->after('assignedToUserID')->default(0);
            })
            ->useHistory()
            ->alter();

        // Lead files
        $this->newTable('leadFiles')
            ->primaryKey('leadFileID')
            ->columns(function (Blueprint $table) {
                $table->unsignedInteger('leadID');
                $table->string('name', 250);
            })
            ->indexes(function (Blueprint $table) {
                $table->index('leadID', 'lead_id_index');
            })
            ->timestamps(true, true)
            ->useHistory(false)
            ->create();

        $this->schema->table('leadFiles', function (Blueprint $table) {
            Schema::uuidColumn('leadFiles', 'fileID', ['nullable' => false, 'after' => 'name']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        $this->updateTable('leadForms')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('googleTagID');
                $table->dropColumn('apiKey');
                $table->dropColumn('apiKeyCreatedAt');
                $table->dropColumn('isSendNotifications');
            })
            ->useHistory(false)
            ->alter();

        $this->updateTable('leadFormFields')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('instruction');
            })
            ->useHistory(false)
            ->alter();

        $this->updateTable('leads')
            ->columns(function (Blueprint $table) {
                $table->dropColumn('isUnsubscribed');
                $table->dropColumn('isSentNotifications');
            })
            ->useHistory()
            ->alter();

        $this->dropTables(['leadFiles'], false);
    }
}
