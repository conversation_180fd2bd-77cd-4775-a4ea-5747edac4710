.reveal-overlay {
    backdrop-filter: blur(0.5rem);
    padding: 24px;
}

@media (max-width: 600px) { /* Assuming '<small' refers to small screens */
    .reveal-overlay {
        padding: 0;
    }
}

.s-modal {
    display: flex;
    align-self: center;
    flex-direction: column;
    max-height: calc(70dvh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
    min-height: unset;
    height: unset;
    padding: 0;
    border-radius: 12px;
    border: none;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    outline: none;
    float: left;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%);
}

@media (max-height: 40rem) {
    .s-modal {
        max-height: 100% !important;
    }
}

@media (max-width: 600px) {
    .s-modal {
        min-height: 100%;
        height: 100%;
        left: unset !important;
        top: unset !important;
        transform: unset;
        border-radius: 0;
        box-shadow: none;
    }
}

.s-modal.tiny {
    width: 528px;
}

@media (max-width: 600px) {
    .s-modal.tiny {
        width: 100%;
        border-radius: 0;
        box-shadow: none;
        padding: 0;
    }
}

.s-modal.large {
    width: 928px;
}

@media (max-width: 976px) {
    .s-modal.large {
        width: 100%;
    }
}

@media (max-width: 600px) {
    .s-modal.large {
        border-radius: 0;
        box-shadow: none;
    }
}

.s-modal .i-m-messages .i-fm-message {
    margin: 8px 8px 0;
}

.s-modal .i-m-header {
    display: flex;
    padding: 12px;
    border-bottom: 1px solid #ddd; /* Approximate for base.$color-grey-light-4 */
}

.s-modal .i-mh-text {
    flex: 1;
    align-self: center;
    font-size: 24px;
    line-height: 32px;
    color: #333; /* Approximate for base.$color-grey-dark-4 */
    padding-left: 4px;
    margin-bottom: 0;
}

@media (max-width: 600px) {
    .s-modal .i-mh-text {
        font-size: 20px;
        line-height: 32px;
    }
}

.s-modal .i-mh-close {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 32px;
    height: 32px;
    border-radius: 32px;
}

.s-modal .i-mh-close:hover {
    border: 1px solid #ccc; /* Approximate for base.$color-grey-light-3 */
}



.s-modal .i-m-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 24px;
    height: 100%;
    overflow: scroll;
}

.s-modal .i-m-content p {
    margin: 0;
}

@media (max-width: 600px) {
    .s-modal .i-m-content {
        padding: 24px 16px;
    }
}

.s-modal .i-m-footer {
    display: flex;
    justify-content: flex-end;
    padding: 12px;
    border-top: 1px solid #ddd; /* Approximate for base.$color-grey-light-4 */
}

.s-modal .i-mf-action-wrapper {
    display: flex;
    align-items: center;
    gap: 16px;
    justify-content: flex-end;
}

@media (max-width: 600px) {
    .s-modal .i-mf-action-wrapper {
        width: 100%;
    }
}

.s-modal .i-mfaw-working {
    display: none;
    width: 24px;
    height: 24px;
    padding: 8px;
    background: url('~@cac-public/images/loading_blue.svg') no-repeat center;
    background-size: 24px 24px;
}

.s-modal .i-mfaw-working.t-show {
    display: flex;
}

.s-modal .i-mfaw-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    width: 100%;
}

@media (max-width: 600px) {
    .s-modal .i-mfawa-action.t-primary {
        padding: 0 24px;
    }
}

.t-notification .i-m-content {
    overflow: auto;
}

.notification-modal > .c-nm-date{
    line-height: 1.4;
    font-size: 13px;
    font-style: italic;
    color: #5C6F85;
    display: block;
    margin-bottom: 0.75rem;
}
